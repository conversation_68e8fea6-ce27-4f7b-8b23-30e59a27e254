/**
 ******************************************************************************
 * @file           : auto_follow.h
 * @brief          : 自动跟随功能模块头文件
 * @version        : v1.0
 * @date           : 2025-01-27
 ******************************************************************************
 */

#ifndef __AUTO_FOLLOW_H
#define __AUTO_FOLLOW_H

#ifdef __cplusplus
extern "C" {
#endif

#include "stm32f4xx_hal.h"
#include <stdint.h>
#include <stdbool.h>

// 跟随模式定义
typedef enum {
    FOLLOW_MODE_DISABLED = 0,    // 跟随功能关闭
    FOLLOW_MODE_ULTRASONIC,      // 超声波跟随
    FOLLOW_MODE_CAMERA,          // 摄像头跟随
    FOLLOW_MODE_LIDAR,           // 激光雷达跟随
    FOLLOW_MODE_BLUETOOTH        // 蓝牙信标跟随
} follow_mode_t;

// 跟随状态定义
typedef enum {
    FOLLOW_STATE_IDLE = 0,       // 空闲状态
    FOLLOW_STATE_SEARCHING,      // 搜索目标
    FOLLOW_STATE_TRACKING,       // 跟踪目标
    FOLLOW_STATE_FOLLOWING,      // 跟随目标
    FOLLOW_STATE_LOST,           // 目标丢失
    FOLLOW_STATE_ERROR           // 错误状态
} follow_state_t;

// 目标信息结构体
typedef struct {
    float distance;              // 目标距离 (cm)
    float angle;                 // 目标角度 (度)
    float relative_speed;        // 相对速度 (cm/s)
    uint8_t confidence;          // 置信度 (0-100)
    uint32_t last_update_time;   // 最后更新时间
    uint8_t is_valid;            // 目标是否有效
} target_info_t;

// 跟随参数配置
typedef struct {
    float target_distance;       // 目标跟随距离 (cm)
    float distance_tolerance;    // 距离容差 (cm)
    float angle_tolerance;       // 角度容差 (度)
    float max_follow_speed;      // 最大跟随速度 (cm/s)
    float max_turn_rate;         // 最大转向速率 (度/s)
    uint32_t target_timeout_ms;  // 目标超时时间 (ms)
    uint8_t enable_prediction;   // 是否启用预测
    uint8_t enable_obstacle_avoidance; // 是否启用避障
} follow_config_t;

// 超声波传感器配置
typedef struct {
    GPIO_TypeDef *trig_port;     // 触发引脚端口
    uint16_t trig_pin;           // 触发引脚
    GPIO_TypeDef *echo_port;     // 回响引脚端口
    uint16_t echo_pin;           // 回响引脚
    TIM_HandleTypeDef *timer;    // 定时器句柄
    uint32_t timer_channel;      // 定时器通道
} ultrasonic_config_t;

// 跟随控制器结构体
typedef struct {
    follow_mode_t mode;          // 跟随模式
    follow_state_t state;        // 跟随状态
    follow_config_t config;      // 跟随配置
    target_info_t target;        // 目标信息
    
    // 传感器配置
    ultrasonic_config_t ultrasonic;
    
    // 控制输出
    float target_speed;          // 目标速度
    float target_turn_rate;      // 目标转向速率
    
    // 状态统计
    uint32_t follow_start_time;  // 跟随开始时间
    uint32_t total_follow_time;  // 总跟随时间
    uint32_t target_lost_count;  // 目标丢失次数
    float total_distance;        // 总跟随距离
    
    // 内部状态
    uint32_t last_update_time;   // 最后更新时间
    uint8_t is_initialized;      // 是否已初始化
    uint8_t error_code;          // 错误代码
} auto_follow_t;

// 默认配置参数
#define FOLLOW_DEFAULT_TARGET_DISTANCE    50.0f    // 50cm
#define FOLLOW_DEFAULT_DISTANCE_TOLERANCE 10.0f    // ±10cm
#define FOLLOW_DEFAULT_ANGLE_TOLERANCE    15.0f    // ±15度
#define FOLLOW_DEFAULT_MAX_SPEED          30.0f    // 30cm/s
#define FOLLOW_DEFAULT_MAX_TURN_RATE      45.0f    // 45度/s
#define FOLLOW_DEFAULT_TARGET_TIMEOUT     2000     // 2秒
#define FOLLOW_DEFAULT_UPDATE_INTERVAL    50       // 50ms (20Hz)

// 错误代码定义
#define FOLLOW_ERROR_NONE                 0
#define FOLLOW_ERROR_SENSOR_INIT          1
#define FOLLOW_ERROR_SENSOR_READ          2
#define FOLLOW_ERROR_TARGET_TIMEOUT       3
#define FOLLOW_ERROR_INVALID_CONFIG       4
#define FOLLOW_ERROR_SYSTEM_FAULT         5

// 函数声明
HAL_StatusTypeDef AutoFollow_Init(auto_follow_t *follow, follow_config_t *config);
HAL_StatusTypeDef AutoFollow_SetMode(auto_follow_t *follow, follow_mode_t mode);
HAL_StatusTypeDef AutoFollow_Start(auto_follow_t *follow);
HAL_StatusTypeDef AutoFollow_Stop(auto_follow_t *follow);
HAL_StatusTypeDef AutoFollow_Update(auto_follow_t *follow);
HAL_StatusTypeDef AutoFollow_GetTargetInfo(auto_follow_t *follow, target_info_t *target);
HAL_StatusTypeDef AutoFollow_GetControlOutput(auto_follow_t *follow, float *speed, float *turn_rate);

// 配置函数
void AutoFollow_GetDefaultConfig(follow_config_t *config);
HAL_StatusTypeDef AutoFollow_SetConfig(auto_follow_t *follow, follow_config_t *config);
HAL_StatusTypeDef AutoFollow_ConfigureUltrasonic(auto_follow_t *follow, ultrasonic_config_t *ultrasonic_config);

// 传感器接口函数
HAL_StatusTypeDef AutoFollow_ReadUltrasonic(auto_follow_t *follow, float *distance);
HAL_StatusTypeDef AutoFollow_ProcessCameraData(auto_follow_t *follow, uint8_t *image_data, uint16_t width, uint16_t height);
HAL_StatusTypeDef AutoFollow_ProcessLidarData(auto_follow_t *follow, float *distances, uint16_t point_count);
HAL_StatusTypeDef AutoFollow_ProcessBluetoothSignal(auto_follow_t *follow, int8_t rssi, uint8_t *beacon_data);

// 控制算法函数
HAL_StatusTypeDef AutoFollow_UpdateTargetTracking(auto_follow_t *follow);
HAL_StatusTypeDef AutoFollow_CalculateControlOutput(auto_follow_t *follow);
HAL_StatusTypeDef AutoFollow_PredictTargetPosition(auto_follow_t *follow, float *predicted_distance, float *predicted_angle);

// 状态查询函数
follow_state_t AutoFollow_GetState(auto_follow_t *follow);
uint8_t AutoFollow_IsTargetValid(auto_follow_t *follow);
uint32_t AutoFollow_GetFollowTime(auto_follow_t *follow);
float AutoFollow_GetFollowDistance(auto_follow_t *follow);
uint8_t AutoFollow_GetErrorCode(auto_follow_t *follow);

// 调试和监控函数
void AutoFollow_PrintStatus(auto_follow_t *follow);
HAL_StatusTypeDef AutoFollow_SelfTest(auto_follow_t *follow);
void AutoFollow_Reset(auto_follow_t *follow);

#ifdef __cplusplus
}
#endif

#endif /* __AUTO_FOLLOW_H */
