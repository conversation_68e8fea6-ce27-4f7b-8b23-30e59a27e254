/**
 ******************************************************************************
 * @file           : auto_follow.h
 * @brief          : 自动跟随功能模块头文件
 * @version        : v1.0
 * @date           : 2025-01-27
 ******************************************************************************
 */

#ifndef __AUTO_FOLLOW_H
#define __AUTO_FOLLOW_H

#ifdef __cplusplus
extern "C" {
#endif

#include "stm32f4xx_hal.h"
#include <stdint.h>
#include <stdbool.h>
#include "pid_controller.h"

// PID配置结构 (使用现有的PID控制器结构)
typedef PID_t pid_config_t;

// 运动输出结构
typedef struct {
    float forward_speed;        // 前进速度 (-1.0 到 1.0)
    float turn_speed;           // 转向速度 (-1.0 到 1.0)
} motion_output_t;

// 返回结果类型
typedef enum {
    AUTO_FOLLOW_OK = 0,
    AUTO_FOLLOW_ERROR_NULL_POINTER,
    AUTO_FOLLOW_ERROR_INVALID_STATE,
    AUTO_FOLLOW_ERROR_INVALID_PARAMETER,
    AUTO_FOLLOW_ERROR_HARDWARE_INIT,
    AUTO_FOLLOW_ERROR_MEASUREMENT_FAILED,
    AUTO_FOLLOW_ERROR_TARGET_LOST,
    AUTO_FOLLOW_ERROR_TIMEOUT
} auto_follow_result_t;

// 跟随状态定义
typedef enum {
    AUTO_FOLLOW_STATE_IDLE = 0,      // 空闲状态
    AUTO_FOLLOW_STATE_SEARCHING,     // 搜索目标
    AUTO_FOLLOW_STATE_FOLLOWING,     // 跟随目标
    AUTO_FOLLOW_STATE_LOST,          // 目标丢失
    AUTO_FOLLOW_STATE_EMERGENCY_STOP,// 紧急停止
    AUTO_FOLLOW_STATE_ERROR          // 错误状态
} auto_follow_state_t;

// 跟随模式定义
typedef enum {
    FOLLOW_MODE_DISABLED = 0,    // 跟随功能关闭
    FOLLOW_MODE_ULTRASONIC,      // 超声波跟随
    FOLLOW_MODE_CAMERA,          // 摄像头跟随
    FOLLOW_MODE_LIDAR,           // 激光雷达跟随
    FOLLOW_MODE_BLUETOOTH        // 蓝牙信标跟随
} follow_mode_t;



// 目标信息结构体
typedef struct {
    float distance;              // 目标距离 (cm)
    float angle;                 // 目标角度 (度)
    float relative_speed;        // 相对速度 (cm/s)
    uint8_t confidence;          // 置信度 (0-100)
    uint32_t last_update_time;   // 最后更新时间
    uint8_t is_valid;            // 目标是否有效
} target_info_t;

// 跟随参数配置
typedef struct {
    float target_distance;       // 目标跟随距离 (cm)
    float distance_tolerance;    // 距离容差 (cm)
    float angle_tolerance;       // 角度容差 (度)
    float max_follow_speed;      // 最大跟随速度 (cm/s)
    float max_turn_rate;         // 最大转向速率 (度/s)
    uint32_t target_timeout_ms;  // 目标超时时间 (ms)
    uint8_t enable_prediction;   // 是否启用预测
    uint8_t enable_obstacle_avoidance; // 是否启用避障

    // 距离控制参数
    float min_follow_distance;   // 最小跟随距离
    float max_follow_distance;   // 最大跟随距离
    uint32_t ultrasonic_timeout_us; // 超声波超时时间
    float filter_coefficient;    // 滤波系数
    uint32_t lost_target_timeout_ms; // 失去目标超时时间
    float emergency_stop_distance; // 紧急停止距离

    // PID控制器配置
    pid_config_t distance_pid;   // 距离PID控制器
    float measurement_frequency; // 测量频率
} follow_config_t;

// 超声波传感器配置
typedef struct {
    GPIO_TypeDef *trig_port;     // 触发引脚端口
    uint16_t trig_pin;           // 触发引脚
    GPIO_TypeDef *echo_port;     // 回响引脚端口
    uint16_t echo_pin;           // 回响引脚
    TIM_HandleTypeDef *timer;    // 定时器句柄
    uint32_t timer_channel;      // 定时器通道
} ultrasonic_config_t;



// 跟随控制器结构体
typedef struct {
    follow_mode_t mode;          // 跟随模式
    auto_follow_state_t state;   // 跟随状态
    follow_config_t config;      // 跟随配置
    target_info_t target;        // 目标信息

    // 传感器配置
    ultrasonic_config_t ultrasonic;

    // 控制输出
    float target_speed;          // 目标速度
    float target_turn_rate;      // 目标转向速率
    motion_output_t motion_output; // 运动输出

    // PID控制器
    float distance_pid_integral;    // 距离PID积分项
    float distance_pid_last_error;  // 距离PID上次误差
    float filtered_distance;        // 滤波后的距离

    // 目标检测
    bool target_detected;           // 目标是否检测到
    float current_distance;         // 当前距离
    bool measurement_valid;         // 测量是否有效
    float last_valid_distance;     // 最后有效距离
    uint32_t last_measurement_time; // 最后测量时间
    uint32_t target_lost_time;      // 目标丢失时间

    // 状态统计
    uint32_t follow_start_time;  // 跟随开始时间
    uint32_t total_follow_time;  // 总跟随时间
    uint32_t target_lost_count;  // 目标丢失次数
    float total_distance;        // 总跟随距离

    // 内部状态
    uint32_t last_update_time;   // 最后更新时间
    uint8_t is_initialized;      // 是否已初始化
    uint8_t error_code;          // 错误代码
} auto_follow_t;

// 默认配置参数
#define FOLLOW_DEFAULT_TARGET_DISTANCE    50.0f    // 50cm
#define FOLLOW_DEFAULT_DISTANCE_TOLERANCE 10.0f    // ±10cm
#define FOLLOW_DEFAULT_ANGLE_TOLERANCE    15.0f    // ±15度
#define FOLLOW_DEFAULT_MAX_SPEED          30.0f    // 30cm/s
#define FOLLOW_DEFAULT_MAX_TURN_RATE      45.0f    // 45度/s
#define FOLLOW_DEFAULT_TARGET_TIMEOUT     2000     // 2秒
#define FOLLOW_DEFAULT_UPDATE_INTERVAL    50       // 50ms (20Hz)

// 错误代码定义
#define FOLLOW_ERROR_NONE                 0
#define FOLLOW_ERROR_SENSOR_INIT          1
#define FOLLOW_ERROR_SENSOR_READ          2
#define FOLLOW_ERROR_TARGET_TIMEOUT       3
#define FOLLOW_ERROR_INVALID_CONFIG       4
#define FOLLOW_ERROR_SYSTEM_FAULT         5

// 函数声明
auto_follow_result_t AutoFollow_Init(auto_follow_t *follow, follow_config_t *config);
auto_follow_result_t AutoFollow_SetMode(auto_follow_t *follow, follow_mode_t mode);
auto_follow_result_t AutoFollow_Start(auto_follow_t *follow);
auto_follow_result_t AutoFollow_Stop(auto_follow_t *follow);
auto_follow_result_t AutoFollow_Update(auto_follow_t *follow);
HAL_StatusTypeDef AutoFollow_GetTargetInfo(auto_follow_t *follow, target_info_t *target);
HAL_StatusTypeDef AutoFollow_GetControlOutput(auto_follow_t *follow, float *speed, float *turn_rate);

// 配置函数
auto_follow_result_t AutoFollow_GetDefaultConfig(follow_config_t *config);
HAL_StatusTypeDef AutoFollow_SetConfig(auto_follow_t *follow, follow_config_t *config);
HAL_StatusTypeDef AutoFollow_ConfigureUltrasonic(auto_follow_t *follow, ultrasonic_config_t *ultrasonic_config);

// 传感器接口函数
HAL_StatusTypeDef AutoFollow_ReadUltrasonic(auto_follow_t *follow, float *distance);
HAL_StatusTypeDef AutoFollow_ProcessCameraData(auto_follow_t *follow, uint8_t *image_data, uint16_t width, uint16_t height);
HAL_StatusTypeDef AutoFollow_ProcessLidarData(auto_follow_t *follow, float *distances, uint16_t point_count);
HAL_StatusTypeDef AutoFollow_ProcessBluetoothSignal(auto_follow_t *follow, int8_t rssi, uint8_t *beacon_data);

// 控制算法函数
HAL_StatusTypeDef AutoFollow_UpdateTargetTracking(auto_follow_t *follow);
HAL_StatusTypeDef AutoFollow_CalculateControlOutput(auto_follow_t *follow);
HAL_StatusTypeDef AutoFollow_PredictTargetPosition(auto_follow_t *follow, float *predicted_distance, float *predicted_angle);

// 状态查询函数
auto_follow_state_t AutoFollow_GetState(auto_follow_t *follow);
uint8_t AutoFollow_IsTargetValid(auto_follow_t *follow);
uint32_t AutoFollow_GetFollowTime(auto_follow_t *follow);
float AutoFollow_GetFollowDistance(auto_follow_t *follow);
uint8_t AutoFollow_GetErrorCode(auto_follow_t *follow);

// 调试和监控函数
void AutoFollow_PrintStatus(auto_follow_t *follow);
HAL_StatusTypeDef AutoFollow_SelfTest(auto_follow_t *follow);
void AutoFollow_Reset(auto_follow_t *follow);

#ifdef __cplusplus
}
#endif

#endif /* __AUTO_FOLLOW_H */
