/**
 ******************************************************************************
 * @file           : balance_car_advanced.h
 * @brief          : 平衡车高级功能模块头文件
 * @version        : v1.0
 * @date           : 2025-01-27
 ******************************************************************************
 * @description    :
 * 平衡车高级功能模块，包含：
 * - 自适应控制算法
 * - 路径跟踪功能
 * - 遥控功能扩展
 * - 智能避障
 * - 数据记录和分析
 ******************************************************************************
 */

#ifndef __BALANCE_CAR_ADVANCED_H
#define __BALANCE_CAR_ADVANCED_H

#include "stm32f4xx_hal.h"
#include "balance_car_system.h"

/**
 * @brief 路径点结构
 */
typedef struct {
    float x;                        // X坐标 (cm)
    float y;                        // Y坐标 (cm)
    float target_speed;             // 目标速度 (cm/s)
    float target_heading;           // 目标航向 (°)
    uint32_t dwell_time_ms;         // 停留时间 (ms)
} path_point_t;

/**
 * @brief 路径跟踪状态
 */
typedef enum {
    PATH_STATE_IDLE = 0,            // 空闲
    PATH_STATE_FOLLOWING,           // 跟踪中
    PATH_STATE_REACHED,             // 到达目标
    PATH_STATE_ERROR                // 错误
} path_state_t;

/**
 * @brief 自适应控制模式
 */
typedef enum {
    ADAPTIVE_MODE_OFF = 0,          // 关闭
    ADAPTIVE_MODE_BASIC,            // 基础自适应
    ADAPTIVE_MODE_ADVANCED,         // 高级自适应
    ADAPTIVE_MODE_LEARNING          // 学习模式
} adaptive_mode_t;

/**
 * @brief 遥控命令类型
 */
typedef enum {
    REMOTE_CMD_STOP = 0,            // 停止
    REMOTE_CMD_FORWARD,             // 前进
    REMOTE_CMD_BACKWARD,            // 后退
    REMOTE_CMD_LEFT,                // 左转
    REMOTE_CMD_RIGHT,               // 右转
    REMOTE_CMD_SPIN_LEFT,           // 原地左转
    REMOTE_CMD_SPIN_RIGHT,          // 原地右转
    REMOTE_CMD_FOLLOW_PATH,         // 路径跟踪
    REMOTE_CMD_EMERGENCY_STOP       // 紧急停止
} remote_command_type_t;

/**
 * @brief 遥控命令结构
 */
typedef struct {
    remote_command_type_t type;     // 命令类型
    float parameter1;               // 参数1 (速度/角度等)
    float parameter2;               // 参数2 (时间/距离等)
    uint32_t timestamp;             // 时间戳
    uint8_t priority;               // 优先级 (0-255)
} remote_command_t;

/**
 * @brief 路径跟踪器
 */
typedef struct {
    path_point_t *path_points;      // 路径点数组
    uint16_t path_length;           // 路径长度
    uint16_t current_point_index;   // 当前目标点索引
    path_state_t state;             // 跟踪状态
    
    // 当前位置估计
    float current_x;                // 当前X坐标
    float current_y;                // 当前Y坐标
    float current_heading;          // 当前航向
    
    // 控制参数
    float lookahead_distance;       // 前瞻距离
    float position_tolerance;       // 位置容差
    float heading_tolerance;        // 航向容差
    float max_lateral_error;        // 最大横向误差
    
    // 状态信息
    float distance_to_target;       // 到目标距离
    float lateral_error;            // 横向误差
    float heading_error;            // 航向误差
    uint32_t point_start_time;      // 当前点开始时间
} path_tracker_t;

/**
 * @brief 自适应控制器
 */
typedef struct {
    adaptive_mode_t mode;           // 自适应模式
    
    // 环境参数估计
    float surface_friction;         // 地面摩擦系数
    float load_estimation;          // 负载估计
    float battery_voltage;          // 电池电压
    float temperature;              // 环境温度
    
    // 性能指标
    float stability_index;          // 稳定性指数
    float response_index;           // 响应性指数
    float energy_efficiency;        // 能效指数
    
    // 自适应参数
    float adaptive_kp_factor;       // 自适应Kp系数
    float adaptive_kd_factor;       // 自适应Kd系数
    float adaptive_speed_factor;    // 自适应速度系数
    
    // 学习数据
    float learning_rate;            // 学习率
    uint32_t learning_samples;      // 学习样本数
    float performance_history[100]; // 性能历史
    uint8_t history_index;          // 历史索引
} adaptive_controller_t;

/**
 * @brief 遥控接收器
 */
typedef struct {
    uint8_t enabled;                // 启用标志
    uint32_t last_command_time;     // 上次命令时间
    uint32_t timeout_ms;            // 超时时间
    
    // 命令队列
    remote_command_t command_queue[10]; // 命令队列
    uint8_t queue_head;             // 队列头
    uint8_t queue_tail;             // 队列尾
    uint8_t queue_count;            // 队列计数
    
    // 统计信息
    uint32_t commands_received;     // 接收命令数
    uint32_t commands_executed;     // 执行命令数
    uint32_t commands_dropped;      // 丢弃命令数
} remote_receiver_t;

/**
 * @brief 高级功能管理器
 */
typedef struct {
    // 子模块
    path_tracker_t path_tracker;
    adaptive_controller_t adaptive_controller;
    remote_receiver_t remote_receiver;
    
    // 系统引用
    balance_car_system_t *system;
    
    // 功能开关
    uint8_t path_tracking_enabled;
    uint8_t adaptive_control_enabled;
    uint8_t remote_control_enabled;
    
    // 状态信息
    uint32_t update_counter;
    uint32_t last_update_time;
} advanced_features_t;

/**
 * @brief 初始化高级功能模块
 * @param advanced 高级功能指针
 * @param system 平衡车系统指针
 * @return HAL状态
 */
HAL_StatusTypeDef AdvancedFeatures_Init(advanced_features_t *advanced, balance_car_system_t *system);

/**
 * @brief 更新高级功能 (在主循环中调用)
 * @param advanced 高级功能指针
 * @return HAL状态
 */
HAL_StatusTypeDef AdvancedFeatures_Update(advanced_features_t *advanced);

// ========== 路径跟踪功能 ==========

/**
 * @brief 设置路径
 * @param advanced 高级功能指针
 * @param path_points 路径点数组
 * @param path_length 路径长度
 * @return HAL状态
 */
HAL_StatusTypeDef PathTracker_SetPath(advanced_features_t *advanced, 
                                     path_point_t *path_points, 
                                     uint16_t path_length);

/**
 * @brief 开始路径跟踪
 * @param advanced 高级功能指针
 * @return HAL状态
 */
HAL_StatusTypeDef PathTracker_Start(advanced_features_t *advanced);

/**
 * @brief 停止路径跟踪
 * @param advanced 高级功能指针
 * @return HAL状态
 */
HAL_StatusTypeDef PathTracker_Stop(advanced_features_t *advanced);

/**
 * @brief 获取路径跟踪状态
 * @param advanced 高级功能指针
 * @param progress 进度百分比 (0-100)
 * @param distance_remaining 剩余距离
 * @return HAL状态
 */
HAL_StatusTypeDef PathTracker_GetStatus(advanced_features_t *advanced,
                                       uint8_t *progress,
                                       float *distance_remaining);

// ========== 自适应控制功能 ==========

/**
 * @brief 启用自适应控制
 * @param advanced 高级功能指针
 * @param mode 自适应模式
 * @return HAL状态
 */
HAL_StatusTypeDef AdaptiveControl_Enable(advanced_features_t *advanced, adaptive_mode_t mode);

/**
 * @brief 禁用自适应控制
 * @param advanced 高级功能指针
 * @return HAL状态
 */
HAL_StatusTypeDef AdaptiveControl_Disable(advanced_features_t *advanced);

/**
 * @brief 获取自适应参数
 * @param advanced 高级功能指针
 * @param kp_factor Kp系数输出
 * @param kd_factor Kd系数输出
 * @param speed_factor 速度系数输出
 * @return HAL状态
 */
HAL_StatusTypeDef AdaptiveControl_GetParameters(advanced_features_t *advanced,
                                               float *kp_factor,
                                               float *kd_factor,
                                               float *speed_factor);

// ========== 遥控功能 ==========

/**
 * @brief 启用遥控功能
 * @param advanced 高级功能指针
 * @param timeout_ms 超时时间
 * @return HAL状态
 */
HAL_StatusTypeDef RemoteControl_Enable(advanced_features_t *advanced, uint32_t timeout_ms);

/**
 * @brief 禁用遥控功能
 * @param advanced 高级功能指针
 * @return HAL状态
 */
HAL_StatusTypeDef RemoteControl_Disable(advanced_features_t *advanced);

/**
 * @brief 发送遥控命令
 * @param advanced 高级功能指针
 * @param command 遥控命令
 * @return HAL状态
 */
HAL_StatusTypeDef RemoteControl_SendCommand(advanced_features_t *advanced, remote_command_t *command);

/**
 * @brief 获取遥控状态
 * @param advanced 高级功能指针
 * @param connected 连接状态
 * @param last_command_age_ms 上次命令时间间隔
 * @return HAL状态
 */
HAL_StatusTypeDef RemoteControl_GetStatus(advanced_features_t *advanced,
                                         uint8_t *connected,
                                         uint32_t *last_command_age_ms);

#endif /* __BALANCE_CAR_ADVANCED_H */
