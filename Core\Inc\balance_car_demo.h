/**
 ******************************************************************************
 * @file           : balance_car_demo.h
 * @brief          : 平衡车完整演示应用头文件
 * @version        : v1.0
 * @date           : 2025-01-27
 ******************************************************************************
 * @description    :
 * 平衡车完整演示应用，展示所有功能的集成使用：
 * - 基础平衡控制
 * - 摇杆运动控制
 * - 参数自动调优
 * - 高级功能 (路径跟踪、自适应控制、遥控)
 * - 用户界面和状态显示
 ******************************************************************************
 */

#ifndef __BALANCE_CAR_DEMO_H
#define __BALANCE_CAR_DEMO_H

#include "stm32f4xx_hal.h"
#include "balance_car_system.h"
#include "balance_car_tuning.h"
#include "balance_car_advanced.h"

/**
 * @brief 演示模式
 */
typedef enum {
    DEMO_MODE_BASIC_BALANCE = 0,    // 基础平衡演示
    DEMO_MODE_JOYSTICK_CONTROL,     // 摇杆控制演示
    DEMO_MODE_AUTO_TUNING,          // 自动调优演示
    DEMO_MODE_PATH_TRACKING,        // 路径跟踪演示
    DEMO_MODE_ADAPTIVE_CONTROL,     // 自适应控制演示
    DEMO_MODE_REMOTE_CONTROL,       // 遥控演示
    DEMO_MODE_COMPREHENSIVE         // 综合演示
} demo_mode_t;

/**
 * @brief 演示状态
 */
typedef enum {
    DEMO_STATE_INIT = 0,            // 初始化
    DEMO_STATE_MENU,                // 菜单选择
    DEMO_STATE_RUNNING,             // 运行中
    DEMO_STATE_PAUSED,              // 暂停
    DEMO_STATE_COMPLETED,           // 完成
    DEMO_STATE_ERROR                // 错误
} demo_state_t;

/**
 * @brief 用户界面状态
 */
typedef struct {
    uint8_t display_enabled;        // 显示启用
    uint32_t last_display_update;   // 上次显示更新时间
    uint32_t display_interval_ms;   // 显示更新间隔
    
    // 显示内容选择
    uint8_t show_basic_info;        // 显示基础信息
    uint8_t show_sensor_data;       // 显示传感器数据
    uint8_t show_control_data;      // 显示控制数据
    uint8_t show_advanced_info;     // 显示高级信息
    uint8_t show_debug_info;        // 显示调试信息
} ui_state_t;

/**
 * @brief 演示配置
 */
typedef struct {
    demo_mode_t mode;               // 演示模式
    uint32_t demo_duration_ms;      // 演示持续时间
    uint8_t auto_switch_modes;      // 自动切换模式
    uint8_t enable_safety_limits;   // 启用安全限制
    uint8_t enable_data_logging;    // 启用数据记录

    // 系统配置
    system_config_t system_config;  // 系统配置

    // 路径跟踪配置
    path_point_t demo_path[10];     // 演示路径
    uint8_t demo_path_length;       // 演示路径长度

    // 调优配置
    tuning_config_t tuning_config;  // 调优配置

    // 用户界面配置
    ui_state_t ui_config;           // 用户界面配置
} demo_config_t;

/**
 * @brief 演示应用主结构
 */
typedef struct {
    // 核心系统
    balance_car_system_t system;
    balance_car_tuner_t tuner;
    advanced_features_t advanced;
    
    // 演示控制
    demo_config_t config;
    demo_state_t state;
    demo_mode_t current_mode;
    uint32_t mode_start_time;
    uint32_t demo_start_time;
    
    // 统计信息
    uint32_t update_counter;
    uint32_t error_counter;
    uint32_t mode_switch_counter;
    
    // 数据记录
    uint8_t data_logging_enabled;
    uint32_t log_sample_count;
    uint32_t last_log_time;
} balance_car_demo_t;

/**
 * @brief 初始化演示应用
 * @param demo 演示应用指针
 * @param config 演示配置
 * @return HAL状态
 */
HAL_StatusTypeDef BalanceCarDemo_Init(balance_car_demo_t *demo, demo_config_t *config);

/**
 * @brief 启动演示
 * @param demo 演示应用指针
 * @param mode 演示模式
 * @return HAL状态
 */
HAL_StatusTypeDef BalanceCarDemo_Start(balance_car_demo_t *demo, demo_mode_t mode);

/**
 * @brief 停止演示
 * @param demo 演示应用指针
 * @return HAL状态
 */
HAL_StatusTypeDef BalanceCarDemo_Stop(balance_car_demo_t *demo);

/**
 * @brief 演示应用主循环更新
 * @param demo 演示应用指针
 * @return HAL状态
 */
HAL_StatusTypeDef BalanceCarDemo_Update(balance_car_demo_t *demo);

/**
 * @brief 切换演示模式
 * @param demo 演示应用指针
 * @param mode 新的演示模式
 * @return HAL状态
 */
HAL_StatusTypeDef BalanceCarDemo_SwitchMode(balance_car_demo_t *demo, demo_mode_t mode);

/**
 * @brief 暂停/恢复演示
 * @param demo 演示应用指针
 * @param pause 暂停标志
 * @return HAL状态
 */
HAL_StatusTypeDef BalanceCarDemo_Pause(balance_car_demo_t *demo, uint8_t pause);

/**
 * @brief 处理用户输入
 * @param demo 演示应用指针
 * @param input 用户输入
 * @return HAL状态
 */
HAL_StatusTypeDef BalanceCarDemo_HandleInput(balance_car_demo_t *demo, uint8_t input);

/**
 * @brief 更新用户界面显示
 * @param demo 演示应用指针
 * @return HAL状态
 */
HAL_StatusTypeDef BalanceCarDemo_UpdateDisplay(balance_car_demo_t *demo);

/**
 * @brief 生成演示报告
 * @param demo 演示应用指针
 * @param report_str 报告字符串缓冲区
 * @param max_len 缓冲区最大长度
 */
void BalanceCarDemo_GenerateReport(balance_car_demo_t *demo, char *report_str, uint16_t max_len);

/**
 * @brief 获取默认演示配置
 * @param config 配置输出
 */
void BalanceCarDemo_GetDefaultConfig(demo_config_t *config);

/**
 * @brief 创建演示路径
 * @param path_points 路径点数组
 * @param max_points 最大点数
 * @param path_type 路径类型 (0=直线, 1=方形, 2=圆形, 3=8字形)
 * @return 实际路径点数
 */
uint8_t BalanceCarDemo_CreateDemoPath(path_point_t *path_points, uint8_t max_points, uint8_t path_type);

// 演示模式名称
extern const char* demo_mode_names[];

// 用户输入定义
#define USER_INPUT_MODE_NEXT        '1'
#define USER_INPUT_MODE_PREV        '2'
#define USER_INPUT_PAUSE_RESUME     '3'
#define USER_INPUT_STOP             '4'
#define USER_INPUT_EMERGENCY        '5'
#define USER_INPUT_DISPLAY_TOGGLE   '6'
#define USER_INPUT_TUNING_START     '7'
#define USER_INPUT_RESET            '8'

// 默认配置值
#define DEMO_DEFAULT_DURATION_MS    60000   // 60秒
#define DEMO_DEFAULT_DISPLAY_INTERVAL_MS 1000 // 1秒显示更新间隔

#endif /* __BALANCE_CAR_DEMO_H */
