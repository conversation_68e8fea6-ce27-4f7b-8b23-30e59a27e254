/**
 ******************************************************************************
 * @file           : balance_car_main.h
 * @brief          : 平衡车主控制系统头文件 (简化版)
 * @version        : v2.0
 * @date           : 2025-01-27
 ******************************************************************************
 * @description    :
 * 简化的平衡车主控制系统，移除演示应用的复杂性
 * 专注于核心功能的稳定运行，支持自动跟随和手机APP控制
 ******************************************************************************
 */

#ifndef __BALANCE_CAR_MAIN_H
#define __BALANCE_CAR_MAIN_H

#ifdef __cplusplus
extern "C" {
#endif

#include "stm32f4xx_hal.h"
#include <stdint.h>
#include <stdbool.h>

// 引入核心模块
#include "mpu6050.h"
#include "balance_controller.h"
#include "motion_controller.h"
#include "joystick_driver.h"
#include "auto_follow.h"
#include "mobile_app.h"

// 返回结果类型
typedef enum {
    BALANCE_CAR_OK = 0,
    BALANCE_CAR_ERROR_NULL_POINTER,
    BALANCE_CAR_ERROR_INVALID_STATE,
    BALANCE_CAR_ERROR_INVALID_PARAMETER,
    BALANCE_CAR_ERROR_HARDWARE_INIT,
    BALANCE_CAR_ERROR_SENSOR_INIT,
    BALANCE_CAR_ERROR_MOTOR_INIT,
    BALANCE_CAR_ERROR_COMMUNICATION,
    BALANCE_CAR_ERROR_ANGLE_LIMIT,
    BALANCE_CAR_ERROR_TILT_LIMIT,
    BALANCE_CAR_ERROR_TIMEOUT
} balance_car_result_t;

// 系统运行模式
typedef enum {
    BALANCE_CAR_MODE_INIT = 0,       // 初始化模式
    BALANCE_CAR_MODE_STANDBY,        // 待机模式
    BALANCE_CAR_MODE_BALANCE_ONLY,   // 纯平衡模式
    BALANCE_CAR_MODE_MANUAL,         // 手动控制模式
    BALANCE_CAR_MODE_AUTO_FOLLOW,    // 自动跟随模式
    BALANCE_CAR_MODE_APP_CONTROL,    // APP控制模式
    BALANCE_CAR_MODE_EMERGENCY       // 紧急停止模式
} balance_car_mode_t;

// 系统状态
typedef enum {
    BALANCE_CAR_STATE_INIT = 0,      // 初始化状态
    BALANCE_CAR_STATE_IDLE,          // 空闲状态
    BALANCE_CAR_STATE_READY,         // 准备状态
    BALANCE_CAR_STATE_RUNNING,       // 运行状态
    BALANCE_CAR_STATE_ERROR,         // 错误状态
    BALANCE_CAR_STATE_EMERGENCY      // 紧急状态
} balance_car_state_t;

// 输入源类型
typedef enum {
    INPUT_SOURCE_NONE = 0,           // 无输入
    INPUT_SOURCE_JOYSTICK,           // 摇杆输入
    INPUT_SOURCE_AUTO_FOLLOW,        // 自动跟随
    INPUT_SOURCE_MOBILE_APP,         // 手机APP
    INPUT_SOURCE_EMERGENCY           // 紧急停止
} input_source_t;

// 系统配置参数
typedef struct {
    // 控制参数
    float max_tilt_angle;            // 最大倾斜角度
    float max_speed;                 // 最大速度
    float max_turn_rate;             // 最大转向速率
    uint16_t control_frequency;      // 控制频率
    uint16_t display_frequency;      // 显示频率

    // PID控制器配置
    PID_t balance_pid;    // 平衡PID控制器
    PID_t velocity_pid;   // 速度PID控制器
    PID_t turn_pid;       // 转向PID控制器

    // 物理参数
    float max_velocity;              // 最大速度
    float wheel_diameter;            // 轮子直径
    float wheel_base;                // 轮距
    float robot_height;              // 机器人高度

    // 安全参数
    uint32_t watchdog_timeout_ms;    // 看门狗超时
    uint32_t input_timeout_ms;       // 输入超时
    float emergency_angle_limit;     // 紧急停止角度限制
    float emergency_stop_angle;      // 紧急停止角度

    // 功能开关
    uint8_t enable_auto_follow;      // 启用自动跟随
    uint8_t enable_mobile_app;       // 启用手机APP
    uint8_t enable_data_logging;     // 启用数据记录
    uint8_t enable_auto_recovery;    // 启用自动恢复

    // 显示参数
    uint8_t display_mode;            // 显示模式
    uint16_t display_update_ms;      // 显示更新间隔
} balance_car_config_t;
// 系统状态信息
typedef struct {
    balance_car_mode_t mode;         // 当前模式
    balance_car_state_t state;       // 当前状态
    input_source_t input_source;     // 当前输入源

    // 传感器数据
    float current_angle;             // 当前角度
    float angular_velocity;          // 角速度
    float battery_voltage;           // 电池电压
    float temperature;               // 温度

    // 控制输出
    float motor_left_output;         // 左电机输出
    float motor_right_output;        // 右电机输出

    // 运行统计
    uint32_t uptime_seconds;         // 运行时间
    uint32_t total_distance_cm;      // 总行驶距离
    uint32_t error_count;            // 错误次数
    uint8_t error_flags;             // 错误标志

    // 性能指标
    float balance_stability_score;   // 平衡稳定性评分
    float control_response_time;     // 控制响应时间
    uint16_t control_frequency_hz;   // 控制频率
} balance_car_status_t;

// 主控制系统结构体
typedef struct {
    // 核心模块
    balance_controller_t balance_ctrl;
    motion_controller_t motion_ctrl;
    joystick_driver_t joystick;
    auto_follow_t auto_follow;
    mobile_app_t mobile_app;

    // 系统配置和状态
    balance_car_config_t config;
    balance_car_status_t status;

    // 控制变量
    float target_speed;              // 目标速度
    float target_turn_rate;          // 目标转向速率
    uint32_t last_input_time;        // 最后输入时间
    uint32_t mode_switch_time;       // 模式切换时间

    // 安全监控
    uint8_t safety_check_enabled;    // 安全检查启用
    uint32_t last_safety_check;      // 最后安全检查时间
    uint32_t emergency_stop_time;    // 紧急停止时间

    // 内部状态
    uint8_t is_initialized;          // 是否已初始化
    uint32_t system_start_time;      // 系统启动时间
    uint32_t last_update_time;       // 最后更新时间

    // 缺少的字段
    balance_car_state_t state;       // 系统状态
    balance_car_mode_t mode;         // 运行模式
    uint32_t joystick_raw[2];        // 摇杆原始数据
    mpu6050_data_t sensor_data;      // 传感器数据
    uint32_t error_code;             // 错误代码

    // 摇杆数据结构
    struct {
        float x_axis;                // X轴值 (-1.0 到 1.0)
        float y_axis;                // Y轴值 (-1.0 到 1.0)
        bool button_pressed;         // 按键状态
    } joystick_data;
} balance_car_main_t;

// 默认配置参数
#define BALANCE_CAR_DEFAULT_MAX_TILT_ANGLE    15.0f    // 15度
#define BALANCE_CAR_DEFAULT_MAX_SPEED         50.0f    // 50cm/s
#define BALANCE_CAR_DEFAULT_MAX_TURN_RATE     90.0f    // 90度/s
#define BALANCE_CAR_DEFAULT_WATCHDOG_TIMEOUT  1000     // 1秒
#define BALANCE_CAR_DEFAULT_INPUT_TIMEOUT     500      // 500ms
#define BALANCE_CAR_DEFAULT_EMERGENCY_ANGLE   25.0f    // 25度
#define BALANCE_CAR_DEFAULT_DISPLAY_UPDATE    100      // 100ms
#define BALANCE_CAR_DEFAULT_CONTROL_FREQ      200      // 200Hz

// 错误标志位定义
#define BALANCE_CAR_ERROR_SENSOR_FAULT        (1 << 0)
#define BALANCE_CAR_ERROR_MOTOR_FAULT         (1 << 1)
#define BALANCE_CAR_ERROR_COMMUNICATION       (1 << 2)
#define BALANCE_CAR_ERROR_BATTERY_LOW         (1 << 3)
#define BALANCE_CAR_ERROR_ANGLE_LIMIT         (1 << 4)
#define BALANCE_CAR_ERROR_TIMEOUT             (1 << 5)
#define BALANCE_CAR_ERROR_SYSTEM_FAULT        (1 << 6)
#define BALANCE_CAR_ERROR_EMERGENCY_STOP      (1 << 7)

// 主要函数声明
balance_car_result_t BalanceCarMain_Init(balance_car_main_t *car);
balance_car_result_t BalanceCarMain_Start(balance_car_main_t *car);
balance_car_result_t BalanceCarMain_Stop(balance_car_main_t *car);
balance_car_result_t BalanceCarMain_Update(balance_car_main_t *car);
balance_car_result_t BalanceCarMain_EmergencyStop(balance_car_main_t *car);

// 模式控制函数
balance_car_result_t BalanceCarMain_SetMode(balance_car_main_t *car, balance_car_mode_t mode);
balance_car_mode_t BalanceCarMain_GetMode(balance_car_main_t *car);

// 配置函数
balance_car_result_t BalanceCarMain_GetDefaultConfig(balance_car_config_t *config);
balance_car_result_t BalanceCarMain_SetConfig(balance_car_main_t *car, const balance_car_config_t *config);

// 状态查询函数
balance_car_status_t* BalanceCarMain_GetStatus(balance_car_main_t *car);
uint8_t BalanceCarMain_IsRunning(balance_car_main_t *car);
uint8_t BalanceCarMain_HasError(balance_car_main_t *car);

// 控制输入函数
HAL_StatusTypeDef BalanceCarMain_SetManualControl(balance_car_main_t *car, float speed, float turn_rate);



// 调试函数
void BalanceCarMain_PrintStatus(balance_car_main_t *car);
HAL_StatusTypeDef BalanceCarMain_SelfTest(balance_car_main_t *car);

// 传统接口保持兼容性
void BalanceCar_Main(void);
void Error_Handler(void);

#ifdef __cplusplus
}
#endif

#endif /* __BALANCE_CAR_MAIN_H */
