/**
 ******************************************************************************
 * @file           : balance_car_system.h
 * @brief          : 平衡车综合控制系统头文件
 * @version        : v1.0
 * @date           : 2025-01-27
 ******************************************************************************
 * @description    :
 * 平衡车综合控制系统，整合：
 * - 摇杆输入处理
 * - 运动控制算法
 * - 平衡控制算法
 * - 传感器数据融合
 * - 安全保护机制
 * 
 * 系统架构：
 * 摇杆输入 → 运动控制器 → 平衡控制器 → 电机输出
 *     ↓           ↓            ↓
 * 指令解析 → 目标角度计算 → PID控制 → PWM输出
 ******************************************************************************
 */

#ifndef __BALANCE_CAR_SYSTEM_H
#define __BALANCE_CAR_SYSTEM_H

#include "stm32f4xx_hal.h"
#include "joystick_driver.h"
#include "motion_controller.h"
#include "balance_controller.h"
#include "mpu6050.h"

/**
 * @brief 系统运行模式
 */
typedef enum {
    SYSTEM_MODE_INIT = 0,           // 初始化模式
    SYSTEM_MODE_CALIBRATION,        // 校准模式
    SYSTEM_MODE_STANDBY,            // 待机模式
    SYSTEM_MODE_BALANCE_ONLY,       // 纯平衡模式
    SYSTEM_MODE_MANUAL_CONTROL,     // 手动控制模式
    SYSTEM_MODE_AUTO_CONTROL,       // 自动控制模式
    SYSTEM_MODE_EMERGENCY,          // 紧急模式
    SYSTEM_MODE_ERROR               // 错误模式
} system_mode_t;

/**
 * @brief 系统状态
 */
typedef enum {
    SYSTEM_STATE_STOPPED = 0,       // 停止状态
    SYSTEM_STATE_STARTING,          // 启动中
    SYSTEM_STATE_RUNNING,           // 运行中
    SYSTEM_STATE_STOPPING,          // 停止中
    SYSTEM_STATE_ERROR,             // 错误状态
    SYSTEM_STATE_CALIBRATING        // 校准中
} system_state_t;

/**
 * @brief 系统配置
 */
typedef struct {
    // 硬件配置
    ADC_HandleTypeDef *hadc;        // ADC句柄
    GPIO_TypeDef *joystick_btn_port; // 摇杆按键端口
    uint16_t joystick_btn_pin;      // 摇杆按键引脚
    
    // 控制参数
    float control_frequency;        // 控制频率 (Hz)
    float sensor_frequency;         // 传感器频率 (Hz)
    uint8_t enable_adaptive_pid;    // 启用自适应PID
    uint8_t enable_safety_check;    // 启用安全检查
    
    // 摇杆配置
    joystick_config_t joystick_cfg;
    
    // 运动控制配置
    motion_config_t motion_cfg;
    
    // 电机接口
    motor_interface_t motor_interface;
} system_config_t;

/**
 * @brief 系统状态信息
 */
typedef struct {
    system_mode_t mode;             // 当前模式
    system_mode_t system_mode;      // 系统模式（兼容性）
    system_state_t state;           // 当前状态
    uint32_t uptime_ms;             // 运行时间
    uint32_t last_update_time;      // 上次更新时间
    
    // 传感器状态
    uint8_t sensor_ok;              // 传感器状态
    float current_angle;            // 当前角度
    float current_angular_velocity; // 当前角速度
    float temperature;              // 传感器温度
    
    // 控制状态
    uint8_t balance_active;         // 平衡控制激活
    uint8_t motion_active;          // 运动控制激活
    float target_tilt;              // 目标倾角
    float turn_rate;                // 转向速率
    
    // 摇杆状态
    uint8_t joystick_connected;     // 摇杆连接状态
    int16_t joystick_x;             // 摇杆X轴
    int16_t joystick_y;             // 摇杆Y轴
    uint8_t joystick_button;        // 摇杆按键
    
    // 电机状态
    int16_t left_motor_output;      // 左电机输出
    int16_t right_motor_output;     // 右电机输出
    
    // 错误信息
    uint32_t error_code;            // 错误代码
    char error_message[64];         // 错误消息
} system_status_t;

/**
 * @brief 平衡车系统主结构
 */
typedef struct {
    // 配置
    system_config_t config;
    
    // 状态
    system_status_t status;
    
    // 子系统
    joystick_data_t joystick_data;
    motion_controller_t motion_controller;
    balance_controller_t balance_controller;
    mpu6050_data_t sensor_data;
    
    // 控制变量
    motion_command_t motion_command;
    uint32_t control_loop_counter;
    uint32_t sensor_loop_counter;
    
    // 时间管理
    uint32_t last_control_time;
    uint32_t last_sensor_time;
    uint32_t system_start_time;
    
    // 安全监控
    uint32_t watchdog_counter;
    uint8_t emergency_stop_flag;
    
    // 调试信息
    uint8_t debug_enabled;
    uint32_t debug_print_interval;
    uint32_t last_debug_time;
} balance_car_system_t;

/**
 * @brief 初始化平衡车系统
 * @param system 系统指针
 * @param config 配置参数
 * @return HAL状态
 */
HAL_StatusTypeDef BalanceCarSystem_Init(balance_car_system_t *system, system_config_t *config);

/**
 * @brief 系统主循环更新
 * @param system 系统指针
 * @return HAL状态
 */
HAL_StatusTypeDef BalanceCarSystem_Update(balance_car_system_t *system);

/**
 * @brief 设置系统模式
 * @param system 系统指针
 * @param mode 目标模式
 * @return HAL状态
 */
HAL_StatusTypeDef BalanceCarSystem_SetMode(balance_car_system_t *system, system_mode_t mode);

/**
 * @brief 紧急停止
 * @param system 系统指针
 * @return HAL状态
 */
HAL_StatusTypeDef BalanceCarSystem_EmergencyStop(balance_car_system_t *system);

/**
 * @brief 系统复位
 * @param system 系统指针
 * @return HAL状态
 */
HAL_StatusTypeDef BalanceCarSystem_Reset(balance_car_system_t *system);

/**
 * @brief 获取系统状态
 * @param system 系统指针
 * @param status 状态输出
 * @return HAL状态
 */
HAL_StatusTypeDef BalanceCarSystem_GetStatus(balance_car_system_t *system, system_status_t *status);

/**
 * @brief 系统自检
 * @param system 系统指针
 * @return 1=自检通过, 0=自检失败
 */
uint8_t BalanceCarSystem_SelfTest(balance_car_system_t *system);

/**
 * @brief 启动校准程序
 * @param system 系统指针
 * @return HAL状态
 */
HAL_StatusTypeDef BalanceCarSystem_StartCalibration(balance_car_system_t *system);

/**
 * @brief 完成校准程序
 * @param system 系统指针
 * @return HAL状态
 */
HAL_StatusTypeDef BalanceCarSystem_FinishCalibration(balance_car_system_t *system);

/**
 * @brief 启用/禁用调试输出
 * @param system 系统指针
 * @param enable 启用标志
 * @param print_interval_ms 打印间隔(ms)
 */
void BalanceCarSystem_EnableDebug(balance_car_system_t *system, uint8_t enable, uint32_t print_interval_ms);

/**
 * @brief 获取系统统计信息
 * @param system 系统指针
 * @param info_str 信息字符串缓冲区
 * @param max_len 缓冲区最大长度
 */
void BalanceCarSystem_GetStatistics(balance_car_system_t *system, char *info_str, uint16_t max_len);

// 错误代码定义
#define SYSTEM_ERROR_NONE               0x00000000
#define SYSTEM_ERROR_SENSOR_FAIL        0x00000001
#define SYSTEM_ERROR_JOYSTICK_FAIL      0x00000002
#define SYSTEM_ERROR_MOTOR_FAIL         0x00000004
#define SYSTEM_ERROR_ANGLE_LIMIT        0x00000008
#define SYSTEM_ERROR_COMMUNICATION      0x00000010
#define SYSTEM_ERROR_CALIBRATION        0x00000020
#define SYSTEM_ERROR_TIMEOUT            0x00000040
#define SYSTEM_ERROR_EMERGENCY_STOP     0x00000080

// 默认配置宏
#define SYSTEM_DEFAULT_CONTROL_FREQ     200.0f      // 200Hz控制频率
#define SYSTEM_DEFAULT_SENSOR_FREQ      1000.0f     // 1000Hz传感器频率
#define SYSTEM_DEFAULT_DEBUG_INTERVAL   1000        // 1秒调试输出间隔

#endif /* __BALANCE_CAR_SYSTEM_H */
