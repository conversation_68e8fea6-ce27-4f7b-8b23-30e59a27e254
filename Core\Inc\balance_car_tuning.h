/**
 ******************************************************************************
 * @file           : balance_car_tuning.h
 * @brief          : 平衡车参数调优和测试工具头文件
 * @version        : v1.0
 * @date           : 2025-01-27
 ******************************************************************************
 * @description    :
 * 平衡车参数调优和测试工具，包含：
 * - PID参数自动调优
 * - 运动响应测试
 * - 安全保护测试
 * - 性能评估和数据记录
 * - 参数保存和加载
 ******************************************************************************
 */

#ifndef __BALANCE_CAR_TUNING_H
#define __BALANCE_CAR_TUNING_H

#include "stm32f4xx_hal.h"
#include "balance_car_system.h"

/**
 * @brief 调优模式
 */
typedef enum {
    TUNING_MODE_MANUAL = 0,         // 手动调优
    TUNING_MODE_AUTO_BALANCE,       // 自动平衡调优
    TUNING_MODE_AUTO_MOTION,        // 自动运动调优
    TUNING_MODE_SAFETY_TEST,        // 安全测试
    TUNING_MODE_PERFORMANCE_TEST    // 性能测试
} tuning_mode_t;

/**
 * @brief 调优状态
 */
typedef enum {
    TUNING_STATE_IDLE = 0,          // 空闲
    TUNING_STATE_RUNNING,           // 运行中
    TUNING_STATE_COMPLETED,         // 完成
    TUNING_STATE_FAILED,            // 失败
    TUNING_STATE_ABORTED            // 中止
} tuning_state_t;

/**
 * @brief PID参数集
 */
typedef struct {
    // 角度环PID
    float angle_kp;
    float angle_ki;
    float angle_kd;
    
    // 速度环PID
    float speed_kp;
    float speed_ki;
    float speed_kd;
    
    // 转向环PID
    float turn_kp;
    float turn_ki;
    float turn_kd;
    
    // 评估指标
    float stability_score;          // 稳定性评分 (0-100)
    float response_score;           // 响应性评分 (0-100)
    float overall_score;            // 综合评分 (0-100)
} pid_parameter_set_t;

/**
 * @brief 运动参数集
 */
typedef struct {
    float max_tilt_angle;           // 最大倾斜角度
    float max_turn_rate;            // 最大转向速率
    float acceleration_limit;       // 加速度限制
    float deceleration_limit;       // 减速度限制
    float response_speed;           // 响应速度
    
    // 评估指标
    float smoothness_score;         // 平滑性评分
    float accuracy_score;           // 精确性评分
    float overall_score;            // 综合评分
} motion_parameter_set_t;

/**
 * @brief 测试结果
 */
typedef struct {
    uint32_t test_duration_ms;      // 测试持续时间
    uint32_t stable_time_ms;        // 稳定时间
    float max_angle_deviation;      // 最大角度偏差
    float avg_angle_deviation;      // 平均角度偏差
    float max_oscillation;          // 最大振荡幅度
    float settling_time_ms;         // 稳定时间
    uint32_t fall_count;            // 跌倒次数
    uint8_t safety_violations;      // 安全违规次数
} test_result_t;

/**
 * @brief 调优配置
 */
typedef struct {
    tuning_mode_t mode;             // 调优模式
    uint32_t test_duration_ms;      // 测试持续时间
    uint32_t max_iterations;        // 最大迭代次数
    float target_score;             // 目标评分
    uint8_t enable_auto_save;       // 自动保存最佳参数
    uint8_t enable_safety_limits;   // 启用安全限制
    
    // 参数搜索范围
    pid_parameter_set_t pid_min;    // PID参数最小值
    pid_parameter_set_t pid_max;    // PID参数最大值
    motion_parameter_set_t motion_min; // 运动参数最小值
    motion_parameter_set_t motion_max; // 运动参数最大值
} tuning_config_t;

/**
 * @brief 调优器主结构
 */
typedef struct {
    tuning_config_t config;         // 配置
    tuning_state_t state;           // 状态
    
    // 当前参数
    pid_parameter_set_t current_pid;
    motion_parameter_set_t current_motion;
    
    // 最佳参数
    pid_parameter_set_t best_pid;
    motion_parameter_set_t best_motion;
    float best_score;
    
    // 测试数据
    test_result_t current_result;
    test_result_t best_result;
    
    // 迭代控制
    uint32_t current_iteration;
    uint32_t test_start_time;
    
    // 数据记录
    float angle_history[1000];      // 角度历史数据
    uint16_t history_index;
    uint16_t history_count;
    
    // 系统引用
    balance_car_system_t *system;
} balance_car_tuner_t;

/**
 * @brief 初始化调优器
 * @param tuner 调优器指针
 * @param system 平衡车系统指针
 * @param config 调优配置
 * @return HAL状态
 */
HAL_StatusTypeDef BalanceCarTuner_Init(balance_car_tuner_t *tuner, 
                                      balance_car_system_t *system,
                                      tuning_config_t *config);

/**
 * @brief 开始调优
 * @param tuner 调优器指针
 * @param mode 调优模式
 * @return HAL状态
 */
HAL_StatusTypeDef BalanceCarTuner_Start(balance_car_tuner_t *tuner, tuning_mode_t mode);

/**
 * @brief 停止调优
 * @param tuner 调优器指针
 * @return HAL状态
 */
HAL_StatusTypeDef BalanceCarTuner_Stop(balance_car_tuner_t *tuner);

/**
 * @brief 调优器更新 (在主循环中调用)
 * @param tuner 调优器指针
 * @return HAL状态
 */
HAL_StatusTypeDef BalanceCarTuner_Update(balance_car_tuner_t *tuner);

/**
 * @brief 手动设置PID参数
 * @param tuner 调优器指针
 * @param pid_params PID参数
 * @return HAL状态
 */
HAL_StatusTypeDef BalanceCarTuner_SetPIDParams(balance_car_tuner_t *tuner, 
                                              pid_parameter_set_t *pid_params);

/**
 * @brief 手动设置运动参数
 * @param tuner 调优器指针
 * @param motion_params 运动参数
 * @return HAL状态
 */
HAL_StatusTypeDef BalanceCarTuner_SetMotionParams(balance_car_tuner_t *tuner,
                                                 motion_parameter_set_t *motion_params);

/**
 * @brief 执行单次测试
 * @param tuner 调优器指针
 * @param duration_ms 测试持续时间
 * @param result 测试结果输出
 * @return HAL状态
 */
HAL_StatusTypeDef BalanceCarTuner_RunSingleTest(balance_car_tuner_t *tuner,
                                               uint32_t duration_ms,
                                               test_result_t *result);

/**
 * @brief 保存最佳参数
 * @param tuner 调优器指针
 * @return HAL状态
 */
HAL_StatusTypeDef BalanceCarTuner_SaveBestParams(balance_car_tuner_t *tuner);

/**
 * @brief 加载保存的参数
 * @param tuner 调优器指针
 * @return HAL状态
 */
HAL_StatusTypeDef BalanceCarTuner_LoadSavedParams(balance_car_tuner_t *tuner);

/**
 * @brief 获取调优进度
 * @param tuner 调优器指针
 * @param progress 进度百分比 (0-100)
 * @param eta_seconds 预计剩余时间(秒)
 * @return HAL状态
 */
HAL_StatusTypeDef BalanceCarTuner_GetProgress(balance_car_tuner_t *tuner,
                                             uint8_t *progress,
                                             uint32_t *eta_seconds);

/**
 * @brief 生成调优报告
 * @param tuner 调优器指针
 * @param report_str 报告字符串缓冲区
 * @param max_len 缓冲区最大长度
 */
void BalanceCarTuner_GenerateReport(balance_car_tuner_t *tuner,
                                   char *report_str,
                                   uint16_t max_len);

/**
 * @brief 获取推荐的初始参数
 * @param pid_params PID参数输出
 * @param motion_params 运动参数输出
 */
void BalanceCarTuner_GetRecommendedParams(pid_parameter_set_t *pid_params,
                                         motion_parameter_set_t *motion_params);

// 默认调优配置
#define TUNING_DEFAULT_DURATION_MS      30000   // 30秒测试
#define TUNING_DEFAULT_MAX_ITERATIONS   50      // 最大50次迭代
#define TUNING_DEFAULT_TARGET_SCORE     85.0f   // 目标85分

// 推荐的PID参数范围
#define ANGLE_KP_MIN    50.0f
#define ANGLE_KP_MAX    150.0f
#define ANGLE_KD_MIN    1.0f
#define ANGLE_KD_MAX    10.0f
#define SPEED_KP_MIN    0.5f
#define SPEED_KP_MAX    2.0f
#define SPEED_KI_MIN    0.05f
#define SPEED_KI_MAX    0.3f

#endif /* __BALANCE_CAR_TUNING_H */
