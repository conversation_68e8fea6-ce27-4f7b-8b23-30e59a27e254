/**
 ******************************************************************************
 * @file           : balance_controller.h
 * @brief          : 平衡车控制器头文件
 * @version        : v1.0
 * @date           : 2025-01-27
 ******************************************************************************
 * @description    :
 * 平衡车三环PID控制系统：
 * 1. 角度环：保持车身平衡，输入俯仰角，输出基础电机速度
 * 2. 速度环：控制前进后退，输入目标速度，输出角度修正
 * 3. 转向环：控制左右转向，输入转向指令，输出左右差速
 * 
 * 控制流程：
 * 传感器数据 → 角度环PID → 基础电机输出
 *              ↑
 * 目标速度 → 速度环PID → 角度修正
 * 转向指令 → 转向环PID → 左右差速
 ******************************************************************************
 */

#ifndef __BALANCE_CONTROLLER_H
#define __BALANCE_CONTROLLER_H

#include "stm32f4xx_hal.h"
#include "pid_controller.h"
#include "mpu6050.h"

/**
 * @brief 电机控制接口
 */
typedef struct {
    void (*set_motor_speed)(int16_t left, int16_t right);   // 设置电机速度
    void (*enable_motors)(uint8_t enable);                  // 启用/禁用电机
    void (*get_motor_speed)(int16_t *left, int16_t *right); // 获取当前电机速度(可选)
} motor_interface_t;

/**
 * @brief 平衡车状态枚举
 */
typedef enum {
    BALANCE_STATE_DISABLED = 0,     // 禁用状态
    BALANCE_STATE_STANDBY,          // 待机状态(等待平衡)
    BALANCE_STATE_BALANCING,        // 平衡状态
    BALANCE_STATE_FALLEN            // 倾倒状态
} balance_state_t;

/**
 * @brief 平衡车控制器
 */
typedef struct {
    // 三环PID控制器
    PID_t angle_pid;                // 角度环PID
    PID_t speed_pid;                // 速度环PID  
    PID_t turn_pid;                 // 转向环PID
    
    // 控制目标
    float target_angle;             // 目标平衡角度 (°)
    float target_speed;             // 目标速度 (cm/s)
    float target_turn_rate;         // 目标转向速率 (°/s)
    
    // 当前状态
    balance_state_t state;          // 控制器状态
    float current_speed;            // 当前速度估计 (cm/s)
    uint32_t balance_start_time;    // 平衡开始时间
    
    // 电机接口
    motor_interface_t motors;
    
    // 安全参数
    float max_angle;                // 最大允许角度 (°)
    float min_balance_time_ms;      // 最小平衡时间 (ms)
    
    // 调试信息
    int16_t last_left_motor;        // 上次左电机输出
    int16_t last_right_motor;       // 上次右电机输出
} balance_controller_t;

/**
 * @brief 初始化平衡控制器
 * @param controller 控制器指针
 * @param motors 电机接口
 */
void Balance_Init(balance_controller_t *controller, motor_interface_t motors);

/**
 * @brief 更新平衡控制(主控制循环)
 * @param controller 控制器指针
 * @param sensor_data 传感器数据
 * @param dt 时间间隔(秒)
 */
void Balance_Update(balance_controller_t *controller, mpu6050_data_t *sensor_data, float dt);

/**
 * @brief 设置控制目标
 * @param controller 控制器指针
 * @param angle 目标角度 (°)
 * @param speed 目标速度 (cm/s)
 * @param turn_rate 转向速率 (°/s)
 */
void Balance_SetTarget(balance_controller_t *controller, float angle, float speed, float turn_rate);

/**
 * @brief 启用/禁用平衡控制
 * @param controller 控制器指针
 * @param enable 启用标志
 */
void Balance_Enable(balance_controller_t *controller, uint8_t enable);

/**
 * @brief 获取控制器状态
 * @param controller 控制器指针
 * @return 当前状态
 */
balance_state_t Balance_GetState(balance_controller_t *controller);

/**
 * @brief 设置PID参数
 * @param controller 控制器指针
 * @param angle_kp 角度环P参数
 * @param angle_kd 角度环D参数
 * @param speed_kp 速度环P参数
 * @param speed_ki 速度环I参数
 */
void Balance_SetPIDParams(balance_controller_t *controller, 
                         float angle_kp, float angle_kd,
                         float speed_kp, float speed_ki);

/**
 * @brief 获取调试信息
 * @param controller 控制器指针
 * @param left_motor 左电机输出
 * @param right_motor 右电机输出
 * @param angle_output 角度环输出
 * @param speed_output 速度环输出
 */
void Balance_GetDebugInfo(balance_controller_t *controller,
                         int16_t *left_motor, int16_t *right_motor,
                         float *angle_output, float *speed_output);

#endif /* __BALANCE_CONTROLLER_H */