/**
 * @file    i2c_dma_driver.h
 * @brief   I2C DMA驱动头文件
 * <AUTHOR> Team
 * @date    2025-01-28
 * @version 1.3.0
 * 
 * @note    提供高效的I2C DMA通信接口，优化传感器数据读取
 */

#ifndef __I2C_DMA_DRIVER_H
#define __I2C_DMA_DRIVER_H

#ifdef __cplusplus
extern "C" {
#endif

#include "app_types.h"
#include "error_handler.h"
#include "stm32f4xx_hal.h"

/* ========================= I2C DMA配置 ========================= */

#define I2C_DMA_BUFFER_SIZE         32      ///< DMA缓冲区大小
#define I2C_DMA_TIMEOUT_MS          100     ///< DMA超时时间 (ms)
#define I2C_DMA_MAX_RETRY           3       ///< 最大重试次数

/* ========================= I2C DMA状态 ========================= */

/**
 * @brief I2C DMA状态枚举
 */
typedef enum {
    I2C_DMA_STATE_IDLE = 0,                ///< 空闲状态
    I2C_DMA_STATE_TX_BUSY,                 ///< 发送忙
    I2C_DMA_STATE_RX_BUSY,                 ///< 接收忙
    I2C_DMA_STATE_ERROR                    ///< 错误状态
} i2c_dma_state_t;

/**
 * @brief I2C DMA传输完成回调函数类型
 */
typedef void (*i2c_dma_complete_callback_t)(app_error_t status, void* user_data);

/**
 * @brief I2C DMA驱动结构体
 */
typedef struct {
    I2C_HandleTypeDef* i2c_handle;         ///< I2C句柄
    i2c_dma_state_t state;                 ///< 当前状态
    uint8_t tx_buffer[I2C_DMA_BUFFER_SIZE]; ///< 发送缓冲区
    uint8_t rx_buffer[I2C_DMA_BUFFER_SIZE]; ///< 接收缓冲区
    uint16_t tx_size;                      ///< 发送大小
    uint16_t rx_size;                      ///< 接收大小
    uint8_t device_addr;                   ///< 设备地址
    uint8_t reg_addr;                      ///< 寄存器地址
    uint8_t retry_count;                   ///< 重试计数
    uint32_t timeout;                      ///< 超时时间
    uint32_t start_time;                   ///< 开始时间
    i2c_dma_complete_callback_t callback;  ///< 完成回调
    void* user_data;                       ///< 用户数据
    bool is_initialized;                   ///< 是否已初始化
    uint32_t successful_transfers;         ///< 成功传输次数
    uint32_t failed_transfers;             ///< 失败传输次数
    uint32_t last_transfer_time;           ///< 最后传输时间
} i2c_dma_driver_t;

/* ========================= 函数声明 ========================= */

/**
 * @brief  初始化I2C DMA驱动
 * @param  driver: I2C DMA驱动指针
 * @param  i2c_handle: I2C句柄指针
 * @retval app_error_t: 错误码
 */
app_error_t i2c_dma_init(i2c_dma_driver_t* driver, I2C_HandleTypeDef* i2c_handle);

/**
 * @brief  使用DMA读取I2C数据
 * @param  driver: I2C DMA驱动指针
 * @param  device_addr: 设备地址
 * @param  reg_addr: 寄存器地址
 * @param  data: 数据缓冲区
 * @param  size: 数据大小
 * @param  callback: 完成回调函数
 * @param  user_data: 用户数据
 * @retval app_error_t: 错误码
 */
app_error_t i2c_dma_read(i2c_dma_driver_t* driver,
                         uint8_t device_addr,
                         uint8_t reg_addr,
                         uint8_t* data,
                         uint16_t size,
                         i2c_dma_complete_callback_t callback,
                         void* user_data);

/**
 * @brief  使用DMA写入I2C数据
 * @param  driver: I2C DMA驱动指针
 * @param  device_addr: 设备地址
 * @param  reg_addr: 寄存器地址
 * @param  data: 数据缓冲区
 * @param  size: 数据大小
 * @param  callback: 完成回调函数
 * @param  user_data: 用户数据
 * @retval app_error_t: 错误码
 */
app_error_t i2c_dma_write(i2c_dma_driver_t* driver,
                          uint8_t device_addr,
                          uint8_t reg_addr,
                          uint8_t* data,
                          uint16_t size,
                          i2c_dma_complete_callback_t callback,
                          void* user_data);

/**
 * @brief  中止I2C DMA传输
 * @param  driver: I2C DMA驱动指针
 * @retval app_error_t: 错误码
 */
app_error_t i2c_dma_abort(i2c_dma_driver_t* driver);

/**
 * @brief  处理I2C DMA超时
 * @param  driver: I2C DMA驱动指针
 * @retval app_error_t: 错误码
 */
app_error_t i2c_dma_process_timeout(i2c_dma_driver_t* driver);

/**
 * @brief  获取I2C DMA状态
 * @param  driver: I2C DMA驱动指针
 * @retval i2c_dma_state_t: DMA状态
 */
i2c_dma_state_t i2c_dma_get_state(i2c_dma_driver_t* driver);

/**
 * @brief  I2C DMA传输完成回调
 * @param  hi2c: I2C句柄指针
 * @note   在stm32f4xx_hal_i2c.c的HAL_I2C_MemRxCpltCallback中调用
 */
void i2c_dma_rx_cplt_callback(I2C_HandleTypeDef* hi2c);

/**
 * @brief  I2C DMA传输错误回调
 * @param  hi2c: I2C句柄指针
 * @note   在stm32f4xx_hal_i2c.c的HAL_I2C_ErrorCallback中调用
 */
void i2c_dma_error_callback(I2C_HandleTypeDef* hi2c);

/**
 * @brief  重置I2C DMA驱动
 * @param  driver: I2C DMA驱动指针
 * @retval app_error_t: 错误码
 */
app_error_t i2c_dma_reset(i2c_dma_driver_t* driver);

/**
 * @brief  获取I2C DMA统计信息
 * @param  driver: I2C DMA驱动指针
 * @param  successful: 成功传输次数指针
 * @param  failed: 失败传输次数指针
 * @retval app_error_t: 错误码
 */
app_error_t i2c_dma_get_statistics(i2c_dma_driver_t* driver,
                                  uint32_t* successful,
                                  uint32_t* failed);

#ifdef __cplusplus
}
#endif

#endif /* __I2C_DMA_DRIVER_H */