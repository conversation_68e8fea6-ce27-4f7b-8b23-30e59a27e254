/**
 ******************************************************************************
 * @file           : joystick_driver.h
 * @brief          : 十字摇杆驱动头文件
 * @version        : v1.0
 * @date           : 2025-01-27
 ******************************************************************************
 * @description    :
 * 十字摇杆(模拟摇杆)驱动模块，支持：
 * - 双轴模拟输入 (X轴/Y轴)
 * - 按键输入检测
 * - 死区处理和滤波
 * - 多种输出格式
 * - 校准功能
 * 
 * 硬件连接：
 * - X轴 → ADC1_CH0 (PA0)  // 左右轴
 * - Y轴 → ADC1_CH1 (PA1)  // 前后轴  
 * - SW  → GPIO (PA2)      // 按键输入
 * - VCC → 3.3V
 * - GND → GND
 * 
 * 坐标系定义：
 * - X轴：左(-) ← 中(0) → 右(+)
 * - Y轴：后(-) ← 中(0) → 前(+)
 ******************************************************************************
 */

#ifndef __JOYSTICK_DRIVER_H
#define __JOYSTICK_DRIVER_H

#include "stm32f4xx_hal.h"

/**
 * @brief 摇杆输入数据结构
 */
typedef struct {
    int16_t x_axis;          // X轴值 (-1000 ~ +1000)
    int16_t y_axis;          // Y轴值 (-1000 ~ +1000)
    uint8_t button_pressed;  // 按键状态 (1=按下, 0=释放)
    uint8_t button_clicked;  // 按键点击事件 (1=新点击, 0=无事件)
    
    // 原始ADC值 (调试用)
    uint16_t raw_x;          // X轴原始ADC值 (0-4095)
    uint16_t raw_y;          // Y轴原始ADC值 (0-4095)
    
    // 状态信息
    uint8_t is_valid;        // 数据有效标志
    uint32_t timestamp;      // 时间戳
} joystick_data_t;

/**
 * @brief 摇杆校准数据结构
 */
typedef struct {
    uint16_t x_center;       // X轴中心值
    uint16_t y_center;       // Y轴中心值
    uint16_t x_min;          // X轴最小值
    uint16_t x_max;          // X轴最大值
    uint16_t y_min;          // Y轴最小值
    uint16_t y_max;          // Y轴最大值
    uint8_t is_calibrated;   // 校准完成标志
} joystick_calibration_t;

/**
 * @brief 摇杆配置结构
 */
typedef struct {
    ADC_HandleTypeDef *hadc;     // ADC句柄
    uint32_t x_channel;          // X轴ADC通道
    uint32_t y_channel;          // Y轴ADC通道
    
    GPIO_TypeDef *button_port;   // 按键GPIO端口
    uint16_t button_pin;         // 按键GPIO引脚
    
    uint16_t deadzone;           // 死区大小 (0-500)
    uint8_t filter_strength;     // 滤波强度 (0-10)
    uint8_t invert_x;            // X轴反向 (1=反向, 0=正向)
    uint8_t invert_y;            // Y轴反向 (1=反向, 0=正向)
} joystick_config_t;

/**
 * @brief 摇杆运动方向枚举
 */
typedef enum {
    JOYSTICK_DIR_CENTER = 0,    // 中心位置
    JOYSTICK_DIR_UP,            // 向上
    JOYSTICK_DIR_DOWN,          // 向下
    JOYSTICK_DIR_LEFT,          // 向左
    JOYSTICK_DIR_RIGHT,         // 向右
    JOYSTICK_DIR_UP_LEFT,       // 左上
    JOYSTICK_DIR_UP_RIGHT,      // 右上
    JOYSTICK_DIR_DOWN_LEFT,     // 左下
    JOYSTICK_DIR_DOWN_RIGHT     // 右下
} joystick_direction_t;

/**
 * @brief 摇杆驱动器结构体
 */
typedef struct {
    joystick_config_t config;        // 摇杆配置
    joystick_calibration_t calibration; // 校准数据
    joystick_data_t current_data;    // 当前数据
    uint8_t is_initialized;          // 初始化标志
    uint32_t last_update_time;       // 最后更新时间
} joystick_driver_t;

// 常用配置宏定义
#define JOYSTICK_DEADZONE_DEFAULT    50     // 默认死区
#define JOYSTICK_FILTER_DEFAULT      3      // 默认滤波强度
#define JOYSTICK_ADC_RESOLUTION      4096   // 12位ADC分辨率
#define JOYSTICK_OUTPUT_RANGE        1000   // 输出范围 ±1000

/**
 * @brief 初始化摇杆驱动
 * @param config 摇杆配置
 * @return HAL状态
 */
HAL_StatusTypeDef Joystick_Init(joystick_config_t *config);

/**
 * @brief 读取摇杆数据
 * @param data 摇杆数据指针
 * @return HAL状态
 */
HAL_StatusTypeDef Joystick_ReadData(joystick_data_t *data);

/**
 * @brief 获取摇杆方向
 * @param data 摇杆数据
 * @return 方向枚举
 */
joystick_direction_t Joystick_GetDirection(joystick_data_t *data);

/**
 * @brief 开始摇杆校准
 * @return HAL状态
 */
HAL_StatusTypeDef Joystick_StartCalibration(void);

/**
 * @brief 完成摇杆校准
 * @return HAL状态
 */
HAL_StatusTypeDef Joystick_FinishCalibration(void);

/**
 * @brief 加载校准数据
 * @param calibration 校准数据
 * @return HAL状态
 */
HAL_StatusTypeDef Joystick_LoadCalibration(joystick_calibration_t *calibration);

/**
 * @brief 保存校准数据
 * @param calibration 校准数据指针
 * @return HAL状态
 */
HAL_StatusTypeDef Joystick_SaveCalibration(joystick_calibration_t *calibration);

/**
 * @brief 设置死区大小
 * @param deadzone 死区大小 (0-500)
 */
void Joystick_SetDeadzone(uint16_t deadzone);

/**
 * @brief 设置滤波强度
 * @param strength 滤波强度 (0-10)
 */
void Joystick_SetFilterStrength(uint8_t strength);

/**
 * @brief 设置轴反向
 * @param invert_x X轴反向标志
 * @param invert_y Y轴反向标志
 */
void Joystick_SetAxisInvert(uint8_t invert_x, uint8_t invert_y);

/**
 * @brief 摇杆自检
 * @return 1=自检通过, 0=自检失败
 */
uint8_t Joystick_SelfTest(void);

/**
 * @brief 获取摇杆状态信息
 * @param info_str 状态信息字符串缓冲区
 * @param max_len 缓冲区最大长度
 */
void Joystick_GetStatusInfo(char *info_str, uint16_t max_len);

/**
 * @brief 重置摇杆到默认状态
 */
void Joystick_Reset(void);

// 平衡车专用接口函数

/**
 * @brief 获取前后运动指令 (平衡车专用)
 * @param data 摇杆数据
 * @return 前后运动值 (-1000 ~ +1000)
 */
int16_t Joystick_GetForwardCommand(joystick_data_t *data);

/**
 * @brief 获取转向指令 (平衡车专用)
 * @param data 摇杆数据
 * @return 转向值 (-1000 ~ +1000)
 */
int16_t Joystick_GetTurnCommand(joystick_data_t *data);

/**
 * @brief 获取运动强度 (平衡车专用)
 * @param data 摇杆数据
 * @return 运动强度 (0 ~ 1000)
 */
uint16_t Joystick_GetMovementIntensity(joystick_data_t *data);

/**
 * @brief 检查是否在死区内 (平衡车专用)
 * @param data 摇杆数据
 * @return 1=在死区内, 0=不在死区内
 */
uint8_t Joystick_IsInDeadzone(joystick_data_t *data);

#endif /* __JOYSTICK_DRIVER_H */
