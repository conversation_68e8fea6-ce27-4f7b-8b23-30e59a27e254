/**
 * @file    kalman_config.h
 * @brief   卡尔曼滤波器配置参数定义
 * <AUTHOR> Team
 * @date    2025-01-27
 * @version 1.1.0
 * 
 * @note    卡尔曼滤波器的噪声参数和配置定义
 */

#ifndef __KALMAN_CONFIG_H
#define __KALMAN_CONFIG_H

#ifdef __cplusplus
extern "C" {
#endif

/* ========================= 默认滤波参数 ========================= */
// X轴（横滚角）滤波器参数
#define KALMAN_X_Q_ANGLE        0.001f      ///< X轴角度过程噪声方差
#define KALMAN_X_Q_BIAS         0.003f      ///< X轴偏差过程噪声方差
#define KALMAN_X_R_MEASURE      0.03f       ///< X轴测量噪声方差

// Y轴（俯仰角）滤波器参数
#define KALMAN_Y_Q_ANGLE        0.001f      ///< Y轴角度过程噪声方差
#define KALMAN_Y_Q_BIAS         0.003f      ///< Y轴偏差过程噪声方差
#define KALMAN_Y_R_MEASURE      0.03f       ///< Y轴测量噪声方差

/* ========================= 滤波器配置选项 ========================= */
#define KALMAN_ENABLE_X_AXIS    1           ///< 启用X轴滤波
#define KALMAN_ENABLE_Y_AXIS    1           ///< 启用Y轴滤波
#define KALMAN_ENABLE_Z_AXIS    0           ///< 启用Z轴滤波（暂未实现）

/* ========================= 角度限制 ========================= */
#define KALMAN_ANGLE_MIN        -180.0f     ///< 最小角度值 (°)
#define KALMAN_ANGLE_MAX        180.0f      ///< 最大角度值 (°)
#define KALMAN_PITCH_LIMIT      90.0f       ///< 俯仰角限制 (°)
#define KALMAN_ROLL_LIMIT       180.0f      ///< 横滚角限制 (°)

/* ========================= 滤波器状态 ========================= */
#define KALMAN_STATE_UNINITIALIZED  0      ///< 未初始化状态
#define KALMAN_STATE_INITIALIZING    1      ///< 初始化中状态
#define KALMAN_STATE_RUNNING         2      ///< 正常运行状态
#define KALMAN_STATE_ERROR           3      ///< 错误状态

/* ========================= 时间配置 ========================= */
#define KALMAN_MIN_DT           0.001f      ///< 最小时间间隔 (s)
#define KALMAN_MAX_DT           0.1f        ///< 最大时间间隔 (s)
#define KALMAN_DEFAULT_DT       0.01f       ///< 默认时间间隔 (s)

/* ========================= 自适应参数 ========================= */
#define KALMAN_ADAPTIVE_ENABLE  0           ///< 启用自适应滤波
#define KALMAN_ADAPT_THRESHOLD  0.1f        ///< 自适应阈值
#define KALMAN_ADAPT_FACTOR     1.1f        ///< 自适应调整因子

/* ========================= 性能优化配置 ========================= */
#define KALMAN_USE_FAST_MATH    1           ///< 使用快速数学运算
#define KALMAN_MATRIX_SIZE      2           ///< 协方差矩阵大小
#define KALMAN_STATE_SIZE       2           ///< 状态向量大小

/* ========================= 调试配置 ========================= */
#define KALMAN_DEBUG_ENABLE     0           ///< 启用调试输出
#define KALMAN_DEBUG_PRINT_P    0           ///< 打印协方差矩阵
#define KALMAN_DEBUG_PRINT_K    0           ///< 打印卡尔曼增益

/* ========================= 预设配置模式 ========================= */
// 高精度模式（低噪声，响应慢）
#define KALMAN_MODE_HIGH_PRECISION  1
#if KALMAN_MODE_HIGH_PRECISION
    #undef KALMAN_X_Q_ANGLE
    #undef KALMAN_X_R_MEASURE
    #undef KALMAN_Y_Q_ANGLE
    #undef KALMAN_Y_R_MEASURE
    #define KALMAN_X_Q_ANGLE    0.0005f
    #define KALMAN_X_R_MEASURE  0.05f
    #define KALMAN_Y_Q_ANGLE    0.0005f
    #define KALMAN_Y_R_MEASURE  0.05f
#endif

// 快速响应模式（高噪声，响应快）
#define KALMAN_MODE_FAST_RESPONSE   0
#if KALMAN_MODE_FAST_RESPONSE
    #undef KALMAN_X_Q_ANGLE
    #undef KALMAN_X_R_MEASURE
    #undef KALMAN_Y_Q_ANGLE
    #undef KALMAN_Y_R_MEASURE
    #define KALMAN_X_Q_ANGLE    0.005f
    #define KALMAN_X_R_MEASURE  0.01f
    #define KALMAN_Y_Q_ANGLE    0.005f
    #define KALMAN_Y_R_MEASURE  0.01f
#endif

#ifdef __cplusplus
}
#endif

#endif /* __KALMAN_CONFIG_H */