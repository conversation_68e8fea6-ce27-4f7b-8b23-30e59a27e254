/**
 ******************************************************************************
 * @file           : mobile_app.h
 * @brief          : 手机APP接口模块头文件
 * @version        : v1.0
 * @date           : 2025-01-27
 ******************************************************************************
 */

#ifndef __MOBILE_APP_H
#define __MOBILE_APP_H

#ifdef __cplusplus
extern "C" {
#endif

#include "stm32f4xx_hal.h"
#include <stdint.h>
#include <stdbool.h>

// 结果类型定义
typedef enum {
    MOBILE_APP_OK = 0,
    MOBILE_APP_ERROR_NULL_POINTER,
    MOBILE_APP_ERROR_INVALID_PARAMETER,
    MOBILE_APP_ERROR_COMMUNICATION,
    MOBILE_APP_ERROR_TIMEOUT,
    MOBILE_APP_ERROR_BUFFER_FULL,
    MOBILE_APP_ERROR_CHECKSUM,
    MOBILE_APP_ERROR_UNKNOWN_COMMAND
} mobile_app_result_t;

// 通信协议定义
#define APP_PROTOCOL_VERSION          0x01
#define APP_FRAME_HEADER              0xAA55
#define APP_FRAME_TAIL                0x55AA
#define APP_MAX_PAYLOAD_SIZE          128
#define APP_MAX_FRAME_SIZE            (APP_MAX_PAYLOAD_SIZE + 8)

// 命令类型定义
typedef enum {
    APP_CMD_PING = 0x01,             // 心跳包
    APP_CMD_GET_STATUS = 0x02,       // 获取状态
    APP_CMD_SET_MODE = 0x03,         // 设置模式
    APP_CMD_CONTROL = 0x04,          // 控制命令
    APP_CMD_CONFIG = 0x05,           // 配置参数
    APP_CMD_CALIBRATE = 0x06,        // 校准命令
    APP_CMD_EMERGENCY_STOP = 0x07,   // 紧急停止
    APP_CMD_DATA_STREAM = 0x08,      // 数据流控制
    APP_CMD_FILE_TRANSFER = 0x09,    // 文件传输
    APP_CMD_FIRMWARE_UPDATE = 0x0A   // 固件更新
} app_command_t;

// 系统模式定义
typedef enum {
    APP_MODE_STANDBY = 0,            // 待机模式
    APP_MODE_BALANCE = 1,            // 平衡模式
    APP_MODE_MANUAL = 2,             // 手动控制
    APP_MODE_AUTO_FOLLOW = 3,        // 自动跟随
    APP_MODE_PATH_TRACKING = 4,      // 路径跟踪
    APP_MODE_REMOTE_CONTROL = 5,     // 遥控模式
    APP_MODE_AUTO_TUNING = 6         // 自动调优
} app_system_mode_t;

// 控制命令结构体
typedef struct {
    int16_t forward_speed;           // 前进速度 (-1000 to 1000)
    int16_t turn_rate;               // 转向速率 (-1000 to 1000)
    uint8_t brake;                   // 刹车 (0/1)
    uint8_t boost;                   // 加速 (0/1)
    uint32_t timestamp;              // 时间戳
} app_control_cmd_t;

// 系统状态结构体
typedef struct {
    app_system_mode_t mode;          // 当前模式
    float battery_voltage;           // 电池电压
    float current_angle;             // 当前角度
    float angular_velocity;          // 角速度
    int16_t motor_left_output;       // 左电机输出
    int16_t motor_right_output;      // 右电机输出
    float temperature;               // 温度
    uint8_t error_flags;             // 错误标志
    uint32_t uptime_seconds;         // 运行时间
} app_system_status_t;

// 配置参数结构体
typedef struct {
    float pid_angle_kp;              // 角度PID Kp
    float pid_angle_ki;              // 角度PID Ki
    float pid_angle_kd;              // 角度PID Kd
    float pid_speed_kp;              // 速度PID Kp
    float pid_speed_ki;              // 速度PID Ki
    float pid_speed_kd;              // 速度PID Kd
    float max_tilt_angle;            // 最大倾斜角
    float max_speed;                 // 最大速度
    uint8_t enable_auto_follow;      // 启用自动跟随
    uint8_t enable_data_logging;     // 启用数据记录
} app_config_params_t;

// 数据流配置
typedef struct {
    uint8_t enable_sensor_data;      // 传感器数据流
    uint8_t enable_control_data;     // 控制数据流
    uint8_t enable_status_data;      // 状态数据流
    uint16_t stream_interval_ms;     // 数据流间隔
    uint8_t stream_format;           // 数据格式 (0=JSON, 1=Binary)
} app_data_stream_config_t;

// 通信帧结构体
typedef struct {
    uint16_t header;                 // 帧头
    uint8_t version;                 // 协议版本
    uint8_t command;                 // 命令类型
    uint16_t length;                 // 数据长度
    uint8_t payload[APP_MAX_PAYLOAD_SIZE]; // 数据载荷
    uint8_t checksum;                // 校验和
    uint16_t tail;                   // 帧尾
} app_frame_t;

// APP接口结构体
typedef struct {
    UART_HandleTypeDef *uart;        // UART句柄
    
    // 接收缓冲区
    uint8_t rx_buffer[APP_MAX_FRAME_SIZE];
    uint16_t rx_index;
    uint8_t rx_state;
    
    // 发送缓冲区
    uint8_t tx_buffer[APP_MAX_FRAME_SIZE];
    
    // 通信状态
    uint8_t is_connected;            // 连接状态
    uint32_t last_ping_time;         // 最后心跳时间
    uint32_t connection_timeout_ms;  // 连接超时时间
    
    // 数据流控制
    app_data_stream_config_t stream_config;
    uint32_t last_stream_time;
    
    // 统计信息
    uint32_t frames_received;        // 接收帧数
    uint32_t frames_sent;            // 发送帧数
    uint32_t frames_error;           // 错误帧数
    uint32_t bytes_received;         // 接收字节数
    uint32_t bytes_sent;             // 发送字节数
    
    // 回调函数
    void (*on_control_command)(app_control_cmd_t *cmd);
    void (*on_mode_change)(app_system_mode_t mode);
    void (*on_config_update)(app_config_params_t *config);
    void (*on_emergency_stop)(void);
    
    // 内部状态
    uint8_t is_initialized;
    uint8_t error_code;
} mobile_app_t;

// 接收状态定义
#define APP_RX_STATE_IDLE             0
#define APP_RX_STATE_HEADER           1
#define APP_RX_STATE_VERSION          2
#define APP_RX_STATE_COMMAND          3
#define APP_RX_STATE_LENGTH           4
#define APP_RX_STATE_PAYLOAD          5
#define APP_RX_STATE_CHECKSUM         6
#define APP_RX_STATE_TAIL             7

// 错误代码定义
#define APP_ERROR_NONE                0
#define APP_ERROR_UART_INIT           1
#define APP_ERROR_FRAME_FORMAT        2
#define APP_ERROR_CHECKSUM            3
#define APP_ERROR_TIMEOUT             4
#define APP_ERROR_BUFFER_OVERFLOW     5

// 默认配置
#define APP_DEFAULT_TIMEOUT_MS        5000    // 5秒超时
#define APP_DEFAULT_STREAM_INTERVAL   100     // 100ms数据流间隔
#define APP_DEFAULT_BAUD_RATE         115200  // 波特率

// 函数声明
mobile_app_result_t MobileApp_Init(mobile_app_t *app, UART_HandleTypeDef *uart);
mobile_app_result_t MobileApp_Start(mobile_app_t *app);
mobile_app_result_t MobileApp_Stop(mobile_app_t *app);
mobile_app_result_t MobileApp_Update(mobile_app_t *app);

// 通信函数
mobile_app_result_t MobileApp_SendStatus(mobile_app_t *app, app_system_status_t *status);
mobile_app_result_t MobileApp_SendResponse(mobile_app_t *app, uint8_t command, uint8_t result, uint8_t *data, uint16_t length);
mobile_app_result_t MobileApp_SendDataStream(mobile_app_t *app);
mobile_app_result_t MobileApp_ProcessReceivedData(mobile_app_t *app, uint8_t *data, uint16_t length);

// 配置函数
mobile_app_result_t MobileApp_SetStreamConfig(mobile_app_t *app, app_data_stream_config_t *config);
mobile_app_result_t MobileApp_SetTimeout(mobile_app_t *app, uint32_t timeout_ms);
mobile_app_result_t MobileApp_RegisterCallbacks(mobile_app_t *app,
    void (*on_control)(app_control_cmd_t *),
    void (*on_mode_change)(app_system_mode_t),
    void (*on_config)(app_config_params_t *),
    void (*on_emergency)(void));

// 状态查询函数
uint8_t MobileApp_IsConnected(mobile_app_t *app);
uint32_t MobileApp_GetConnectionTime(mobile_app_t *app);
void MobileApp_GetStatistics(mobile_app_t *app, uint32_t *rx_frames, uint32_t *tx_frames, uint32_t *errors);

// 工具函数
uint8_t MobileApp_CalculateChecksum(uint8_t *data, uint16_t length);
mobile_app_result_t MobileApp_BuildFrame(app_frame_t *frame, uint8_t command, uint8_t *payload, uint16_t length);
mobile_app_result_t MobileApp_ParseFrame(uint8_t *buffer, uint16_t length, app_frame_t *frame);

// 调试函数
void MobileApp_PrintStatus(mobile_app_t *app);
void MobileApp_PrintFrame(app_frame_t *frame);
mobile_app_result_t MobileApp_SelfTest(mobile_app_t *app);

#ifdef __cplusplus
}
#endif

#endif /* __MOBILE_APP_H */
