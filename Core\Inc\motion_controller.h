/**
 ******************************************************************************
 * @file           : motion_controller.h
 * @brief          : 运动控制器头文件
 * @version        : v1.0
 * @date           : 2025-01-27
 ******************************************************************************
 * @description    :
 * 平衡车运动控制器，负责：
 * - 摇杆指令解析和处理
 * - 前后运动控制（通过调整平衡点）
 * - 左右转向控制（通过差速控制）
 * - 运动状态管理和安全保护
 * - 多种运动模式支持
 * 
 * 控制原理：
 * - 前后运动：调整目标倾角实现加速/减速
 * - 左右转向：通过左右轮差速实现转向
 * - 动态平衡：运动过程中实时调整平衡参数
 ******************************************************************************
 */

#ifndef __MOTION_CONTROLLER_H
#define __MOTION_CONTROLLER_H

#include "stm32f4xx_hal.h"
#include "joystick_driver.h"

/**
 * @brief 运动模式枚举
 */
typedef enum {
    MOTION_MODE_STOP = 0,       // 停止模式
    MOTION_MODE_BALANCE,        // 纯平衡模式
    MOTION_MODE_MANUAL,         // 手动控制模式
    MOTION_MODE_AUTO,           // 自动控制模式
    MOTION_MODE_CRUISE,         // 巡航模式
    MOTION_MODE_EMERGENCY       // 紧急模式
} motion_mode_t;

/**
 * @brief 运动状态枚举
 */
typedef enum {
    MOTION_STATE_IDLE = 0,      // 空闲状态
    MOTION_STATE_MOVING,        // 运动状态
    MOTION_STATE_TURNING,       // 转向状态
    MOTION_STATE_BRAKING,       // 制动状态
    MOTION_STATE_ERROR          // 错误状态
} motion_state_t;

/**
 * @brief 运动指令结构
 */
typedef struct {
    float target_tilt_angle;    // 目标倾角 (度)
    float turn_rate;            // 转向速率 (-1.0 ~ +1.0)
    float speed_limit;          // 速度限制 (0.0 ~ 1.0)
    uint8_t enable_motion;      // 运动使能标志
    uint8_t emergency_stop;     // 紧急停止标志
} motion_command_t;

/**
 * @brief 运动参数配置
 */
typedef struct {
    // 前后运动参数
    float max_tilt_angle;       // 最大倾角 (度)
    float tilt_sensitivity;     // 倾角灵敏度
    float acceleration_limit;   // 加速度限制
    float deceleration_limit;   // 减速度限制
    
    // 转向参数
    float max_turn_rate;        // 最大转向速率
    float turn_sensitivity;     // 转向灵敏度
    float turn_damping;         // 转向阻尼
    
    // 安全参数
    float emergency_angle;      // 紧急停止角度
    float max_speed;            // 最大速度
    uint32_t timeout_ms;        // 控制超时时间
    
    // 滤波参数
    float command_filter;       // 指令滤波系数
    float response_filter;      // 响应滤波系数
} motion_config_t;

/**
 * @brief 运动状态反馈
 */
typedef struct {
    float current_tilt_angle;   // 当前倾角
    float current_speed;        // 当前速度
    float current_turn_rate;    // 当前转向速率
    motion_state_t state;       // 运动状态
    motion_mode_t mode;         // 运动模式
    uint32_t last_update_time;  // 上次更新时间
    uint8_t is_stable;          // 稳定状态标志
} motion_status_t;

/**
 * @brief 运动控制器结构
 */
typedef struct {
    motion_config_t config;     // 配置参数
    motion_command_t command;   // 当前指令
    motion_status_t status;     // 状态反馈
    
    // 内部状态
    float filtered_forward;     // 滤波后的前进指令
    float filtered_turn;        // 滤波后的转向指令
    float last_forward_cmd;     // 上次前进指令
    float last_turn_cmd;        // 上次转向指令
    
    // 安全监控
    uint32_t last_command_time; // 上次指令时间
    uint8_t safety_enabled;     // 安全功能使能
    uint8_t initialized;        // 初始化标志
} motion_controller_t;

/**
 * @brief 初始化运动控制器
 * @param controller 控制器指针
 * @param config 配置参数
 * @return HAL状态
 */
HAL_StatusTypeDef Motion_Init(motion_controller_t *controller, motion_config_t *config);

/**
 * @brief 更新运动控制器
 * @param controller 控制器指针
 * @param joystick_data 摇杆数据
 * @param current_angle 当前角度
 * @param dt 时间间隔
 * @return HAL状态
 */
HAL_StatusTypeDef Motion_Update(motion_controller_t *controller,
                               joystick_data_t *joystick_data,
                               float current_angle,
                               float dt);

/**
 * @brief 处理摇杆输入
 * @param controller 控制器指针
 * @param joystick_data 摇杆数据
 * @return HAL状态
 */
HAL_StatusTypeDef Motion_ProcessJoystickInput(motion_controller_t *controller, joystick_data_t *joystick_data);

/**
 * @brief 获取运动指令
 * @param controller 控制器指针
 * @param command 指令输出
 * @return HAL状态
 */
HAL_StatusTypeDef Motion_GetCommand(motion_controller_t *controller, motion_command_t *command);

/**
 * @brief 设置运动模式
 * @param controller 控制器指针
 * @param mode 运动模式
 * @return HAL状态
 */
HAL_StatusTypeDef Motion_SetMode(motion_controller_t *controller, motion_mode_t mode);

/**
 * @brief 紧急停止
 * @param controller 控制器指针
 * @return HAL状态
 */
HAL_StatusTypeDef Motion_EmergencyStop(motion_controller_t *controller);

/**
 * @brief 重置运动控制器
 * @param controller 控制器指针
 * @return HAL状态
 */
HAL_StatusTypeDef Motion_Reset(motion_controller_t *controller);

/**
 * @brief 设置安全参数
 * @param controller 控制器指针
 * @param emergency_angle 紧急角度
 * @param max_speed 最大速度
 * @param timeout_ms 超时时间
 * @return HAL状态
 */
HAL_StatusTypeDef Motion_SetSafetyParams(motion_controller_t *controller,
                                        float emergency_angle,
                                        float max_speed,
                                        uint32_t timeout_ms);

/**
 * @brief 获取运动状态
 * @param controller 控制器指针
 * @param status 状态输出
 * @return HAL状态
 */
HAL_StatusTypeDef Motion_GetStatus(motion_controller_t *controller, motion_status_t *status);

/**
 * @brief 运动控制器自检
 * @param controller 控制器指针
 * @return 1=自检通过, 0=自检失败
 */
uint8_t Motion_SelfTest(motion_controller_t *controller);

// 高级功能接口

/**
 * @brief 设置巡航速度
 * @param controller 控制器指针
 * @param cruise_speed 巡航速度 (-1.0 ~ +1.0)
 * @return HAL状态
 */
HAL_StatusTypeDef Motion_SetCruiseSpeed(motion_controller_t *controller, float cruise_speed);

/**
 * @brief 执行预定义动作
 * @param controller 控制器指针
 * @param action_id 动作ID
 * @return HAL状态
 */
HAL_StatusTypeDef Motion_ExecuteAction(motion_controller_t *controller, uint8_t action_id);

/**
 * @brief 获取运动统计信息
 * @param controller 控制器指针
 * @param info_str 信息字符串缓冲区
 * @param max_len 缓冲区最大长度
 */
void Motion_GetStatistics(motion_controller_t *controller, char *info_str, uint16_t max_len);

// 平衡车专用接口

/**
 * @brief 计算目标倾角 (平衡车专用)
 * @param forward_command 前进指令 (-1000 ~ +1000)
 * @param config 配置参数
 * @return 目标倾角 (度)
 */
float Motion_CalculateTargetTilt(int16_t forward_command, motion_config_t *config);

/**
 * @brief 计算差速系数 (平衡车专用)
 * @param turn_command 转向指令 (-1000 ~ +1000)
 * @param config 配置参数
 * @return 差速系数 (-1.0 ~ +1.0)
 */
float Motion_CalculateTurnRate(int16_t turn_command, motion_config_t *config);

/**
 * @brief 应用运动限制 (平衡车专用)
 * @param controller 控制器指针
 * @param target_tilt 目标倾角指针
 * @param turn_rate 转向速率指针
 */
void Motion_ApplyLimits(motion_controller_t *controller, float *target_tilt, float *turn_rate);

/**
 * @brief 检查安全状态 (平衡车专用)
 * @param controller 控制器指针
 * @param current_angle 当前角度
 * @return 1=安全, 0=不安全
 */
uint8_t Motion_CheckSafety(motion_controller_t *controller, float current_angle);

// 默认配置宏
#define MOTION_DEFAULT_MAX_TILT         15.0f   // 默认最大倾角
#define MOTION_DEFAULT_TILT_SENSITIVITY 0.8f    // 默认倾角灵敏度
#define MOTION_DEFAULT_MAX_TURN_RATE    1.0f    // 默认最大转向速率
#define MOTION_DEFAULT_TURN_SENSITIVITY 0.6f    // 默认转向灵敏度
#define MOTION_DEFAULT_EMERGENCY_ANGLE  45.0f   // 默认紧急角度
#define MOTION_DEFAULT_TIMEOUT_MS       500     // 默认超时时间

#endif /* __MOTION_CONTROLLER_H */
