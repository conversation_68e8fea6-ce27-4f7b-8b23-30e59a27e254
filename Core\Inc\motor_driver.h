/**
 ******************************************************************************
 * @file           : motor_driver.h
 * @brief          : 电机驱动头文件
 * @version        : v1.0
 * @date           : 2025-01-27
 ******************************************************************************
 * @description    :
 * 双电机PWM驱动模块，支持：
 * - 双H桥电机驱动(如L298N、TB6612FNG)
 * - PWM速度控制 + GPIO方向控制
 * - 电机使能控制
 * - 速度反馈接口(可选)
 * 
 * 硬件连接(以L298N为例)：
 * - PWM_A  → TIM2_CH1 (PA0)  左电机PWM
 * - PWM_B  → TIM2_CH2 (PA1)  右电机PWM
 * - DIR_A1 → PA2             左电机方向1
 * - DIR_A2 → PA3             左电机方向2
 * - DIR_B1 → PA4             右电机方向1
 * - DIR_B2 → PA5             右电机方向2
 * - ENABLE → PA6             电机使能
 ******************************************************************************
 */

#ifndef __MOTOR_DRIVER_H
#define __MOTOR_DRIVER_H

#include "stm32f4xx_hal.h"

/**
 * @brief 电机驱动器类型
 */
typedef enum {
    MOTOR_DRIVER_L298N = 0,     // L298N双H桥驱动
    MOTOR_DRIVER_TB6612FNG,     // TB6612FNG双H桥驱动
    MOTOR_DRIVER_DRV8833        // DRV8833双H桥驱动
} motor_driver_type_t;

/**
 * @brief 电机配置结构体
 */
typedef struct {
    // PWM定时器
    TIM_HandleTypeDef *htim;
    uint32_t left_pwm_channel;     // 左电机PWM通道
    uint32_t right_pwm_channel;    // 右电机PWM通道
    
    // 方向控制GPIO
    GPIO_TypeDef *left_dir1_port;  // 左电机方向1端口
    uint16_t left_dir1_pin;        // 左电机方向1引脚
    GPIO_TypeDef *left_dir2_port;  // 左电机方向2端口
    uint16_t left_dir2_pin;        // 左电机方向2引脚
    
    GPIO_TypeDef *right_dir1_port; // 右电机方向1端口
    uint16_t right_dir1_pin;       // 右电机方向1引脚
    GPIO_TypeDef *right_dir2_port; // 右电机方向2端口
    uint16_t right_dir2_pin;       // 右电机方向2引脚
    
    // 使能控制GPIO
    GPIO_TypeDef *enable_port;     // 使能端口
    uint16_t enable_pin;           // 使能引脚
    
    // 驱动器参数
    motor_driver_type_t driver_type;
    uint16_t pwm_frequency;        // PWM频率(Hz)
    uint16_t pwm_resolution;       // PWM分辨率
} motor_config_t;

/**
 * @brief 初始化电机驱动
 * @param config 电机配置
 * @return HAL状态
 */
HAL_StatusTypeDef Motor_Init(motor_config_t *config);

/**
 * @brief 设置电机速度
 * @param left_speed 左电机速度 (-1000 到 1000)
 * @param right_speed 右电机速度 (-1000 到 1000)
 */
void Motor_SetSpeed(int16_t left_speed, int16_t right_speed);

/**
 * @brief 启用/禁用电机
 * @param enable 启用标志
 */
void Motor_Enable(uint8_t enable);

/**
 * @brief 停止所有电机
 */
void Motor_Stop(void);

/**
 * @brief 获取当前电机速度设定值
 * @param left_speed 左电机速度指针
 * @param right_speed 右电机速度指针
 */
void Motor_GetSpeed(int16_t *left_speed, int16_t *right_speed);

/**
 * @brief 电机自检
 * @return 自检结果 (0=成功, 其他=失败)
 */
uint8_t Motor_SelfTest(void);

/**
 * @brief 设置PWM死区时间(防止H桥短路)
 * @param dead_time_us 死区时间(微秒)
 */
void Motor_SetDeadTime(uint16_t dead_time_us);

#endif /* __MOTOR_DRIVER_H */