/**
  ******************************************************************************
  * @file           : mpu6050.h
  * @brief          : MPU6050传感器驱动头文件
  * @version        : v1.0
  * @date           : 2025-06-29
  ******************************************************************************
  * @description    :
  * MPU6050六轴传感器驱动接口定义
  *
  * 数据结构:
  * - 原始数据 (16位ADC值)
  * - 物理量 (g, °/s, °C)
  * - 姿态角 (pitch, roll)
  * - 平衡车专用参数
  *
  * 接口函数:
  * - 初始化和自检
  * - 数据读取
  * - 校准功能
  * - Flash存储
  ******************************************************************************
  */

#ifndef __MPU6050_H
#define __MPU6050_H

#include "stm32f4xx_hal.h"
#include "error_handler.h"

typedef struct {
    // 原始数据
    int16_t accel_x_raw, accel_y_raw, accel_z_raw;
    int16_t gyro_x_raw, gyro_y_raw, gyro_z_raw;
    int16_t temp_raw;

    // 物理量
    float ax, ay, az;           // 加速度 (g)
    float gx, gy, gz;           // 角速度 (°/s)
    float temperature;          // 温度 (°C)

    // 姿态角
    float pitch, roll;          // 俯仰角、横滚角 (°)

    // 平衡车专用参数
    float pitch_rate;           // 俯仰角速度 (°/s) - 平衡车主要控制参数
    float roll_rate;            // 横滚角速度 (°/s) - 转向控制参数
    float pitch_accel;          // 俯仰角加速度 (°/s²)
    float balance_angle;        // 平衡角度 (相对于垂直位置)
} mpu6050_data_t;

// 简单接口
app_error_t mpu6050_init(I2C_HandleTypeDef *hi2c);
app_error_t mpu6050_read_data(I2C_HandleTypeDef *hi2c, mpu6050_data_t *data);
app_error_t mpu6050_self_test(I2C_HandleTypeDef *hi2c);
app_error_t mpu6050_calibrate(I2C_HandleTypeDef *hi2c);
app_error_t mpu6050_save_calibration(void);
app_error_t mpu6050_load_calibration(void);

#endif /* __MPU6050_H */

