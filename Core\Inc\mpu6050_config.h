#ifndef __MPU6050_CONFIG_H
#define __MPU6050_CONFIG_H

#define MPU6050_I2C_ADDR        0xD0
#define MPU6050_DEVICE_ID       0x68

// 寄存器地址
#define MPU6050_REG_WHO_AM_I    0x75
#define MPU6050_REG_PWR_MGMT_1  0x6B
#define MPU6050_REG_ACCEL_CONFIG 0x1C
#define MPU6050_REG_GYRO_CONFIG 0x1B
#define MPU6050_REG_ACCEL_XOUT_H 0x3B

// 转换系数
#define MPU6050_ACCEL_LSB       16384.0f
#define MPU6050_GYRO_LSB        131.0f
#define MPU6050_TEMP_OFFSET     36.53f
#define MPU6050_TEMP_LSB        340.0f

// 数据限制
#define MPU6050_ACCEL_MAX       4.0f
#define MPU6050_GYRO_MAX        500.0f

#endif /* __MPU6050_CONFIG_H */