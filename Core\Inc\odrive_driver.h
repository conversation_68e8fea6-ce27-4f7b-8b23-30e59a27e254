/**
 ******************************************************************************
 * @file           : odrive_driver.h
 * @brief          : ODrive 3.6电机控制板驱动头文件
 * @version        : v1.0
 * @date           : 2025-01-27
 ******************************************************************************
 * @description    :
 * ODrive 3.6双电机控制板驱动，支持：
 * - UART串口通信协议
 * - 速度控制模式
 * - 位置控制模式
 * - 电流控制模式
 * - 实时状态反馈
 * - 错误检测和处理
 * 
 * 硬件连接：
 * - STM32 PA9 (UART1_TX) → ODrive GPIO1 (RX)
 * - STM32 PA10 (UART1_RX) → ODrive GPIO2 (TX)
 * - 波特率：115200bps
 * 
 * ODrive配置要求：
 * - odrv0.config.uart_baudrate = 115200
 * - odrv0.config.enable_uart = True
 * - 电机已完成校准和配置
 ******************************************************************************
 */

#ifndef __ODRIVE_DRIVER_H
#define __ODRIVE_DRIVER_H

#include "stm32f4xx_hal.h"

/**
 * @brief ODrive控制模式
 */
typedef enum {
    ODRIVE_CONTROL_VELOCITY = 0,    // 速度控制模式
    ODRIVE_CONTROL_POSITION,        // 位置控制模式
    ODRIVE_CONTROL_CURRENT          // 电流控制模式
} odrive_control_mode_t;

/**
 * @brief ODrive轴状态
 */
typedef enum {
    ODRIVE_AXIS_STATE_UNDEFINED = 0,
    ODRIVE_AXIS_STATE_IDLE = 1,
    ODRIVE_AXIS_STATE_STARTUP_SEQUENCE = 2,
    ODRIVE_AXIS_STATE_FULL_CALIBRATION_SEQUENCE = 3,
    ODRIVE_AXIS_STATE_MOTOR_CALIBRATION = 4,
    ODRIVE_AXIS_STATE_SENSORLESS_CONTROL = 5,
    ODRIVE_AXIS_STATE_ENCODER_INDEX_SEARCH = 6,
    ODRIVE_AXIS_STATE_ENCODER_OFFSET_CALIBRATION = 7,
    ODRIVE_AXIS_STATE_CLOSED_LOOP_CONTROL = 8,
    ODRIVE_AXIS_STATE_LOCKIN_SPIN = 9,
    ODRIVE_AXIS_STATE_ENCODER_DIR_FIND = 10,
    ODRIVE_AXIS_STATE_HOMING = 11
} odrive_axis_state_t;

/**
 * @brief ODrive电机状态数据
 */
typedef struct {
    float velocity;                 // 当前速度 (turns/s)
    float position;                 // 当前位置 (turns)
    float current;                  // 当前电流 (A)
    float voltage;                  // 当前电压 (V)
    odrive_axis_state_t state;      // 轴状态
    uint32_t error;                 // 错误代码
    uint8_t is_valid;               // 数据有效标志
} odrive_motor_status_t;

/**
 * @brief ODrive配置结构体
 */
typedef struct {
    UART_HandleTypeDef *huart;      // UART句柄
    uint32_t timeout_ms;            // 通信超时时间
    float max_velocity;             // 最大速度限制 (turns/s)
    float max_current;              // 最大电流限制 (A)
    odrive_control_mode_t control_mode; // 控制模式
} odrive_config_t;

/**
 * @brief 初始化ODrive驱动
 * @param config ODrive配置
 * @return HAL状态
 */
HAL_StatusTypeDef ODrive_Init(odrive_config_t *config);

/**
 * @brief 设置电机速度 (速度控制模式)
 * @param axis 轴编号 (0或1)
 * @param velocity 目标速度 (turns/s)
 * @return HAL状态
 */
HAL_StatusTypeDef ODrive_SetVelocity(uint8_t axis, float velocity);

/**
 * @brief 设置电机位置 (位置控制模式)
 * @param axis 轴编号 (0或1)
 * @param position 目标位置 (turns)
 * @param velocity_limit 速度限制 (turns/s)
 * @return HAL状态
 */
HAL_StatusTypeDef ODrive_SetPosition(uint8_t axis, float position, float velocity_limit);

/**
 * @brief 设置电机电流 (电流控制模式)
 * @param axis 轴编号 (0或1)
 * @param current 目标电流 (A)
 * @return HAL状态
 */
HAL_StatusTypeDef ODrive_SetCurrent(uint8_t axis, float current);

/**
 * @brief 启动轴到闭环控制状态
 * @param axis 轴编号 (0或1)
 * @return HAL状态
 */
HAL_StatusTypeDef ODrive_StartAxis(uint8_t axis);

/**
 * @brief 停止轴到空闲状态
 * @param axis 轴编号 (0或1)
 * @return HAL状态
 */
HAL_StatusTypeDef ODrive_StopAxis(uint8_t axis);

/**
 * @brief 读取电机状态
 * @param axis 轴编号 (0或1)
 * @param status 状态数据指针
 * @return HAL状态
 */
HAL_StatusTypeDef ODrive_GetStatus(uint8_t axis, odrive_motor_status_t *status);

/**
 * @brief 清除轴错误
 * @param axis 轴编号 (0或1)
 * @return HAL状态
 */
HAL_StatusTypeDef ODrive_ClearErrors(uint8_t axis);

/**
 * @brief 紧急停止所有电机
 * @return HAL状态
 */
HAL_StatusTypeDef ODrive_EmergencyStop(void);

/**
 * @brief 检查ODrive连接状态
 * @return 1=连接正常, 0=连接异常
 */
uint8_t ODrive_IsConnected(void);

/**
 * @brief 设置控制模式
 * @param axis 轴编号 (0或1)
 * @param mode 控制模式
 * @return HAL状态
 */
HAL_StatusTypeDef ODrive_SetControlMode(uint8_t axis, odrive_control_mode_t mode);

/**
 * @brief 保存ODrive配置到非易失性存储器
 * @return HAL状态
 */
HAL_StatusTypeDef ODrive_SaveConfiguration(void);

/**
 * @brief 重启ODrive
 * @return HAL状态
 */
HAL_StatusTypeDef ODrive_Reboot(void);

/**
 * @brief ODrive自检
 * @return 1=自检通过, 0=自检失败
 */
uint8_t ODrive_SelfTest(void);

// 平衡车专用接口函数
/**
 * @brief 设置平衡车电机速度 (适配motor_driver接口)
 * @param left_speed 左电机速度 (-1000 到 1000)
 * @param right_speed 右电机速度 (-1000 到 1000)
 */
void ODrive_SetMotorSpeed(int16_t left_speed, int16_t right_speed);

/**
 * @brief 启用/禁用电机 (适配motor_driver接口)
 * @param enable 启用标志
 */
void ODrive_EnableMotors(uint8_t enable);

/**
 * @brief 获取当前电机速度 (适配motor_driver接口)
 * @param left_speed 左电机速度指针
 * @param right_speed 右电机速度指针
 */
void ODrive_GetMotorSpeed(int16_t *left_speed, int16_t *right_speed);

#endif /* __ODRIVE_DRIVER_H */
