/**
 ******************************************************************************
 * @file           : pid_controller.h
 * @brief          : PID控制器头文件
 * @version        : v1.0
 * @date           : 2025-01-27
 ******************************************************************************
 * @description    :
 * 通用PID控制器实现，支持：
 * - 标准PID算法
 * - 积分限幅防止积分饱和
 * - 微分项使用测量值变化避免设定点突变冲击
 * - 输出限幅
 * - 参数在线调整
 ******************************************************************************
 */

#ifndef __PID_CONTROLLER_H
#define __PID_CONTROLLER_H

#include "stm32f4xx_hal.h"

/**
 * @brief PID控制器结构体
 */
typedef struct {
    // PID参数
    float Kp;                   // 比例系数
    float Ki;                   // 积分系数  
    float Kd;                   // 微分系数
    
    // 控制限制
    float output_limit;         // 输出限制 (±)
    float integral_limit;       // 积分限制 (±)
    
    // 内部状态
    float setpoint;             // 目标值
    float integral;             // 积分累积
    float prev_error;           // 上一次误差
    float prev_measurement;     // 上一次测量值
    
    // 调试信息
    float last_p_term;          // 上次P项输出
    float last_i_term;          // 上次I项输出
    float last_d_term;          // 上次D项输出
    float last_output;          // 上次总输出
} PID_t;

/**
 * @brief 初始化PID控制器
 * @param pid PID控制器指针
 * @param Kp 比例系数
 * @param Ki 积分系数
 * @param Kd 微分系数
 * @param output_limit 输出限制
 */
void PID_Init(PID_t *pid, float Kp, float Ki, float Kd, float output_limit);

/**
 * @brief PID控制计算
 * @param pid PID控制器指针
 * @param measurement 当前测量值
 * @param dt 时间间隔(秒)
 * @return 控制输出
 */
float PID_Compute(PID_t *pid, float measurement, float dt);

/**
 * @brief 重置PID控制器状态
 * @param pid PID控制器指针
 */
void PID_Reset(PID_t *pid);

/**
 * @brief 设置目标值
 * @param pid PID控制器指针
 * @param setpoint 目标值
 */
void PID_SetSetpoint(PID_t *pid, float setpoint);

/**
 * @brief 设置PID参数
 * @param pid PID控制器指针
 * @param Kp 比例系数
 * @param Ki 积分系数
 * @param Kd 微分系数
 */
void PID_SetParams(PID_t *pid, float Kp, float Ki, float Kd);

/**
 * @brief 获取PID调试信息
 * @param pid PID控制器指针
 * @param p_term P项输出
 * @param i_term I项输出
 * @param d_term D项输出
 */
void PID_GetDebugInfo(PID_t *pid, float *p_term, float *i_term, float *d_term);

#endif /* __PID_CONTROLLER_H */