/**
 * @file auto_follow.c
 * @brief 自动跟随功能实现
 * <AUTHOR> Agent
 * @date 2025-01-05
 * 
 * 硬件配置:
 * - 超声波传感器: PA5(Trig), PA6(Echo)
 * - 可选扩展: 摄像头、激光雷达、蓝牙信标
 */

#include "auto_follow.h"
#include "balance_car_main.h"
#include <stdio.h>
#include <string.h>
#include <math.h>

// 临时类型定义 (应该在头文件中定义)
typedef struct {
    float forward_speed;
    float turn_speed;
} auto_follow_motion_t;

typedef struct {
    auto_follow_state_t state;
    bool target_detected;
    float current_distance;      // 当前距离
    float filtered_distance;
    float target_distance;
    float forward_speed;
    float turn_speed;
} auto_follow_status_t;

/* 外部变量 */
extern TIM_HandleTypeDef htim10;  // 超声波定时器 (如果配置)

/* 私有函数声明 */
static auto_follow_result_t AutoFollow_InitUltrasonic(auto_follow_t *follow);
static float AutoFollow_ReadUltrasonicDistance(auto_follow_t *follow);
static auto_follow_result_t AutoFollow_ProcessUltrasonicData(auto_follow_t *follow);
static auto_follow_result_t AutoFollow_CalculateMotion(auto_follow_t *follow);
static void AutoFollow_UpdatePIDController(auto_follow_t *follow);

/**
 * @brief 获取默认配置
 */
auto_follow_result_t AutoFollow_GetDefaultConfig(follow_config_t *config)
{
    if (config == NULL) {
        return AUTO_FOLLOW_ERROR_NULL_POINTER;
    }
    
    /* 跟随参数 */
    config->target_distance = 0.5f;        // 目标距离 50cm
    config->distance_tolerance = 0.1f;     // 距离容差 ±10cm
    config->max_follow_speed = 1.0f;       // 最大跟随速度 1m/s
    config->min_follow_distance = 0.2f;    // 最小跟随距离 20cm
    config->max_follow_distance = 2.0f;    // 最大跟随距离 2m
    
    /* 超声波配置 */
    config->ultrasonic_timeout_us = 30000; // 30ms超时
    config->measurement_frequency = 20;     // 20Hz测量频率
    config->filter_coefficient = 0.3f;     // 滤波系数
    
    /* PID控制参数 */
    config->distance_pid.kp = 2.0f;
    config->distance_pid.ki = 0.1f;
    config->distance_pid.kd = 0.5f;
    config->distance_pid.output_limit = 1.0f;
    
    /* 安全参数 */
    config->lost_target_timeout_ms = 3000; // 3秒失去目标超时
    config->emergency_stop_distance = 0.15f; // 15cm紧急停止距离
    
    return AUTO_FOLLOW_OK;
}

/**
 * @brief 初始化自动跟随模块
 */
auto_follow_result_t AutoFollow_Init(auto_follow_t *follow, follow_config_t *config)
{
    if (follow == NULL || config == NULL) {
        return AUTO_FOLLOW_ERROR_NULL_POINTER;
    }
    
    /* 清零结构体 */
    memset(follow, 0, sizeof(auto_follow_t));
    
    /* 复制配置 */
    memcpy(&follow->config, config, sizeof(follow_config_t));
    
    /* 设置初始状态 */
    follow->state = AUTO_FOLLOW_STATE_IDLE;
    follow->target_detected = false;
    follow->last_valid_distance = 0.0f;
    
    /* 初始化超声波 */
    auto_follow_result_t result = AutoFollow_InitUltrasonic(follow);
    if (result != AUTO_FOLLOW_OK) {
        return result;
    }
    
    /* 初始化PID控制器 */
    follow->distance_pid_integral = 0.0f;
    follow->distance_pid_last_error = 0.0f;
    
    printf("Auto Follow Module Initialized\r\n");
    return AUTO_FOLLOW_OK;
}

/**
 * @brief 启动自动跟随
 */
auto_follow_result_t AutoFollow_Start(auto_follow_t *follow)
{
    if (follow == NULL) {
        return AUTO_FOLLOW_ERROR_NULL_POINTER;
    }
    
    if (follow->state != AUTO_FOLLOW_STATE_IDLE) {
        return AUTO_FOLLOW_ERROR_INVALID_STATE;
    }
    
    /* 重置状态 */
    follow->state = AUTO_FOLLOW_STATE_SEARCHING;
    follow->target_detected = false;
    follow->last_measurement_time = HAL_GetTick();
    follow->target_lost_time = 0;
    
    /* 重置PID */
    follow->distance_pid_integral = 0.0f;
    follow->distance_pid_last_error = 0.0f;
    
    printf("Auto Follow Started - Searching for target\r\n");
    return AUTO_FOLLOW_OK;
}

/**
 * @brief 停止自动跟随
 */
auto_follow_result_t AutoFollow_Stop(auto_follow_t *follow)
{
    if (follow == NULL) {
        return AUTO_FOLLOW_ERROR_NULL_POINTER;
    }
    
    follow->state = AUTO_FOLLOW_STATE_IDLE;
    follow->target_detected = false;
    follow->motion_output.forward_speed = 0.0f;
    follow->motion_output.turn_speed = 0.0f;
    
    printf("Auto Follow Stopped\r\n");
    return AUTO_FOLLOW_OK;
}

/**
 * @brief 更新自动跟随
 */
auto_follow_result_t AutoFollow_Update(auto_follow_t *follow)
{
    if (follow == NULL) {
        return AUTO_FOLLOW_ERROR_NULL_POINTER;
    }
    
    if (follow->state == AUTO_FOLLOW_STATE_IDLE) {
        return AUTO_FOLLOW_OK;
    }
    
    uint32_t current_time = HAL_GetTick();
    
    /* 检查测量频率 */
    if (current_time - follow->last_measurement_time >= (1000 / follow->config.measurement_frequency)) {
        follow->last_measurement_time = current_time;
        
        /* 处理超声波数据 */
        AutoFollow_ProcessUltrasonicData(follow);
        
        /* 计算运动输出 */
        AutoFollow_CalculateMotion(follow);
    }
    
    /* 检查目标丢失超时 */
    if (!follow->target_detected && follow->target_lost_time > 0) {
        if (current_time - follow->target_lost_time >= follow->config.lost_target_timeout_ms) {
            follow->state = AUTO_FOLLOW_STATE_LOST;
            follow->motion_output.forward_speed = 0.0f;
            follow->motion_output.turn_speed = 0.0f;
            printf("Target lost - stopping\r\n");
        }
    }
    
    return AUTO_FOLLOW_OK;
}

/**
 * @brief 获取运动输出
 */
auto_follow_result_t AutoFollow_GetMotionOutput(auto_follow_t *follow, auto_follow_motion_t *motion)
{
    if (follow == NULL || motion == NULL) {
        return AUTO_FOLLOW_ERROR_NULL_POINTER;
    }
    
    *motion = follow->motion_output;
    return AUTO_FOLLOW_OK;
}

/**
 * @brief 获取状态信息
 */
auto_follow_result_t AutoFollow_GetStatus(auto_follow_t *follow, auto_follow_status_t *status)
{
    if (follow == NULL || status == NULL) {
        return AUTO_FOLLOW_ERROR_NULL_POINTER;
    }
    
    status->state = follow->state;
    status->target_detected = follow->target_detected;
    status->current_distance = follow->current_distance;
    status->filtered_distance = follow->filtered_distance;
    status->target_distance = follow->config.target_distance;
    status->forward_speed = follow->motion_output.forward_speed;
    status->turn_speed = follow->motion_output.turn_speed;
    
    return AUTO_FOLLOW_OK;
}

/**
 * @brief 初始化超声波传感器 (私有函数)
 */
static auto_follow_result_t AutoFollow_InitUltrasonic(auto_follow_t *follow)
{
    /* 配置GPIO引脚 */
    // Trig引脚 (PA5) 已在CubeMX中配置为GPIO_Output
    // Echo引脚 (PA6) 已在CubeMX中配置为GPIO_Input
    
    /* 初始化Trig引脚为低电平 */
    HAL_GPIO_WritePin(GPIOA, GPIO_PIN_5, GPIO_PIN_RESET);
    
    printf("  - 超声波传感器初始化完成\r\n");
    return AUTO_FOLLOW_OK;
}

/**
 * @brief 读取超声波距离 (私有函数)
 */
static float AutoFollow_ReadUltrasonicDistance(auto_follow_t *follow)
{
    uint32_t start_time, end_time;
    float distance;
    
    /* 发送触发脉冲 */
    HAL_GPIO_WritePin(GPIOA, GPIO_PIN_5, GPIO_PIN_SET);
    HAL_Delay(0); // 10us延时 (简化版本)
    HAL_GPIO_WritePin(GPIOA, GPIO_PIN_5, GPIO_PIN_RESET);
    
    /* 等待Echo上升沿 */
    uint32_t timeout = HAL_GetTick() + 50; // 50ms超时
    while (HAL_GPIO_ReadPin(GPIOA, GPIO_PIN_6) == GPIO_PIN_RESET) {
        if (HAL_GetTick() > timeout) {
            return -1.0f; // 超时
        }
    }
    start_time = HAL_GetTick();
    
    /* 等待Echo下降沿 */
    timeout = HAL_GetTick() + 50;
    while (HAL_GPIO_ReadPin(GPIOA, GPIO_PIN_6) == GPIO_PIN_SET) {
        if (HAL_GetTick() > timeout) {
            return -1.0f; // 超时
        }
    }
    end_time = HAL_GetTick();
    
    /* 计算距离 (简化版本，实际需要更精确的时间测量) */
    uint32_t pulse_duration = end_time - start_time;
    distance = (float)pulse_duration * 0.034f / 2.0f; // 声速34cm/ms
    
    return distance;
}

/**
 * @brief 处理超声波数据 (私有函数)
 */
static auto_follow_result_t AutoFollow_ProcessUltrasonicData(auto_follow_t *follow)
{
    float raw_distance = AutoFollow_ReadUltrasonicDistance(follow);
    
    if (raw_distance < 0.0f) {
        /* 测量失败 */
        follow->measurement_valid = false;
        return AUTO_FOLLOW_ERROR_MEASUREMENT_FAILED;
    }
    
    follow->current_distance = raw_distance;
    follow->measurement_valid = true;
    
    /* 低通滤波 */
    if (follow->filtered_distance == 0.0f) {
        follow->filtered_distance = raw_distance;
    } else {
        follow->filtered_distance = follow->filtered_distance * (1.0f - follow->config.filter_coefficient) +
                                   raw_distance * follow->config.filter_coefficient;
    }
    
    /* 检查目标检测 */
    if (follow->filtered_distance >= follow->config.min_follow_distance &&
        follow->filtered_distance <= follow->config.max_follow_distance) {
        
        if (!follow->target_detected) {
            follow->target_detected = true;
            follow->state = AUTO_FOLLOW_STATE_FOLLOWING;
            follow->target_lost_time = 0;
            printf("Target detected at %.2fm\r\n", follow->filtered_distance);
        }
        follow->last_valid_distance = follow->filtered_distance;
    } else {
        if (follow->target_detected) {
            follow->target_detected = false;
            follow->target_lost_time = HAL_GetTick();
            follow->state = AUTO_FOLLOW_STATE_SEARCHING;
        }
    }
    
    return AUTO_FOLLOW_OK;
}

/**
 * @brief 计算运动输出 (私有函数)
 */
static auto_follow_result_t AutoFollow_CalculateMotion(auto_follow_t *follow)
{
    if (!follow->target_detected) {
        /* 没有目标，停止运动 */
        follow->motion_output.forward_speed = 0.0f;
        follow->motion_output.turn_speed = 0.0f;
        return AUTO_FOLLOW_OK;
    }
    
    /* 紧急停止检查 */
    if (follow->filtered_distance <= follow->config.emergency_stop_distance) {
        follow->motion_output.forward_speed = 0.0f;
        follow->motion_output.turn_speed = 0.0f;
        follow->state = AUTO_FOLLOW_STATE_EMERGENCY_STOP;
        return AUTO_FOLLOW_OK;
    }
    
    /* PID距离控制 */
    AutoFollow_UpdatePIDController(follow);
    
    /* 限制输出 */
    if (follow->motion_output.forward_speed > follow->config.max_follow_speed) {
        follow->motion_output.forward_speed = follow->config.max_follow_speed;
    } else if (follow->motion_output.forward_speed < -follow->config.max_follow_speed) {
        follow->motion_output.forward_speed = -follow->config.max_follow_speed;
    }
    
    /* 转向控制 (简化版本，只有前进后退) */
    follow->motion_output.turn_speed = 0.0f;
    
    return AUTO_FOLLOW_OK;
}

/**
 * @brief 更新PID控制器 (私有函数)
 */
static void AutoFollow_UpdatePIDController(auto_follow_t *follow)
{
    float error = follow->config.target_distance - follow->filtered_distance;
    float dt = 1.0f / follow->config.measurement_frequency;
    
    /* PID计算 */
    follow->distance_pid_integral += error * dt;
    float derivative = (error - follow->distance_pid_last_error) / dt;
    
    /* 积分限幅 */
    if (follow->distance_pid_integral > follow->config.distance_pid.output_limit) {
        follow->distance_pid_integral = follow->config.distance_pid.output_limit;
    } else if (follow->distance_pid_integral < -follow->config.distance_pid.output_limit) {
        follow->distance_pid_integral = -follow->config.distance_pid.output_limit;
    }
    
    /* PID输出 */
    follow->motion_output.forward_speed = 
        follow->config.distance_pid.kp * error +
        follow->config.distance_pid.ki * follow->distance_pid_integral +
        follow->config.distance_pid.kd * derivative;
    
    follow->distance_pid_last_error = error;
}
