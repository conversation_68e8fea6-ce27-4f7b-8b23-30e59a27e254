/**
 ******************************************************************************
 * @file           : balance_car_advanced.c
 * @brief          : 平衡车高级功能模块实现
 * @version        : v1.0
 * @date           : 2025-01-27
 ******************************************************************************
 */

#include "balance_car_advanced.h"
#include <math.h>
#include <string.h>
#include <stdio.h>
#include <stdlib.h>

// 内部函数声明
static HAL_StatusTypeDef update_path_tracking(advanced_features_t *advanced);
static HAL_StatusTypeDef update_adaptive_control(advanced_features_t *advanced);
static HAL_StatusTypeDef update_remote_control(advanced_features_t *advanced);
static void calculate_position_estimate(advanced_features_t *advanced);
static float calculate_distance(float x1, float y1, float x2, float y2);
static float normalize_angle(float angle);
static void update_performance_metrics(advanced_features_t *advanced);
static HAL_StatusTypeDef execute_remote_command(advanced_features_t *advanced, remote_command_t *cmd);

HAL_StatusTypeDef AdvancedFeatures_Init(advanced_features_t *advanced, balance_car_system_t *system) {
    if (!advanced || !system) {
        return HAL_ERROR;
    }
    
    // 清零结构体
    memset(advanced, 0, sizeof(advanced_features_t));
    
    // 保存系统引用
    advanced->system = system;
    
    // 初始化路径跟踪器
    advanced->path_tracker.state = PATH_STATE_IDLE;
    advanced->path_tracker.lookahead_distance = 50.0f;  // 50cm前瞻距离
    advanced->path_tracker.position_tolerance = 10.0f;  // 10cm位置容差
    advanced->path_tracker.heading_tolerance = 5.0f;    // 5°航向容差
    advanced->path_tracker.max_lateral_error = 30.0f;   // 30cm最大横向误差
    
    // 初始化自适应控制器
    advanced->adaptive_controller.mode = ADAPTIVE_MODE_OFF;
    advanced->adaptive_controller.surface_friction = 1.0f;
    advanced->adaptive_controller.load_estimation = 1.0f;
    advanced->adaptive_controller.adaptive_kp_factor = 1.0f;
    advanced->adaptive_controller.adaptive_kd_factor = 1.0f;
    advanced->adaptive_controller.adaptive_speed_factor = 1.0f;
    advanced->adaptive_controller.learning_rate = 0.01f;
    
    // 初始化遥控接收器
    advanced->remote_receiver.enabled = 0;
    advanced->remote_receiver.timeout_ms = 5000;  // 5秒超时
    advanced->remote_receiver.queue_head = 0;
    advanced->remote_receiver.queue_tail = 0;
    advanced->remote_receiver.queue_count = 0;
    
    // 初始化功能开关
    advanced->path_tracking_enabled = 0;
    advanced->adaptive_control_enabled = 0;
    advanced->remote_control_enabled = 0;
    
    printf("高级功能模块初始化完成\n");
    
    return HAL_OK;
}

HAL_StatusTypeDef AdvancedFeatures_Update(advanced_features_t *advanced) {
    if (!advanced) {
        return HAL_ERROR;
    }
    
    uint32_t current_time = HAL_GetTick();
    advanced->last_update_time = current_time;
    advanced->update_counter++;
    
    // 更新位置估计
    calculate_position_estimate(advanced);
    
    // 更新路径跟踪
    if (advanced->path_tracking_enabled) {
        if (update_path_tracking(advanced) != HAL_OK) {
            printf("路径跟踪更新失败\n");
        }
    }
    
    // 更新自适应控制
    if (advanced->adaptive_control_enabled) {
        if (update_adaptive_control(advanced) != HAL_OK) {
            printf("自适应控制更新失败\n");
        }
    }
    
    // 更新遥控功能
    if (advanced->remote_control_enabled) {
        if (update_remote_control(advanced) != HAL_OK) {
            printf("遥控功能更新失败\n");
        }
    }
    
    // 更新性能指标
    update_performance_metrics(advanced);
    
    return HAL_OK;
}

// ========== 路径跟踪功能实现 ==========

HAL_StatusTypeDef PathTracker_SetPath(advanced_features_t *advanced, 
                                     path_point_t *path_points, 
                                     uint16_t path_length) {
    if (!advanced || !path_points || path_length == 0) {
        return HAL_ERROR;
    }
    
    advanced->path_tracker.path_points = path_points;
    advanced->path_tracker.path_length = path_length;
    advanced->path_tracker.current_point_index = 0;
    advanced->path_tracker.state = PATH_STATE_IDLE;
    
    printf("路径设置完成，包含 %d 个路径点\n", path_length);
    
    return HAL_OK;
}

HAL_StatusTypeDef PathTracker_Start(advanced_features_t *advanced) {
    if (!advanced || !advanced->path_tracker.path_points) {
        return HAL_ERROR;
    }
    
    advanced->path_tracking_enabled = 1;
    advanced->path_tracker.state = PATH_STATE_FOLLOWING;
    advanced->path_tracker.current_point_index = 0;
    advanced->path_tracker.point_start_time = HAL_GetTick();
    
    // 设置系统为自动控制模式
    BalanceCarSystem_SetMode(advanced->system, SYSTEM_MODE_AUTO_CONTROL);
    
    printf("开始路径跟踪\n");
    
    return HAL_OK;
}

HAL_StatusTypeDef PathTracker_Stop(advanced_features_t *advanced) {
    if (!advanced) {
        return HAL_ERROR;
    }
    
    advanced->path_tracking_enabled = 0;
    advanced->path_tracker.state = PATH_STATE_IDLE;
    
    // 停止运动
    Motion_SetMode(&advanced->system->motion_controller, MOTION_MODE_STOP);
    
    printf("路径跟踪已停止\n");
    
    return HAL_OK;
}

HAL_StatusTypeDef PathTracker_GetStatus(advanced_features_t *advanced,
                                       uint8_t *progress,
                                       float *distance_remaining) {
    if (!advanced || !progress || !distance_remaining) {
        return HAL_ERROR;
    }
    
    if (advanced->path_tracker.path_length == 0) {
        *progress = 100;
        *distance_remaining = 0.0f;
        return HAL_OK;
    }
    
    // 计算进度
    *progress = (advanced->path_tracker.current_point_index * 100) / advanced->path_tracker.path_length;
    
    // 计算剩余距离
    *distance_remaining = advanced->path_tracker.distance_to_target;
    for (uint16_t i = advanced->path_tracker.current_point_index + 1; i < advanced->path_tracker.path_length; i++) {
        if (i == advanced->path_tracker.current_point_index + 1) {
            *distance_remaining += calculate_distance(
                advanced->path_tracker.path_points[i-1].x,
                advanced->path_tracker.path_points[i-1].y,
                advanced->path_tracker.path_points[i].x,
                advanced->path_tracker.path_points[i].y
            );
        } else {
            *distance_remaining += calculate_distance(
                advanced->path_tracker.path_points[i-1].x,
                advanced->path_tracker.path_points[i-1].y,
                advanced->path_tracker.path_points[i].x,
                advanced->path_tracker.path_points[i].y
            );
        }
    }
    
    return HAL_OK;
}

// ========== 自适应控制功能实现 ==========

HAL_StatusTypeDef AdaptiveControl_Enable(advanced_features_t *advanced, adaptive_mode_t mode) {
    if (!advanced) {
        return HAL_ERROR;
    }
    
    advanced->adaptive_control_enabled = 1;
    advanced->adaptive_controller.mode = mode;
    
    // 重置学习数据
    advanced->adaptive_controller.learning_samples = 0;
    advanced->adaptive_controller.history_index = 0;
    memset(advanced->adaptive_controller.performance_history, 0, 
           sizeof(advanced->adaptive_controller.performance_history));
    
    printf("自适应控制已启用，模式: %d\n", mode);
    
    return HAL_OK;
}

HAL_StatusTypeDef AdaptiveControl_Disable(advanced_features_t *advanced) {
    if (!advanced) {
        return HAL_ERROR;
    }
    
    advanced->adaptive_control_enabled = 0;
    advanced->adaptive_controller.mode = ADAPTIVE_MODE_OFF;
    
    // 重置自适应参数
    advanced->adaptive_controller.adaptive_kp_factor = 1.0f;
    advanced->adaptive_controller.adaptive_kd_factor = 1.0f;
    advanced->adaptive_controller.adaptive_speed_factor = 1.0f;
    
    printf("自适应控制已禁用\n");
    
    return HAL_OK;
}

HAL_StatusTypeDef AdaptiveControl_GetParameters(advanced_features_t *advanced,
                                               float *kp_factor,
                                               float *kd_factor,
                                               float *speed_factor) {
    if (!advanced || !kp_factor || !kd_factor || !speed_factor) {
        return HAL_ERROR;
    }
    
    *kp_factor = advanced->adaptive_controller.adaptive_kp_factor;
    *kd_factor = advanced->adaptive_controller.adaptive_kd_factor;
    *speed_factor = advanced->adaptive_controller.adaptive_speed_factor;
    
    return HAL_OK;
}

// ========== 遥控功能实现 ==========

HAL_StatusTypeDef RemoteControl_Enable(advanced_features_t *advanced, uint32_t timeout_ms) {
    if (!advanced) {
        return HAL_ERROR;
    }
    
    advanced->remote_control_enabled = 1;
    advanced->remote_receiver.enabled = 1;
    advanced->remote_receiver.timeout_ms = timeout_ms;
    advanced->remote_receiver.last_command_time = HAL_GetTick();
    
    // 清空命令队列
    advanced->remote_receiver.queue_head = 0;
    advanced->remote_receiver.queue_tail = 0;
    advanced->remote_receiver.queue_count = 0;
    
    printf("遥控功能已启用，超时: %lu ms\n", timeout_ms);
    
    return HAL_OK;
}

HAL_StatusTypeDef RemoteControl_Disable(advanced_features_t *advanced) {
    if (!advanced) {
        return HAL_ERROR;
    }
    
    advanced->remote_control_enabled = 0;
    advanced->remote_receiver.enabled = 0;
    
    printf("遥控功能已禁用\n");
    
    return HAL_OK;
}

HAL_StatusTypeDef RemoteControl_SendCommand(advanced_features_t *advanced, remote_command_t *command) {
    if (!advanced || !command || !advanced->remote_receiver.enabled) {
        return HAL_ERROR;
    }
    
    // 检查队列是否已满
    if (advanced->remote_receiver.queue_count >= 10) {
        advanced->remote_receiver.commands_dropped++;
        return HAL_ERROR;
    }
    
    // 添加命令到队列
    advanced->remote_receiver.command_queue[advanced->remote_receiver.queue_tail] = *command;
    advanced->remote_receiver.queue_tail = (advanced->remote_receiver.queue_tail + 1) % 10;
    advanced->remote_receiver.queue_count++;
    advanced->remote_receiver.commands_received++;
    advanced->remote_receiver.last_command_time = HAL_GetTick();
    
    return HAL_OK;
}

HAL_StatusTypeDef RemoteControl_GetStatus(advanced_features_t *advanced,
                                         uint8_t *connected,
                                         uint32_t *last_command_age_ms) {
    if (!advanced || !connected || !last_command_age_ms) {
        return HAL_ERROR;
    }

    uint32_t current_time = HAL_GetTick();
    *last_command_age_ms = current_time - advanced->remote_receiver.last_command_time;
    *connected = (*last_command_age_ms < advanced->remote_receiver.timeout_ms) ? 1 : 0;

    return HAL_OK;
}

// ========== 内部函数实现 ==========

static HAL_StatusTypeDef update_path_tracking(advanced_features_t *advanced) {
    if (advanced->path_tracker.state != PATH_STATE_FOLLOWING ||
        !advanced->path_tracker.path_points) {
        return HAL_OK;
    }

    // 获取当前目标点
    if (advanced->path_tracker.current_point_index >= advanced->path_tracker.path_length) {
        advanced->path_tracker.state = PATH_STATE_REACHED;
        PathTracker_Stop(advanced);
        printf("路径跟踪完成！\n");
        return HAL_OK;
    }

    path_point_t *target = &advanced->path_tracker.path_points[advanced->path_tracker.current_point_index];

    // 计算到目标点的距离和方向
    float dx = target->x - advanced->path_tracker.current_x;
    float dy = target->y - advanced->path_tracker.current_y;
    advanced->path_tracker.distance_to_target = sqrtf(dx*dx + dy*dy);

    float target_heading = atan2f(dy, dx) * 180.0f / M_PI;
    advanced->path_tracker.heading_error = normalize_angle(target_heading - advanced->path_tracker.current_heading);

    // 计算横向误差
    float cross_track_error = dx * sinf(advanced->path_tracker.current_heading * M_PI / 180.0f) -
                             dy * cosf(advanced->path_tracker.current_heading * M_PI / 180.0f);
    advanced->path_tracker.lateral_error = cross_track_error;

    // 检查是否到达目标点
    if (advanced->path_tracker.distance_to_target < advanced->path_tracker.position_tolerance) {
        // 检查停留时间
        uint32_t dwell_time = HAL_GetTick() - advanced->path_tracker.point_start_time;
        if (dwell_time >= target->dwell_time_ms) {
            // 移动到下一个点
            advanced->path_tracker.current_point_index++;
            advanced->path_tracker.point_start_time = HAL_GetTick();
            printf("到达路径点 %d\n", advanced->path_tracker.current_point_index);
        }
        return HAL_OK;
    }

    // 生成运动指令
    motion_command_t motion_cmd = {0};
    motion_cmd.enable_motion = 1;

    // 前进速度控制
    float speed_factor = 1.0f;
    if (advanced->path_tracker.distance_to_target < advanced->path_tracker.lookahead_distance) {
        speed_factor = advanced->path_tracker.distance_to_target / advanced->path_tracker.lookahead_distance;
    }
    motion_cmd.target_tilt_angle = target->target_speed * speed_factor * 0.01f; // 简化的速度到角度转换

    // 转向控制
    motion_cmd.turn_rate = advanced->path_tracker.heading_error * 0.1f; // 简化的航向控制

    // 限制控制量
    if (motion_cmd.target_tilt_angle > 15.0f) motion_cmd.target_tilt_angle = 15.0f;
    if (motion_cmd.target_tilt_angle < -15.0f) motion_cmd.target_tilt_angle = -15.0f;
    if (motion_cmd.turn_rate > 5.0f) motion_cmd.turn_rate = 5.0f;
    if (motion_cmd.turn_rate < -5.0f) motion_cmd.turn_rate = -5.0f;

    // 应用运动指令
    // 将motion_command_t转换为joystick_data_t格式
    joystick_data_t joystick_equiv = {
        .x_axis = (int16_t)(motion_cmd.target_tilt_angle * 100),  // 转换为摇杆范围
        .y_axis = (int16_t)(motion_cmd.turn_rate * 1000),
        .button_pressed = 0,
        .raw_x = (int16_t)(motion_cmd.target_tilt_angle * 2048),
        .raw_y = (int16_t)(motion_cmd.turn_rate * 2048)
    };
    Motion_Update(&advanced->system->motion_controller, &joystick_equiv,
                 advanced->system->sensor_data.pitch, 0.005f);

    return HAL_OK;
}

static HAL_StatusTypeDef update_adaptive_control(advanced_features_t *advanced) {
    if (advanced->adaptive_controller.mode == ADAPTIVE_MODE_OFF) {
        return HAL_OK;
    }

    // 获取当前系统状态
    float current_angle = advanced->system->status.current_angle;
    float current_angular_velocity = advanced->system->status.current_angular_velocity;

    // 估计环境参数
    // 简化版本：基于角度偏差估计地面摩擦
    float angle_deviation = fabsf(current_angle);
    if (angle_deviation > 5.0f) {
        advanced->adaptive_controller.surface_friction *= 0.99f; // 摩擦系数降低
    } else if (angle_deviation < 1.0f) {
        advanced->adaptive_controller.surface_friction *= 1.01f; // 摩擦系数增加
    }

    // 限制摩擦系数范围
    if (advanced->adaptive_controller.surface_friction < 0.5f) {
        advanced->adaptive_controller.surface_friction = 0.5f;
    }
    if (advanced->adaptive_controller.surface_friction > 2.0f) {
        advanced->adaptive_controller.surface_friction = 2.0f;
    }

    // 根据模式调整参数
    switch (advanced->adaptive_controller.mode) {
        case ADAPTIVE_MODE_BASIC:
            // 基础自适应：根据角度偏差调整Kp
            if (angle_deviation > 3.0f) {
                advanced->adaptive_controller.adaptive_kp_factor = 1.2f;
                advanced->adaptive_controller.adaptive_kd_factor = 1.1f;
            } else {
                advanced->adaptive_controller.adaptive_kp_factor = 1.0f;
                advanced->adaptive_controller.adaptive_kd_factor = 1.0f;
            }
            break;

        case ADAPTIVE_MODE_ADVANCED:
            // 高级自适应：考虑多个因素
            float stability_factor = 1.0f / (1.0f + angle_deviation);
            float response_factor = 1.0f / (1.0f + fabsf(current_angular_velocity));

            advanced->adaptive_controller.adaptive_kp_factor =
                0.8f + 0.4f * stability_factor * advanced->adaptive_controller.surface_friction;
            advanced->adaptive_controller.adaptive_kd_factor =
                0.9f + 0.2f * response_factor;
            advanced->adaptive_controller.adaptive_speed_factor =
                0.8f + 0.4f * advanced->adaptive_controller.surface_friction;
            break;

        case ADAPTIVE_MODE_LEARNING:
            // 学习模式：记录性能并优化参数
            float performance = 100.0f / (1.0f + angle_deviation + fabsf(current_angular_velocity));

            // 记录性能历史
            advanced->adaptive_controller.performance_history[advanced->adaptive_controller.history_index] = performance;
            advanced->adaptive_controller.history_index =
                (advanced->adaptive_controller.history_index + 1) % 100;
            advanced->adaptive_controller.learning_samples++;

            // 基于性能历史调整参数
            if (advanced->adaptive_controller.learning_samples > 10) {
                float avg_performance = 0.0f;
                for (int i = 0; i < 10; i++) {
                    int idx = (advanced->adaptive_controller.history_index - 1 - i + 100) % 100;
                    avg_performance += advanced->adaptive_controller.performance_history[idx];
                }
                avg_performance /= 10.0f;

                // 简化的梯度下降
                if (avg_performance < 80.0f) {
                    advanced->adaptive_controller.adaptive_kp_factor +=
                        advanced->adaptive_controller.learning_rate * (80.0f - avg_performance);
                }
            }
            break;

        default:
            break;
    }

    // 应用自适应参数到平衡控制器
    Balance_AdaptivePIDTuning(&advanced->system->balance_controller,
                             current_angle,
                             advanced->adaptive_controller.adaptive_kp_factor);

    return HAL_OK;
}

static HAL_StatusTypeDef update_remote_control(advanced_features_t *advanced) {
    if (!advanced->remote_receiver.enabled) {
        return HAL_OK;
    }

    // 检查超时
    uint32_t current_time = HAL_GetTick();
    if (current_time - advanced->remote_receiver.last_command_time > advanced->remote_receiver.timeout_ms) {
        // 超时，停止运动
        Motion_SetMode(&advanced->system->motion_controller, MOTION_MODE_STOP);
        return HAL_OK;
    }

    // 处理命令队列
    while (advanced->remote_receiver.queue_count > 0) {
        remote_command_t cmd = advanced->remote_receiver.command_queue[advanced->remote_receiver.queue_head];
        advanced->remote_receiver.queue_head = (advanced->remote_receiver.queue_head + 1) % 10;
        advanced->remote_receiver.queue_count--;

        // 执行命令
        if (execute_remote_command(advanced, &cmd) == HAL_OK) {
            advanced->remote_receiver.commands_executed++;
        }
    }

    return HAL_OK;
}

static void calculate_position_estimate(advanced_features_t *advanced) {
    // 简化的位置估计：基于角度积分
    // 实际应用中应该使用更复杂的里程计算法

    static uint32_t last_time = 0;
    uint32_t current_time = HAL_GetTick();

    if (last_time == 0) {
        last_time = current_time;
        return;
    }

    float dt = (current_time - last_time) / 1000.0f;
    last_time = current_time;

    // 估计速度 (基于倾斜角度)
    float estimated_speed = advanced->system->status.current_angle * 2.0f; // cm/s

    // 估计航向变化 (基于转向速率)
    float heading_rate = advanced->system->status.turn_rate; // °/s

    // 更新位置
    float heading_rad = advanced->path_tracker.current_heading * M_PI / 180.0f;
    advanced->path_tracker.current_x += estimated_speed * cosf(heading_rad) * dt;
    advanced->path_tracker.current_y += estimated_speed * sinf(heading_rad) * dt;
    advanced->path_tracker.current_heading += heading_rate * dt;
    advanced->path_tracker.current_heading = normalize_angle(advanced->path_tracker.current_heading);
}

static float calculate_distance(float x1, float y1, float x2, float y2) {
    float dx = x2 - x1;
    float dy = y2 - y1;
    return sqrtf(dx*dx + dy*dy);
}

static float normalize_angle(float angle) {
    while (angle > 180.0f) angle -= 360.0f;
    while (angle < -180.0f) angle += 360.0f;
    return angle;
}

static void update_performance_metrics(advanced_features_t *advanced) {
    // 更新稳定性指数
    float angle_deviation = fabsf(advanced->system->status.current_angle);
    advanced->adaptive_controller.stability_index = 100.0f / (1.0f + angle_deviation);

    // 更新响应性指数
    float angular_velocity = fabsf(advanced->system->status.current_angular_velocity);
    advanced->adaptive_controller.response_index = 100.0f / (1.0f + angular_velocity);

    // 更新能效指数 (简化版本)
    int16_t motor_output = abs(advanced->system->status.left_motor_output) +
                          abs(advanced->system->status.right_motor_output);
    advanced->adaptive_controller.energy_efficiency = 100.0f / (1.0f + motor_output / 100.0f);
}

static HAL_StatusTypeDef execute_remote_command(advanced_features_t *advanced, remote_command_t *cmd) {
    if (!cmd) {
        return HAL_ERROR;
    }

    motion_command_t motion_cmd = {0};

    switch (cmd->type) {
        case REMOTE_CMD_STOP:
            Motion_SetMode(&advanced->system->motion_controller, MOTION_MODE_STOP);
            break;

        case REMOTE_CMD_FORWARD:
            motion_cmd.enable_motion = 1;
            motion_cmd.target_tilt_angle = cmd->parameter1; // 前进角度
            // 将motion_command_t转换为joystick_data_t格式
            joystick_data_t joystick_equiv = {
                .x_axis = (int16_t)(motion_cmd.target_tilt_angle * 100),
                .y_axis = (int16_t)(motion_cmd.turn_rate * 1000),
                .button_pressed = 0,
                .raw_x = (int16_t)(motion_cmd.target_tilt_angle * 2048),
                .raw_y = (int16_t)(motion_cmd.turn_rate * 2048)
            };
            Motion_Update(&advanced->system->motion_controller, &joystick_equiv,
                         advanced->system->sensor_data.pitch, 0.005f);
            break;

        case REMOTE_CMD_BACKWARD:
            motion_cmd.enable_motion = 1;
            motion_cmd.target_tilt_angle = -cmd->parameter1; // 后退角度
            joystick_equiv.x_axis = (int16_t)(-cmd->parameter1 * 100);
            joystick_equiv.raw_x = (int16_t)(-cmd->parameter1 * 2048);
            Motion_Update(&advanced->system->motion_controller, &joystick_equiv,
                         advanced->system->sensor_data.pitch, 0.005f);
            break;

        case REMOTE_CMD_LEFT:
            motion_cmd.enable_motion = 1;
            motion_cmd.turn_rate = -cmd->parameter1; // 左转速率
            joystick_equiv.y_axis = (int16_t)(-cmd->parameter1 * 100);
            joystick_equiv.raw_y = (int16_t)(-cmd->parameter1 * 2048);
            Motion_Update(&advanced->system->motion_controller, &joystick_equiv,
                         advanced->system->sensor_data.pitch, 0.005f);
            break;

        case REMOTE_CMD_RIGHT:
            motion_cmd.enable_motion = 1;
            motion_cmd.turn_rate = cmd->parameter1; // 右转速率
            joystick_equiv.y_axis = (int16_t)(cmd->parameter1 * 100);
            joystick_equiv.raw_y = (int16_t)(cmd->parameter1 * 2048);
            Motion_Update(&advanced->system->motion_controller, &joystick_equiv,
                         advanced->system->sensor_data.pitch, 0.005f);
            break;

        case REMOTE_CMD_EMERGENCY_STOP:
            BalanceCarSystem_EmergencyStop(advanced->system);
            break;

        default:
            return HAL_ERROR;
    }

    printf("执行遥控命令: %d\n", cmd->type);

    return HAL_OK;
}
