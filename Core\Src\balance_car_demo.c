/**
 ******************************************************************************
 * @file           : balance_car_demo.c
 * @brief          : 平衡车完整演示应用实现
 * @version        : v1.0
 * @date           : 2025-01-27
 ******************************************************************************
 */

#include "balance_car_demo.h"
#include <stdio.h>
#include <string.h>
#include <math.h>

// 演示模式名称
const char* demo_mode_names[] = {
    "基础平衡演示",
    "摇杆控制演示", 
    "自动调优演示",
    "路径跟踪演示",
    "自适应控制演示",
    "遥控演示",
    "综合演示"
};

// 内部函数声明
static HAL_StatusTypeDef run_basic_balance_demo(balance_car_demo_t *demo);
static HAL_StatusTypeDef run_joystick_control_demo(balance_car_demo_t *demo);
static HAL_StatusTypeDef run_auto_tuning_demo(balance_car_demo_t *demo);
static HAL_StatusTypeDef run_path_tracking_demo(balance_car_demo_t *demo);
static HAL_StatusTypeDef run_adaptive_control_demo(balance_car_demo_t *demo);
static HAL_StatusTypeDef run_remote_control_demo(balance_car_demo_t *demo);
static HAL_StatusTypeDef run_comprehensive_demo(balance_car_demo_t *demo);
static void display_basic_info(balance_car_demo_t *demo);
static void display_sensor_data(balance_car_demo_t *demo);
static void display_control_data(balance_car_demo_t *demo);
static void display_advanced_info(balance_car_demo_t *demo);
static void log_data_sample(balance_car_demo_t *demo);

HAL_StatusTypeDef BalanceCarDemo_Init(balance_car_demo_t *demo, demo_config_t *config) {
    if (!demo || !config) {
        return HAL_ERROR;
    }
    
    // 清零结构体
    memset(demo, 0, sizeof(balance_car_demo_t));
    
    // 保存配置
    demo->config = *config;
    demo->state = DEMO_STATE_INIT;
    demo->current_mode = config->mode;
    
    // 初始化核心系统
    balance_car_config_t system_config;
    BalanceCarSystem_GetDefaultConfig(&system_config);
    
    if (BalanceCarSystem_Init(&demo->system, &system_config) != HAL_OK) {
        printf("系统初始化失败\n");
        return HAL_ERROR;
    }
    
    // 初始化调优器
    if (BalanceCarTuner_Init(&demo->tuner, &demo->system, &config->tuning_config) != HAL_OK) {
        printf("调优器初始化失败\n");
        return HAL_ERROR;
    }
    
    // 初始化高级功能
    if (AdvancedFeatures_Init(&demo->advanced, &demo->system) != HAL_OK) {
        printf("高级功能初始化失败\n");
        return HAL_ERROR;
    }
    
    // 设置演示路径
    if (config->demo_path_length > 0) {
        PathTracker_SetPath(&demo->advanced, config->demo_path, config->demo_path_length);
    }
    
    demo->state = DEMO_STATE_MENU;
    demo->demo_start_time = HAL_GetTick();
    
    printf("=== 平衡车演示系统初始化完成 ===\n");
    printf("当前模式: %s\n", demo_mode_names[demo->current_mode]);
    printf("按键说明:\n");
    printf("1/2: 切换模式  3: 暂停/恢复  4: 停止  5: 紧急停止\n");
    printf("6: 切换显示  7: 开始调优  8: 重置\n");
    printf("=====================================\n");
    
    return HAL_OK;
}

HAL_StatusTypeDef BalanceCarDemo_Start(balance_car_demo_t *demo, demo_mode_t mode) {
    if (!demo) {
        return HAL_ERROR;
    }
    
    demo->current_mode = mode;
    demo->state = DEMO_STATE_RUNNING;
    demo->mode_start_time = HAL_GetTick();
    demo->mode_switch_counter++;
    
    printf("\n=== 开始演示: %s ===\n", demo_mode_names[mode]);
    
    // 根据模式进行特定初始化
    switch (mode) {
        case DEMO_MODE_BASIC_BALANCE:
            BalanceCarSystem_SetMode(&demo->system, SYSTEM_MODE_BALANCE_ONLY);
            break;
            
        case DEMO_MODE_JOYSTICK_CONTROL:
            BalanceCarSystem_SetMode(&demo->system, SYSTEM_MODE_MANUAL_CONTROL);
            break;
            
        case DEMO_MODE_AUTO_TUNING:
            BalanceCarTuner_Start(&demo->tuner, TUNING_MODE_AUTO_BALANCE);
            break;
            
        case DEMO_MODE_PATH_TRACKING:
            BalanceCarSystem_SetMode(&demo->system, SYSTEM_MODE_AUTO_CONTROL);
            PathTracker_Start(&demo->advanced);
            break;
            
        case DEMO_MODE_ADAPTIVE_CONTROL:
            BalanceCarSystem_SetMode(&demo->system, SYSTEM_MODE_BALANCE_ONLY);
            AdaptiveControl_Enable(&demo->advanced, ADAPTIVE_MODE_ADVANCED);
            break;
            
        case DEMO_MODE_REMOTE_CONTROL:
            BalanceCarSystem_SetMode(&demo->system, SYSTEM_MODE_MANUAL_CONTROL);
            RemoteControl_Enable(&demo->advanced, 5000);
            break;
            
        case DEMO_MODE_COMPREHENSIVE:
            BalanceCarSystem_SetMode(&demo->system, SYSTEM_MODE_AUTO_CONTROL);
            AdaptiveControl_Enable(&demo->advanced, ADAPTIVE_MODE_LEARNING);
            RemoteControl_Enable(&demo->advanced, 10000);
            break;
            
        default:
            return HAL_ERROR;
    }
    
    return HAL_OK;
}

HAL_StatusTypeDef BalanceCarDemo_Stop(balance_car_demo_t *demo) {
    if (!demo) {
        return HAL_ERROR;
    }
    
    demo->state = DEMO_STATE_COMPLETED;
    
    // 停止所有功能
    BalanceCarSystem_SetMode(&demo->system, SYSTEM_MODE_STANDBY);
    BalanceCarTuner_Stop(&demo->tuner);
    PathTracker_Stop(&demo->advanced);
    AdaptiveControl_Disable(&demo->advanced);
    RemoteControl_Disable(&demo->advanced);
    
    printf("\n=== 演示已停止 ===\n");
    
    return HAL_OK;
}

HAL_StatusTypeDef BalanceCarDemo_Update(balance_car_demo_t *demo) {
    if (!demo) {
        return HAL_ERROR;
    }
    
    demo->update_counter++;
    
    // 检查演示时间限制
    uint32_t current_time = HAL_GetTick();
    if (demo->config.demo_duration_ms > 0 && 
        current_time - demo->mode_start_time > demo->config.demo_duration_ms) {
        
        if (demo->config.auto_switch_modes) {
            // 自动切换到下一个模式
            demo_mode_t next_mode = (demo->current_mode + 1) % DEMO_MODE_COMPREHENSIVE;
            BalanceCarDemo_SwitchMode(demo, next_mode);
        } else {
            BalanceCarDemo_Stop(demo);
            return HAL_OK;
        }
    }
    
    if (demo->state != DEMO_STATE_RUNNING) {
        return HAL_OK;
    }
    
    // 更新核心系统
    if (BalanceCarSystem_Update(&demo->system) != HAL_OK) {
        demo->error_counter++;
        printf("系统更新错误\n");
    }
    
    // 更新调优器
    if (BalanceCarTuner_Update(&demo->tuner) != HAL_OK) {
        demo->error_counter++;
    }
    
    // 更新高级功能
    if (AdvancedFeatures_Update(&demo->advanced) != HAL_OK) {
        demo->error_counter++;
    }
    
    // 运行当前模式的特定逻辑
    switch (demo->current_mode) {
        case DEMO_MODE_BASIC_BALANCE:
            run_basic_balance_demo(demo);
            break;
        case DEMO_MODE_JOYSTICK_CONTROL:
            run_joystick_control_demo(demo);
            break;
        case DEMO_MODE_AUTO_TUNING:
            run_auto_tuning_demo(demo);
            break;
        case DEMO_MODE_PATH_TRACKING:
            run_path_tracking_demo(demo);
            break;
        case DEMO_MODE_ADAPTIVE_CONTROL:
            run_adaptive_control_demo(demo);
            break;
        case DEMO_MODE_REMOTE_CONTROL:
            run_remote_control_demo(demo);
            break;
        case DEMO_MODE_COMPREHENSIVE:
            run_comprehensive_demo(demo);
            break;
    }
    
    // 更新显示
    BalanceCarDemo_UpdateDisplay(demo);
    
    // 数据记录
    if (demo->config.enable_data_logging) {
        log_data_sample(demo);
    }
    
    return HAL_OK;
}

HAL_StatusTypeDef BalanceCarDemo_SwitchMode(balance_car_demo_t *demo, demo_mode_t mode) {
    if (!demo || mode >= DEMO_MODE_COMPREHENSIVE + 1) {
        return HAL_ERROR;
    }
    
    // 停止当前模式
    BalanceCarDemo_Stop(demo);
    
    // 启动新模式
    return BalanceCarDemo_Start(demo, mode);
}

HAL_StatusTypeDef BalanceCarDemo_Pause(balance_car_demo_t *demo, uint8_t pause) {
    if (!demo) {
        return HAL_ERROR;
    }
    
    if (pause) {
        demo->state = DEMO_STATE_PAUSED;
        BalanceCarSystem_SetMode(&demo->system, SYSTEM_MODE_STANDBY);
        printf("演示已暂停\n");
    } else {
        demo->state = DEMO_STATE_RUNNING;
        printf("演示已恢复\n");
        // 重新启动当前模式
        BalanceCarDemo_Start(demo, demo->current_mode);
    }
    
    return HAL_OK;
}

HAL_StatusTypeDef BalanceCarDemo_HandleInput(balance_car_demo_t *demo, uint8_t input) {
    if (!demo) {
        return HAL_ERROR;
    }
    
    switch (input) {
        case USER_INPUT_MODE_NEXT:
            {
                demo_mode_t next_mode = (demo->current_mode + 1) % (DEMO_MODE_COMPREHENSIVE + 1);
                BalanceCarDemo_SwitchMode(demo, next_mode);
            }
            break;
            
        case USER_INPUT_MODE_PREV:
            {
                demo_mode_t prev_mode = (demo->current_mode == 0) ? 
                    DEMO_MODE_COMPREHENSIVE : (demo->current_mode - 1);
                BalanceCarDemo_SwitchMode(demo, prev_mode);
            }
            break;
            
        case USER_INPUT_PAUSE_RESUME:
            BalanceCarDemo_Pause(demo, demo->state != DEMO_STATE_PAUSED);
            break;
            
        case USER_INPUT_STOP:
            BalanceCarDemo_Stop(demo);
            break;
            
        case USER_INPUT_EMERGENCY:
            BalanceCarSystem_EmergencyStop(&demo->system);
            demo->state = DEMO_STATE_ERROR;
            printf("紧急停止！\n");
            break;
            
        case USER_INPUT_DISPLAY_TOGGLE:
            demo->config.ui_config.display_enabled = !demo->config.ui_config.display_enabled;
            printf("显示 %s\n", demo->config.ui_config.display_enabled ? "开启" : "关闭");
            break;
            
        case USER_INPUT_TUNING_START:
            if (demo->current_mode != DEMO_MODE_AUTO_TUNING) {
                BalanceCarDemo_SwitchMode(demo, DEMO_MODE_AUTO_TUNING);
            }
            break;
            
        case USER_INPUT_RESET:
            // 重置系统
            BalanceCarSystem_Reset(&demo->system);
            demo->error_counter = 0;
            printf("系统已重置\n");
            break;
            
        default:
            return HAL_ERROR;
    }
    
    return HAL_OK;
}

HAL_StatusTypeDef BalanceCarDemo_UpdateDisplay(balance_car_demo_t *demo) {
    if (!demo || !demo->config.ui_config.display_enabled) {
        return HAL_OK;
    }

    uint32_t current_time = HAL_GetTick();
    if (current_time - demo->config.ui_config.last_display_update < demo->config.ui_config.display_interval_ms) {
        return HAL_OK;
    }

    demo->config.ui_config.last_display_update = current_time;

    printf("\n========== 平衡车状态显示 ==========\n");

    if (demo->config.ui_config.show_basic_info) {
        display_basic_info(demo);
    }

    if (demo->config.ui_config.show_sensor_data) {
        display_sensor_data(demo);
    }

    if (demo->config.ui_config.show_control_data) {
        display_control_data(demo);
    }

    if (demo->config.ui_config.show_advanced_info) {
        display_advanced_info(demo);
    }

    printf("===================================\n");

    return HAL_OK;
}

void BalanceCarDemo_GenerateReport(balance_car_demo_t *demo, char *report_str, uint16_t max_len) {
    if (!demo || !report_str) {
        return;
    }

    uint32_t runtime_ms = HAL_GetTick() - demo->demo_start_time;
    uint32_t runtime_sec = runtime_ms / 1000;

    snprintf(report_str, max_len,
        "=== 平衡车演示报告 ===\n"
        "运行时间: %lu秒\n"
        "当前模式: %s\n"
        "更新次数: %lu\n"
        "错误次数: %lu\n"
        "模式切换: %lu次\n"
        "系统状态: %s\n"
        "当前角度: %.2f°\n"
        "角速度: %.2f°/s\n"
        "电机输出: L=%d R=%d\n"
        "最佳PID评分: %.2f\n",
        runtime_sec,
        demo_mode_names[demo->current_mode],
        demo->update_counter,
        demo->error_counter,
        demo->mode_switch_counter,
        (demo->system.status.system_mode == SYSTEM_MODE_BALANCE_ONLY) ? "平衡中" : "其他",
        demo->system.status.current_angle,
        demo->system.status.current_angular_velocity,
        demo->system.status.left_motor_output,
        demo->system.status.right_motor_output,
        demo->tuner.best_score
    );
}

void BalanceCarDemo_GetDefaultConfig(demo_config_t *config) {
    if (!config) {
        return;
    }

    memset(config, 0, sizeof(demo_config_t));

    // 基本配置
    config->mode = DEMO_MODE_BASIC_BALANCE;
    config->demo_duration_ms = DEMO_DEFAULT_DURATION_MS;
    config->auto_switch_modes = 1;
    config->enable_safety_limits = 1;
    config->enable_data_logging = 1;

    // 创建默认演示路径 (方形路径)
    config->demo_path_length = BalanceCarDemo_CreateDemoPath(config->demo_path, 10, 1);

    // 调优配置
    config->tuning_config.mode = TUNING_MODE_AUTO_BALANCE;
    config->tuning_config.test_duration_ms = TUNING_DEFAULT_DURATION_MS;
    config->tuning_config.max_iterations = TUNING_DEFAULT_MAX_ITERATIONS;
    config->tuning_config.target_score = TUNING_DEFAULT_TARGET_SCORE;
    config->tuning_config.enable_auto_save = 1;
    config->tuning_config.enable_safety_limits = 1;

    // UI配置
    config->ui_config.display_enabled = 1;
    config->ui_config.display_interval_ms = DEMO_DEFAULT_DISPLAY_INTERVAL_MS;
    config->ui_config.show_basic_info = 1;
    config->ui_config.show_sensor_data = 1;
    config->ui_config.show_control_data = 1;
    config->ui_config.show_advanced_info = 0;
    config->ui_config.show_debug_info = 0;
}

uint8_t BalanceCarDemo_CreateDemoPath(path_point_t *path_points, uint8_t max_points, uint8_t path_type) {
    if (!path_points || max_points == 0) {
        return 0;
    }

    uint8_t point_count = 0;

    switch (path_type) {
        case 0: // 直线路径
            if (max_points >= 2) {
                path_points[0] = (path_point_t){0, 0, 20, 0, 1000};
                path_points[1] = (path_point_t){100, 0, 20, 0, 2000};
                point_count = 2;
            }
            break;

        case 1: // 方形路径
            if (max_points >= 4) {
                path_points[0] = (path_point_t){0, 0, 15, 0, 1000};
                path_points[1] = (path_point_t){50, 0, 15, 90, 1000};
                path_points[2] = (path_point_t){50, 50, 15, 180, 1000};
                path_points[3] = (path_point_t){0, 50, 15, 270, 1000};
                point_count = 4;
            }
            break;

        case 2: // 圆形路径
            if (max_points >= 8) {
                float radius = 30.0f;
                for (uint8_t i = 0; i < 8 && i < max_points; i++) {
                    float angle = i * 45.0f * M_PI / 180.0f;
                    path_points[i].x = radius * cosf(angle);
                    path_points[i].y = radius * sinf(angle);
                    path_points[i].target_speed = 10;
                    path_points[i].target_heading = (i * 45.0f + 90.0f);
                    path_points[i].dwell_time_ms = 500;
                }
                point_count = 8;
            }
            break;

        case 3: // 8字形路径
            if (max_points >= 6) {
                path_points[0] = (path_point_t){0, 0, 12, 0, 500};
                path_points[1] = (path_point_t){20, 10, 12, 45, 500};
                path_points[2] = (path_point_t){0, 20, 12, 180, 500};
                path_points[3] = (path_point_t){-20, 10, 12, 225, 500};
                path_points[4] = (path_point_t){0, 0, 12, 270, 500};
                path_points[5] = (path_point_t){20, -10, 12, 315, 500};
                point_count = 6;
            }
            break;
    }

    return point_count;
}

// ========== 内部函数实现 ==========

static HAL_StatusTypeDef run_basic_balance_demo(balance_car_demo_t *demo) {
    // 基础平衡演示：只保持平衡，不进行运动控制
    static uint32_t last_disturbance_time = 0;
    uint32_t current_time = HAL_GetTick();

    // 每10秒添加一次小扰动来测试平衡性能
    if (current_time - last_disturbance_time > 10000) {
        printf("添加平衡测试扰动\n");
        // 这里可以添加小的角度偏移来测试恢复能力
        last_disturbance_time = current_time;
    }

    return HAL_OK;
}

static HAL_StatusTypeDef run_joystick_control_demo(balance_car_demo_t *demo) {
    // 摇杆控制演示：展示手动控制功能
    static uint32_t last_instruction_time = 0;
    uint32_t current_time = HAL_GetTick();

    // 每5秒显示一次操作提示
    if (current_time - last_instruction_time > 5000) {
        printf("摇杆控制演示 - 请使用摇杆进行前后左右控制\n");
        last_instruction_time = current_time;
    }

    return HAL_OK;
}

static HAL_StatusTypeDef run_auto_tuning_demo(balance_car_demo_t *demo) {
    // 自动调优演示：显示调优进度
    static uint32_t last_progress_time = 0;
    uint32_t current_time = HAL_GetTick();

    if (current_time - last_progress_time > 3000) {
        uint8_t progress;
        uint32_t eta_seconds;

        if (BalanceCarTuner_GetProgress(&demo->tuner, &progress, &eta_seconds) == HAL_OK) {
            printf("调优进度: %d%%, 预计剩余: %lu秒\n", progress, eta_seconds);
        }

        last_progress_time = current_time;
    }

    return HAL_OK;
}

static HAL_StatusTypeDef run_path_tracking_demo(balance_car_demo_t *demo) {
    // 路径跟踪演示：显示跟踪状态
    static uint32_t last_status_time = 0;
    uint32_t current_time = HAL_GetTick();

    if (current_time - last_status_time > 2000) {
        uint8_t progress;
        float distance_remaining;

        if (PathTracker_GetStatus(&demo->advanced, &progress, &distance_remaining) == HAL_OK) {
            printf("路径跟踪进度: %d%%, 剩余距离: %.1fcm\n", progress, distance_remaining);
        }

        last_status_time = current_time;
    }

    return HAL_OK;
}

static HAL_StatusTypeDef run_adaptive_control_demo(balance_car_demo_t *demo) {
    // 自适应控制演示：显示自适应参数
    static uint32_t last_param_time = 0;
    uint32_t current_time = HAL_GetTick();

    if (current_time - last_param_time > 4000) {
        float kp_factor, kd_factor, speed_factor;

        if (AdaptiveControl_GetParameters(&demo->advanced, &kp_factor, &kd_factor, &speed_factor) == HAL_OK) {
            printf("自适应参数 - Kp: %.2f, Kd: %.2f, Speed: %.2f\n",
                   kp_factor, kd_factor, speed_factor);
        }

        last_param_time = current_time;
    }

    return HAL_OK;
}

static HAL_StatusTypeDef run_remote_control_demo(balance_car_demo_t *demo) {
    // 遥控演示：显示连接状态
    static uint32_t last_status_time = 0;
    uint32_t current_time = HAL_GetTick();

    if (current_time - last_status_time > 3000) {
        uint8_t connected;
        uint32_t last_command_age_ms;

        if (RemoteControl_GetStatus(&demo->advanced, &connected, &last_command_age_ms) == HAL_OK) {
            printf("遥控状态: %s, 上次命令: %lums前\n",
                   connected ? "已连接" : "未连接", last_command_age_ms);
        }

        last_status_time = current_time;
    }

    return HAL_OK;
}

static HAL_StatusTypeDef run_comprehensive_demo(balance_car_demo_t *demo) {
    // 综合演示：结合多种功能
    static uint32_t demo_phase_start = 0;
    static uint8_t current_phase = 0;
    uint32_t current_time = HAL_GetTick();

    if (demo_phase_start == 0) {
        demo_phase_start = current_time;
    }

    uint32_t phase_duration = current_time - demo_phase_start;

    // 每20秒切换一个功能演示
    if (phase_duration > 20000) {
        current_phase = (current_phase + 1) % 3;
        demo_phase_start = current_time;

        switch (current_phase) {
            case 0:
                printf("综合演示 - 阶段1: 自适应平衡控制\n");
                break;
            case 1:
                printf("综合演示 - 阶段2: 路径跟踪\n");
                PathTracker_Start(&demo->advanced);
                break;
            case 2:
                printf("综合演示 - 阶段3: 遥控模式\n");
                PathTracker_Stop(&demo->advanced);
                break;
        }
    }

    return HAL_OK;
}

static void display_basic_info(balance_car_demo_t *demo) {
    uint32_t runtime_sec = (HAL_GetTick() - demo->demo_start_time) / 1000;

    printf("模式: %s | 运行: %lus | 更新: %lu | 错误: %lu\n",
           demo_mode_names[demo->current_mode],
           runtime_sec,
           demo->update_counter,
           demo->error_counter);
}

static void display_sensor_data(balance_car_demo_t *demo) {
    printf("角度: %.2f° | 角速度: %.2f°/s | 温度: %.1f°C\n",
           demo->system.status.current_angle,
           demo->system.status.current_angular_velocity,
           demo->system.status.temperature);
}

static void display_control_data(balance_car_demo_t *demo) {
    printf("电机输出: L=%d R=%d | 摇杆: X=%d Y=%d\n",
           demo->system.status.left_motor_output,
           demo->system.status.right_motor_output,
           demo->system.status.joystick_x,
           demo->system.status.joystick_y);
}

static void display_advanced_info(balance_car_demo_t *demo) {
    printf("调优评分: %.1f | 稳定性: %.1f | 响应性: %.1f\n",
           demo->tuner.best_score,
           demo->advanced.adaptive_controller.stability_index,
           demo->advanced.adaptive_controller.response_index);
}

static void log_data_sample(balance_car_demo_t *demo) {
    uint32_t current_time = HAL_GetTick();

    if (current_time - demo->last_log_time > 100) { // 10Hz记录频率
        demo->log_sample_count++;
        demo->last_log_time = current_time;

        // 这里可以添加数据记录到文件或发送到上位机的代码
        // 简化版本：只在调试时输出关键数据
        if (demo->config.ui_config.show_debug_info && demo->log_sample_count % 50 == 0) {
            printf("LOG[%lu]: A=%.2f AV=%.2f ML=%d MR=%d\n",
                   demo->log_sample_count,
                   demo->system.status.current_angle,
                   demo->system.status.current_angular_velocity,
                   demo->system.status.left_motor_output,
                   demo->system.status.right_motor_output);
        }
    }
}
