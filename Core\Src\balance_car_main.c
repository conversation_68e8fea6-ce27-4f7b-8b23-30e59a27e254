/**
 ******************************************************************************
 * @file           : balance_car_main.c
 * @brief          : 平衡车简化主控制系统实现
 * @version        : v2.0 (优化版)
 * @date           : 2025-01-05
 ******************************************************************************
 * @description    :
 * 平衡车简化主控制系统，基于优化的硬件配置：
 *
 * 硬件配置 (优化版):
 * - MPU6050 + OLED: I2C1 (PB6/PB7) 共用总线
 * - ODrive控制: UART1 (PA9/PA10) ⭐ 修正
 * - 蓝牙通信: UART6 (PA11/PA12) ⭐ 修正
 * - 摇杆输入: ADC1 (PA0/PA1/PA4)
 * - 超声波: GPIO (PA5/PA6)
 *
 * 控制模式：
 * - 待机模式：系统准备就绪
 * - 手动控制：摇杆控制平衡车
 * - 自动跟随：超声波跟随目标
 * - APP控制：手机APP远程控制
 ******************************************************************************
 */

#include "balance_car_main.h"
#include "mpu6050.h"
#include "odrive_driver.h"
#include "../Drivers/ssd1306/ssd1306.h"
#include "auto_follow.h"
#include "mobile_app.h"
#include "error_handler.h"
#include <stdio.h>
#include <string.h>
#include <math.h>

// 全局变量
static balance_car_main_t g_balance_car;
static volatile uint32_t g_system_tick = 0;
static volatile bool g_control_update_flag = false;

// 外部变量 (CubeMX生成)
extern ADC_HandleTypeDef hadc1;     // 摇杆输入
extern UART_HandleTypeDef huart1;   // ODrive通信 ⭐ 修正
extern UART_HandleTypeDef huart6;   // 蓝牙通信 ⭐ 修正
extern I2C_HandleTypeDef hi2c1;     // 传感器+显示共用
extern TIM_HandleTypeDef htim2;     // 系统定时器

// 私有函数声明
static balance_car_result_t BalanceCarMain_InitHardware(balance_car_main_t *car);
static balance_car_result_t BalanceCarMain_InitControllers(balance_car_main_t *car);
static void BalanceCarMain_UpdateSensors(balance_car_main_t *car);
static void BalanceCarMain_RunControlLoop(balance_car_main_t *car);
static void BalanceCarMain_UpdateDisplay(balance_car_main_t *car);
static void BalanceCarMain_ProcessInput(balance_car_main_t *car);
static void BalanceCarMain_SafetyCheck(balance_car_main_t *car);
static void BalanceCarMain_HandleError(balance_car_main_t *car, balance_car_result_t error);
static void BalanceCarMain_HandleError(balance_car_main_t *car, balance_car_result_t error);

/**
 * @brief 获取默认配置
 */
balance_car_result_t BalanceCarMain_GetDefaultConfig(balance_car_config_t *config)
{
    if (config == NULL) {
        return BALANCE_CAR_ERROR_NULL_POINTER;
    }

    /* 控制参数配置 */
    config->control_frequency = 200;  // 200Hz控制频率
    config->display_frequency = 10;   // 10Hz显示更新

    /* 平衡控制PID参数 */
    config->balance_pid.Kp = 15.0f;
    config->balance_pid.Ki = 0.1f;
    config->balance_pid.Kd = 0.8f;
    config->balance_pid.output_limit = 1000.0f;

    /* 速度控制PID参数 */
    config->velocity_pid.Kp = 0.5f;
    config->velocity_pid.Ki = 0.01f;
    config->velocity_pid.Kd = 0.0f;
    config->velocity_pid.output_limit = 30.0f;

    /* 转向控制PID参数 */
    config->turn_pid.Kp = 2.0f;
    config->turn_pid.Ki = 0.0f;
    config->turn_pid.Kd = 0.1f;
    config->turn_pid.output_limit = 500.0f;

    /* 安全参数 */
    config->max_tilt_angle = 45.0f;      // 最大倾斜角度
    config->max_velocity = 2.0f;         // 最大速度 m/s
    config->emergency_stop_angle = 60.0f; // 紧急停止角度

    /* 硬件参数 */
    config->wheel_diameter = 0.1f;       // 轮子直径 10cm
    config->wheel_base = 0.2f;           // 轮距 20cm
    config->robot_height = 0.3f;         // 机器人高度 30cm

    return BALANCE_CAR_OK;
}

/**
 * @brief 初始化平衡车系统
 */
balance_car_result_t BalanceCarMain_Init(balance_car_main_t *car)
{
    if (car == NULL) {
        return BALANCE_CAR_ERROR_NULL_POINTER;
    }

    /* 清零结构体 */
    memset(car, 0, sizeof(balance_car_main_t));

    /* 设置初始状态 */
    car->state = BALANCE_CAR_STATE_INIT;
    car->mode = BALANCE_CAR_MODE_STANDBY;
    car->error_code = BALANCE_CAR_OK;

    /* 初始化硬件 */
    balance_car_result_t result = BalanceCarMain_InitHardware(car);
    if (result != BALANCE_CAR_OK) {
        car->error_code = BALANCE_CAR_ERROR_HARDWARE_INIT;
        return result;
    }

    /* 初始化控制器 */
    result = BalanceCarMain_InitControllers(car);
    if (result != BALANCE_CAR_OK) {
        car->error_code = BALANCE_CAR_ERROR_HARDWARE_INIT;
        return result;
    }

    /* 设置为就绪状态 */
    car->state = BALANCE_CAR_STATE_READY;

    printf("Balance Car Main System Initialized Successfully\r\n");
    return BALANCE_CAR_OK;
}



/**
 * @brief 初始化控制器 (私有函数)
 */
static balance_car_result_t BalanceCarMain_InitControllers(balance_car_main_t *car)
{
    printf("Initializing controllers...\r\n");
    // 控制器初始化代码
    return BALANCE_CAR_OK;
}

/**
 * @brief 更新传感器数据 (私有函数)
 */
static void BalanceCarMain_UpdateSensors(balance_car_main_t *car)
{
    // 传感器数据更新代码
}

/**
 * @brief 运行控制循环 (私有函数)
 */
static void BalanceCarMain_RunControlLoop(balance_car_main_t *car)
{
    // 控制循环代码
}

/**
 * @brief 更新显示 (私有函数)
 */
static void BalanceCarMain_UpdateDisplay(balance_car_main_t *car)
{
    // 显示更新代码
}

/**
 * @brief 处理输入 (私有函数)
 */
static void BalanceCarMain_ProcessInput(balance_car_main_t *car)
{
    // 输入处理代码
}

/**
 * @brief 安全检查 (私有函数)
 */
static void BalanceCarMain_SafetyCheck(balance_car_main_t *car)
{
    // 安全检查代码
    // 检查倾斜角度是否超过安全限制
    if (fabs(car->sensor_data.pitch) > car->config.emergency_stop_angle) {
        BalanceCarMain_HandleError(car, BALANCE_CAR_ERROR_HARDWARE_INIT);
    }
}

/**
 * @brief 平衡车主程序入口 (简化版)
 */
void BalanceCar_Main(void) {
    printf("\n=== 双轮平衡车控制系统启动 (优化版) ===\n");
    printf("版本: v2.0\n");
    printf("日期: 2025-01-05\n");
    printf("硬件: STM32F411 + MPU6050 + ODrive3.6\n");
    printf("功能: 简化架构 + 多模式控制\n");
    printf("========================================\n\n");

    /* 获取默认配置 */
    balance_car_config_t config;
    BalanceCarMain_GetDefaultConfig(&config);

    /* 初始化系统 */
    printf("1. 初始化平衡车系统...\n");
    if (BalanceCarMain_Init(&g_balance_car) != BALANCE_CAR_OK) {
        printf("❌ 系统初始化失败！\n");
        Error_Handler();
    }

    /* 设置配置 */
    BalanceCarMain_SetConfig(&g_balance_car, &config);

    /* 启动系统 */
    printf("2. 启动平衡车系统...\n");
    if (BalanceCarMain_Start(&g_balance_car) != BALANCE_CAR_OK) {
        printf("❌ 系统启动失败！\n");
        Error_Handler();
    }

    /* 设置为手动控制模式 */
    BalanceCarMain_SetMode(&g_balance_car, BALANCE_CAR_MODE_MANUAL);

    printf("✅ 系统启动完成，进入主循环\n\n");

    /* 主循环 */
    while (1) {
        BalanceCarMain_Update(&g_balance_car);
        HAL_Delay(5); // 200Hz更新频率
    }
}

/**
 * @brief 设置配置参数
 */
balance_car_result_t BalanceCarMain_SetConfig(balance_car_main_t *car, const balance_car_config_t *config)
{
    if (car == NULL || config == NULL) {
        return BALANCE_CAR_ERROR_NULL_POINTER;
    }

    /* 复制配置 */
    memcpy(&car->config, config, sizeof(balance_car_config_t));

    printf("Balance Car Configuration Updated\r\n");
    return BALANCE_CAR_OK;
}

/**
 * @brief 启动平衡车系统
 */
balance_car_result_t BalanceCarMain_Start(balance_car_main_t *car)
{
    if (car == NULL) {
        return BALANCE_CAR_ERROR_NULL_POINTER;
    }

    if (car->state != BALANCE_CAR_STATE_READY) {
        return BALANCE_CAR_ERROR_INVALID_STATE;
    }

    /* 启动定时器中断 */
    HAL_TIM_Base_Start_IT(&htim2);

    /* 启动ADC DMA */
    HAL_ADC_Start_DMA(&hadc1, (uint32_t*)car->joystick_raw, 2);

    /* 设置运行状态 */
    car->state = BALANCE_CAR_STATE_RUNNING;
    car->mode = BALANCE_CAR_MODE_STANDBY;

    printf("Balance Car System Started\r\n");
    return BALANCE_CAR_OK;
}

/**
 * @brief 设置运行模式
 */
balance_car_result_t BalanceCarMain_SetMode(balance_car_main_t *car, balance_car_mode_t mode)
{
    if (car == NULL) {
        return BALANCE_CAR_ERROR_NULL_POINTER;
    }

    if (car->state != BALANCE_CAR_STATE_RUNNING) {
        return BALANCE_CAR_ERROR_INVALID_STATE;
    }

    car->mode = mode;

    /* 根据模式初始化相应功能 */
    switch (mode) {
        case BALANCE_CAR_MODE_MANUAL:
            printf("Switched to Manual Mode\r\n");
            break;

        case BALANCE_CAR_MODE_AUTO_FOLLOW:
            printf("Switched to Auto Follow Mode\r\n");
            break;

        case BALANCE_CAR_MODE_APP_CONTROL:
            printf("Switched to App Control Mode\r\n");
            break;

        case BALANCE_CAR_MODE_STANDBY:
        default:
            printf("Switched to Standby Mode\r\n");
            break;
    }

    return BALANCE_CAR_OK;
}

/**
 * @brief 主更新函数
 */
balance_car_result_t BalanceCarMain_Update(balance_car_main_t *car)
{
    if (car == NULL) {
        return BALANCE_CAR_ERROR_NULL_POINTER;
    }

    /* 检查系统状态 */
    if (car->state != BALANCE_CAR_STATE_RUNNING) {
        return BALANCE_CAR_ERROR_INVALID_STATE;
    }

    /* 安全检查 */
    BalanceCarMain_SafetyCheck(car);

    /* 处理输入 */
    BalanceCarMain_ProcessInput(car);

    /* 更新传感器数据 */
    BalanceCarMain_UpdateSensors(car);

    /* 控制循环 (由定时器中断触发) */
    if (g_control_update_flag) {
        g_control_update_flag = false;
        BalanceCarMain_RunControlLoop(car);
    }

    /* 更新显示 (低频率) */
    static uint32_t last_display_update = 0;
    if (HAL_GetTick() - last_display_update >= car->config.display_update_ms) {
        last_display_update = HAL_GetTick();
        BalanceCarMain_UpdateDisplay(car);
    }

    return BALANCE_CAR_OK;
}

/**
 * @brief 初始化硬件 (私有函数)
 */
static balance_car_result_t BalanceCarMain_InitHardware(balance_car_main_t *car)
{
    printf("  - 初始化MPU6050传感器...\n");
    // if (MPU6050_Init(&hi2c1) != HAL_OK) {
    //     return BALANCE_CAR_ERROR_HARDWARE_INIT;
    // }

    printf("  - 初始化OLED显示...\n");
    // if (SSD1306_Init(&hi2c1) != HAL_OK) {
    //     return BALANCE_CAR_ERROR_HARDWARE_INIT;
    // }

    printf("  - 初始化ODrive驱动...\n");
    odrive_config_t odrive_cfg = {
        .huart = &huart1,              // 使用UART1 ⭐ 修正
        .timeout_ms = 100,
        .max_velocity = 10.0f,
        .max_current = 20.0f,
        .control_mode = ODRIVE_CONTROL_VELOCITY
    };
    // if (ODrive_Init(&car->odrive, &odrive_cfg) != ODRIVE_OK) {
    //     return BALANCE_CAR_ERROR_HARDWARE_INIT;
    // }

    printf("  - 硬件初始化完成\n");
    return BALANCE_CAR_OK;
}



/**
 * @brief 定时器中断回调函数 (200Hz控制频率)
 */
void HAL_TIM_PeriodElapsedCallback(TIM_HandleTypeDef *htim)
{
    if (htim->Instance == TIM2) {
        g_system_tick++;
        g_control_update_flag = true;
    }
}

/**
 * @brief 错误处理 (私有函数)
 */
static void BalanceCarMain_HandleError(balance_car_main_t *car, balance_car_result_t error)
{
    printf("Error: %d\r\n", error);

    /* 紧急停止电机 */
    // ODrive_SetVelocity(&car->odrive, 0, 0.0f);
    // ODrive_SetVelocity(&car->odrive, 1, 0.0f);

    /* 设置错误状态 */
    car->state = BALANCE_CAR_STATE_ERROR;
    car->error_code = error;
}




