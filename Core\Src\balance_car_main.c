/**
 ******************************************************************************
 * @file           : balance_car_main.c
 * @brief          : 平衡车主应用程序
 * @version        : v1.0
 * @date           : 2025-01-27
 ******************************************************************************
 * @description    :
 * 平衡车主应用程序，演示如何使用综合控制系统：
 * 
 * 使用流程：
 * 1. 系统初始化和自检
 * 2. 传感器校准
 * 3. 进入主控制循环
 * 4. 处理用户输入和模式切换
 * 5. 安全监控和错误处理
 * 
 * 控制模式：
 * - 待机模式：系统准备就绪，等待启动
 * - 纯平衡模式：只进行平衡控制，不响应摇杆
 * - 手动控制模式：平衡+摇杆控制
 * - 紧急模式：立即停止所有动作
 ******************************************************************************
 */

#include "balance_car_main.h"
#include "balance_car_system.h"
#include "odrive_driver.h"
#include <stdio.h>

// 全局变量
static balance_car_system_t g_balance_car_system;
static system_config_t g_system_config;

// 外部变量 (需要在main.c中定义)
extern ADC_HandleTypeDef hadc1;
extern UART_HandleTypeDef huart1;  // 用于ODrive通信
extern UART_HandleTypeDef huart2;  // 用于调试输出

// 函数声明
static HAL_StatusTypeDef setup_system_config(void);
static HAL_StatusTypeDef setup_motor_interface(void);
static void handle_user_commands(void);
static void system_startup_sequence(void);
static void system_main_loop(void);

/**
 * @brief 平衡车主程序入口
 */
void BalanceCar_Main(void) {
    printf("\n=== 双轮平衡车控制系统启动 ===\n");
    printf("版本: v1.0\n");
    printf("日期: 2025-01-27\n");
    printf("功能: 电子陀螺仪平衡 + 十字摇杆控制\n");
    printf("================================\n\n");
    
    // 系统启动序列
    system_startup_sequence();
    
    // 进入主循环
    system_main_loop();
}

/**
 * @brief 系统启动序列
 */
static void system_startup_sequence(void) {
    printf("1. 配置系统参数...\n");
    if (setup_system_config() != HAL_OK) {
        printf("❌ 系统配置失败！\n");
        Error_Handler();
    }
    printf("✅ 系统配置完成\n");
    
    printf("2. 初始化平衡车系统...\n");
    if (BalanceCarSystem_Init(&g_balance_car_system, &g_system_config) != HAL_OK) {
        printf("❌ 系统初始化失败！\n");
        Error_Handler();
    }
    printf("✅ 系统初始化完成\n");
    
    printf("3. 执行系统自检...\n");
    if (!BalanceCarSystem_SelfTest(&g_balance_car_system)) {
        printf("❌ 系统自检失败！\n");
        printf("错误代码: 0x%08lX\n", g_balance_car_system.status.error_code);
        printf("错误信息: %s\n", g_balance_car_system.status.error_message);
        Error_Handler();
    }
    printf("✅ 系统自检通过\n");
    
    printf("4. 启动传感器校准...\n");
    if (BalanceCarSystem_StartCalibration(&g_balance_car_system) != HAL_OK) {
        printf("❌ 校准启动失败！\n");
        Error_Handler();
    }
    
    printf("请将平衡车放置在水平面上，保持静止...\n");
    printf("校准中");
    
    // 校准过程 (5秒)
    for (int i = 0; i < 50; i++) {
        HAL_Delay(100);
        printf(".");
        fflush(stdout);
        
        // 更新系统以处理校准
        BalanceCarSystem_Update(&g_balance_car_system);
    }
    printf("\n");
    
    if (BalanceCarSystem_FinishCalibration(&g_balance_car_system) != HAL_OK) {
        printf("❌ 校准完成失败！\n");
        Error_Handler();
    }
    printf("✅ 传感器校准完成\n");
    
    printf("5. 系统准备就绪！\n");
    printf("\n=== 控制说明 ===\n");
    printf("摇杆操作:\n");
    printf("  前后推拉: 前进/后退\n");
    printf("  左右推拉: 左转/右转\n");
    printf("  按键短按: 启动/停止平衡\n");
    printf("  按键长按: 切换控制模式\n");
    printf("\n控制模式:\n");
    printf("  待机模式 → 纯平衡模式 → 手动控制模式 → 待机模式\n");
    printf("================\n\n");
    
    // 启用调试输出
    BalanceCarSystem_EnableDebug(&g_balance_car_system, 1, 2000); // 每2秒输出一次
}

/**
 * @brief 系统主循环
 */
static void system_main_loop(void) {
    printf("进入主控制循环...\n\n");
    
    uint32_t loop_counter = 0;
    uint32_t last_status_time = HAL_GetTick();
    
    while (1) {
        // 更新系统
        if (BalanceCarSystem_Update(&g_balance_car_system) != HAL_OK) {
            printf("⚠️  系统更新错误，错误代码: 0x%08lX\n", g_balance_car_system.status.error_code);
            
            // 尝试恢复
            if (g_balance_car_system.status.error_code & SYSTEM_ERROR_EMERGENCY_STOP) {
                printf("检测到紧急停止，等待用户重置...\n");
                HAL_Delay(1000);
                continue;
            }
        }
        
        // 处理用户命令 (如果有串口输入)
        handle_user_commands();
        
        // 定期输出状态信息
        uint32_t current_time = HAL_GetTick();
        if (current_time - last_status_time >= 5000) { // 每5秒输出一次状态
            system_status_t status;
            BalanceCarSystem_GetStatus(&g_balance_car_system, &status);
            
            printf("=== 系统状态 ===\n");
            printf("运行时间: %lu 秒\n", status.uptime_ms / 1000);
            printf("当前模式: ");
            switch (status.mode) {
                case SYSTEM_MODE_STANDBY: printf("待机模式"); break;
                case SYSTEM_MODE_BALANCE_ONLY: printf("纯平衡模式"); break;
                case SYSTEM_MODE_MANUAL_CONTROL: printf("手动控制模式"); break;
                case SYSTEM_MODE_AUTO_CONTROL: printf("自动控制模式"); break;
                case SYSTEM_MODE_EMERGENCY: printf("紧急模式"); break;
                default: printf("未知模式"); break;
            }
            printf("\n");
            printf("平衡状态: %s\n", status.balance_active ? "激活" : "停止");
            printf("运动控制: %s\n", status.motion_active ? "激活" : "停止");
            printf("当前角度: %.2f°\n", status.current_angle);
            printf("摇杆连接: %s\n", status.joystick_connected ? "已连接" : "未连接");
            printf("===============\n\n");
            
            last_status_time = current_time;
        }
        
        loop_counter++;
        
        // 控制循环频率 (200Hz)
        HAL_Delay(5);
    }
}

/**
 * @brief 配置系统参数
 */
static HAL_StatusTypeDef setup_system_config(void) {
    // 清零配置结构
    memset(&g_system_config, 0, sizeof(system_config_t));
    
    // 硬件配置
    g_system_config.hadc = &hadc1;
    g_system_config.joystick_btn_port = GPIOA;
    g_system_config.joystick_btn_pin = GPIO_PIN_2;
    
    // 控制参数
    g_system_config.control_frequency = SYSTEM_DEFAULT_CONTROL_FREQ;
    g_system_config.sensor_frequency = SYSTEM_DEFAULT_SENSOR_FREQ;
    g_system_config.enable_adaptive_pid = 1;
    g_system_config.enable_safety_check = 1;
    
    // 摇杆配置
    g_system_config.joystick_cfg.hadc = &hadc1;
    g_system_config.joystick_cfg.x_channel = ADC_CHANNEL_0;  // PA0
    g_system_config.joystick_cfg.y_channel = ADC_CHANNEL_1;  // PA1
    g_system_config.joystick_cfg.button_port = GPIOA;
    g_system_config.joystick_cfg.button_pin = GPIO_PIN_2;
    g_system_config.joystick_cfg.deadzone_threshold = 50;
    g_system_config.joystick_cfg.filter_coefficient = 0.1f;
    g_system_config.joystick_cfg.invert_x = 0;
    g_system_config.joystick_cfg.invert_y = 0;
    
    // 运动控制配置
    g_system_config.motion_cfg.max_tilt_angle = 15.0f;
    g_system_config.motion_cfg.max_turn_rate = 5.0f;
    g_system_config.motion_cfg.acceleration_limit = 10.0f;
    g_system_config.motion_cfg.deceleration_limit = 15.0f;
    g_system_config.motion_cfg.response_speed = 2.0f;
    g_system_config.motion_cfg.safety_timeout_ms = 1000;
    
    // 设置电机接口
    if (setup_motor_interface() != HAL_OK) {
        return HAL_ERROR;
    }
    
    return HAL_OK;
}

/**
 * @brief 设置电机接口
 */
static HAL_StatusTypeDef setup_motor_interface(void) {
    // 初始化ODrive驱动
    odrive_config_t odrive_cfg = {
        .huart = &huart2,              // 使用UART2
        .timeout_ms = 100,
        .max_velocity = 10.0f,         // 最大速度10 turns/s
        .max_current = 20.0f,          // 最大电流20A
        .control_mode = ODRIVE_CONTROL_VELOCITY
    };
    
    if (ODrive_Init(&odrive_cfg) != HAL_OK) {
        printf("❌ ODrive初始化失败！\n");
        return HAL_ERROR;
    }
    
    // 设置电机接口函数指针
    g_system_config.motor_interface.set_motor_speed = ODrive_SetMotorSpeed;
    g_system_config.motor_interface.enable_motors = ODrive_EnableMotors;
    g_system_config.motor_interface.get_motor_status = ODrive_GetStatus;
    
    return HAL_OK;
}

/**
 * @brief 处理用户命令 (串口输入)
 */
static void handle_user_commands(void) {
    // 这里可以添加串口命令处理
    // 例如：'r' = 重置系统, 'e' = 紧急停止, 's' = 获取状态等
    
    // 简化版本：检查是否有字符输入
    // 实际实现需要根据具体的串口接收方式来处理
}

/**
 * @brief 错误处理函数
 */
void Error_Handler(void) {
    printf("❌ 系统发生致命错误，停止运行！\n");
    
    // 紧急停止
    BalanceCarSystem_EmergencyStop(&g_balance_car_system);
    
    // 无限循环等待重启
    while (1) {
        HAL_Delay(1000);
        printf("系统已停止，请重启设备...\n");
    }
}
