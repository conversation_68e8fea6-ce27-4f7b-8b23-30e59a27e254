/**
 ******************************************************************************
 * @file           : balance_car_system.c
 * @brief          : 平衡车综合控制系统实现
 * @version        : v1.0
 * @date           : 2025-01-27
 ******************************************************************************
 * @description    :
 * 平衡车综合控制系统实现，包含：
 * - 系统初始化和配置
 * - 主控制循环
 * - 模式切换和状态管理
 * - 安全监控和错误处理
 * - 调试和统计信息
 ******************************************************************************
 */

#include "balance_car_system.h"
#include <math.h>
#include <string.h>
#include <stdio.h>

// 内部函数声明
static HAL_StatusTypeDef update_sensor_data(balance_car_system_t *system);
static HAL_StatusTypeDef update_joystick_data(balance_car_system_t *system);
static HAL_StatusTypeDef process_control_loop(balance_car_system_t *system);
static HAL_StatusTypeDef check_safety_conditions(balance_car_system_t *system);
static void update_system_status(balance_car_system_t *system);
static void handle_mode_transitions(balance_car_system_t *system);
static void print_debug_info(balance_car_system_t *system);

HAL_StatusTypeDef BalanceCarSystem_Init(balance_car_system_t *system, system_config_t *config) {
    if (!system || !config) {
        return HAL_ERROR;
    }
    
    // 保存配置
    system->config = *config;
    
    // 初始化状态
    memset(&system->status, 0, sizeof(system_status_t));
    system->status.mode = SYSTEM_MODE_INIT;
    system->status.state = SYSTEM_STATE_STOPPED;
    system->system_start_time = HAL_GetTick();
    
    // 初始化摇杆驱动
    if (Joystick_Init(&config->joystick_cfg) != HAL_OK) {
        system->status.error_code |= SYSTEM_ERROR_JOYSTICK_FAIL;
        strcpy(system->status.error_message, "Joystick init failed");
        return HAL_ERROR;
    }
    
    // 初始化运动控制器
    if (Motion_Init(&system->motion_controller, &config->motion_cfg) != HAL_OK) {
        system->status.error_code |= SYSTEM_ERROR_COMMUNICATION;
        strcpy(system->status.error_message, "Motion controller init failed");
        return HAL_ERROR;
    }
    
    // 初始化平衡控制器
    Balance_Init(&system->balance_controller, config->motor_interface);
    
    // 启用运动控制集成
    Balance_EnableMotionControl(&system->balance_controller, 1);
    Balance_SetMotionBlendFactor(&system->balance_controller, 0.8f);
    
    // 初始化时间变量
    system->last_control_time = HAL_GetTick();
    system->last_sensor_time = HAL_GetTick();
    system->control_loop_counter = 0;
    system->sensor_loop_counter = 0;
    
    // 初始化安全监控
    system->watchdog_counter = 0;
    system->emergency_stop_flag = 0;
    
    // 初始化调试
    system->debug_enabled = 0;
    system->debug_print_interval = SYSTEM_DEFAULT_DEBUG_INTERVAL;
    system->last_debug_time = HAL_GetTick();
    
    // 设置为待机模式
    system->status.mode = SYSTEM_MODE_STANDBY;
    system->status.state = SYSTEM_STATE_STOPPED;
    
    return HAL_OK;
}

HAL_StatusTypeDef BalanceCarSystem_Update(balance_car_system_t *system) {
    if (!system) {
        return HAL_ERROR;
    }
    
    uint32_t current_time = HAL_GetTick();
    system->status.uptime_ms = current_time - system->system_start_time;
    system->status.last_update_time = current_time;
    
    // 更新传感器数据
    if (update_sensor_data(system) != HAL_OK) {
        system->status.error_code |= SYSTEM_ERROR_SENSOR_FAIL;
        return HAL_ERROR;
    }
    
    // 更新摇杆数据
    if (update_joystick_data(system) != HAL_OK) {
        system->status.error_code |= SYSTEM_ERROR_JOYSTICK_FAIL;
        // 摇杆失败不是致命错误，继续运行
    }
    
    // 安全检查
    if (check_safety_conditions(system) != HAL_OK) {
        BalanceCarSystem_EmergencyStop(system);
        return HAL_ERROR;
    }
    
    // 处理模式转换
    handle_mode_transitions(system);
    
    // 执行控制循环
    if (process_control_loop(system) != HAL_OK) {
        return HAL_ERROR;
    }
    
    // 更新系统状态
    update_system_status(system);
    
    // 调试输出
    if (system->debug_enabled) {
        print_debug_info(system);
    }
    
    // 更新看门狗
    system->watchdog_counter++;
    
    return HAL_OK;
}

HAL_StatusTypeDef BalanceCarSystem_SetMode(balance_car_system_t *system, system_mode_t mode) {
    if (!system) {
        return HAL_ERROR;
    }
    
    // 检查模式切换的合法性
    switch (mode) {
        case SYSTEM_MODE_BALANCE_ONLY:
            Motion_SetMode(&system->motion_controller, MOTION_MODE_BALANCE);
            Balance_EnableMotionControl(&system->balance_controller, 0);
            break;
            
        case SYSTEM_MODE_MANUAL_CONTROL:
            Motion_SetMode(&system->motion_controller, MOTION_MODE_MANUAL);
            Balance_EnableMotionControl(&system->balance_controller, 1);
            break;
            
        case SYSTEM_MODE_AUTO_CONTROL:
            Motion_SetMode(&system->motion_controller, MOTION_MODE_AUTO);
            Balance_EnableMotionControl(&system->balance_controller, 1);
            break;
            
        case SYSTEM_MODE_EMERGENCY:
            BalanceCarSystem_EmergencyStop(system);
            return HAL_OK;
            
        case SYSTEM_MODE_STANDBY:
            Motion_SetMode(&system->motion_controller, MOTION_MODE_STOP);
            Balance_Enable(&system->balance_controller, 0);
            break;
            
        default:
            return HAL_ERROR;
    }
    
    system->status.mode = mode;
    return HAL_OK;
}

HAL_StatusTypeDef BalanceCarSystem_EmergencyStop(balance_car_system_t *system) {
    if (!system) {
        return HAL_ERROR;
    }
    
    // 设置紧急停止标志
    system->emergency_stop_flag = 1;
    
    // 停止所有控制器
    Motion_EmergencyStop(&system->motion_controller);
    Balance_Enable(&system->balance_controller, 0);
    
    // 设置紧急模式
    system->status.mode = SYSTEM_MODE_EMERGENCY;
    system->status.state = SYSTEM_STATE_ERROR;
    system->status.error_code |= SYSTEM_ERROR_EMERGENCY_STOP;
    strcpy(system->status.error_message, "Emergency stop activated");
    
    return HAL_OK;
}

HAL_StatusTypeDef BalanceCarSystem_Reset(balance_car_system_t *system) {
    if (!system) {
        return HAL_ERROR;
    }
    
    // 重置错误状态
    system->status.error_code = SYSTEM_ERROR_NONE;
    memset(system->status.error_message, 0, sizeof(system->status.error_message));
    system->emergency_stop_flag = 0;
    
    // 重置控制器
    Motion_Reset(&system->motion_controller);
    Balance_Enable(&system->balance_controller, 0);
    
    // 重置计数器
    system->control_loop_counter = 0;
    system->sensor_loop_counter = 0;
    system->watchdog_counter = 0;
    
    // 设置为待机模式
    system->status.mode = SYSTEM_MODE_STANDBY;
    system->status.state = SYSTEM_STATE_STOPPED;
    
    return HAL_OK;
}

HAL_StatusTypeDef BalanceCarSystem_GetStatus(balance_car_system_t *system, system_status_t *status) {
    if (!system || !status) {
        return HAL_ERROR;
    }
    
    *status = system->status;
    return HAL_OK;
}

uint8_t BalanceCarSystem_SelfTest(balance_car_system_t *system) {
    if (!system) {
        return 0;
    }
    
    // 测试摇杆
    if (!Joystick_SelfTest()) {
        system->status.error_code |= SYSTEM_ERROR_JOYSTICK_FAIL;
        return 0;
    }
    
    // 测试运动控制器
    if (!Motion_SelfTest(&system->motion_controller)) {
        system->status.error_code |= SYSTEM_ERROR_COMMUNICATION;
        return 0;
    }
    
    // 测试传感器 (简化版)
    if (update_sensor_data(system) != HAL_OK) {
        system->status.error_code |= SYSTEM_ERROR_SENSOR_FAIL;
        return 0;
    }
    
    return 1; // 自检通过
}

HAL_StatusTypeDef BalanceCarSystem_StartCalibration(balance_car_system_t *system) {
    if (!system) {
        return HAL_ERROR;
    }
    
    // 设置校准模式
    system->status.mode = SYSTEM_MODE_CALIBRATION;
    system->status.state = SYSTEM_STATE_CALIBRATING;
    
    // 停止所有控制
    Balance_Enable(&system->balance_controller, 0);
    Motion_SetMode(&system->motion_controller, MOTION_MODE_STOP);
    
    // 开始摇杆校准
    Joystick_StartCalibration();
    
    return HAL_OK;
}

HAL_StatusTypeDef BalanceCarSystem_FinishCalibration(balance_car_system_t *system) {
    if (!system || system->status.mode != SYSTEM_MODE_CALIBRATION) {
        return HAL_ERROR;
    }
    
    // 完成摇杆校准
    if (Joystick_FinishCalibration() != HAL_OK) {
        system->status.error_code |= SYSTEM_ERROR_CALIBRATION;
        return HAL_ERROR;
    }
    
    // 返回待机模式
    system->status.mode = SYSTEM_MODE_STANDBY;
    system->status.state = SYSTEM_STATE_STOPPED;
    
    return HAL_OK;
}

void BalanceCarSystem_EnableDebug(balance_car_system_t *system, uint8_t enable, uint32_t print_interval_ms) {
    if (!system) {
        return;
    }

    system->debug_enabled = enable;
    system->debug_print_interval = print_interval_ms;
    system->last_debug_time = HAL_GetTick();
}

void BalanceCarSystem_GetStatistics(balance_car_system_t *system, char *info_str, uint16_t max_len) {
    if (!system || !info_str) {
        return;
    }

    snprintf(info_str, max_len,
        "System Stats:\n"
        "Uptime: %lu ms\n"
        "Mode: %d, State: %d\n"
        "Control Loops: %lu\n"
        "Sensor Loops: %lu\n"
        "Angle: %.2f°\n"
        "Motors: L=%d R=%d\n"
        "Errors: 0x%08lX\n",
        system->status.uptime_ms,
        system->status.mode, system->status.state,
        system->control_loop_counter,
        system->sensor_loop_counter,
        system->status.current_angle,
        system->status.left_motor_output,
        system->status.right_motor_output,
        system->status.error_code
    );
}

// ========== 内部函数实现 ==========

static HAL_StatusTypeDef update_sensor_data(balance_car_system_t *system) {
    uint32_t current_time = HAL_GetTick();

    // 检查传感器更新频率
    if (current_time - system->last_sensor_time < (1000.0f / system->config.sensor_frequency)) {
        return HAL_OK; // 还没到更新时间
    }

    // 读取MPU6050数据 (这里需要实际的MPU6050读取函数)
    // 暂时使用模拟数据
    system->sensor_data.pitch = 0.0f;      // 实际应该从MPU6050读取
    system->sensor_data.roll = 0.0f;
    // yaw角度在MPU6050中不直接提供，设为0或通过其他方式计算
    // system->sensor_data.yaw = 0.0f;
    system->sensor_data.pitch_rate = 0.0f;
    system->sensor_data.roll_rate = 0.0f;
    system->sensor_data.gz = 0.0f;

    // 更新状态
    system->status.sensor_ok = 1;
    system->status.current_angle = system->sensor_data.pitch;
    system->status.current_angular_velocity = system->sensor_data.pitch_rate;

    system->last_sensor_time = current_time;
    system->sensor_loop_counter++;

    return HAL_OK;
}

static HAL_StatusTypeDef update_joystick_data(balance_car_system_t *system) {
    // 读取摇杆数据
    if (Joystick_ReadData(&system->joystick_data) != HAL_OK) {
        system->status.joystick_connected = 0;
        return HAL_ERROR;
    }

    // 更新状态
    system->status.joystick_connected = 1;
    system->status.joystick_x = system->joystick_data.raw_x;
    system->status.joystick_y = system->joystick_data.raw_y;
    system->status.joystick_button = system->joystick_data.button_pressed;

    return HAL_OK;
}

static HAL_StatusTypeDef process_control_loop(balance_car_system_t *system) {
    uint32_t current_time = HAL_GetTick();

    // 检查控制频率
    if (current_time - system->last_control_time < (1000.0f / system->config.control_frequency)) {
        return HAL_OK; // 还没到控制时间
    }

    float dt = (current_time - system->last_control_time) / 1000.0f;

    // 根据模式执行不同的控制逻辑
    switch (system->status.mode) {
        case SYSTEM_MODE_BALANCE_ONLY:
            // 纯平衡模式，不处理摇杆输入
            Balance_Update(&system->balance_controller, &system->sensor_data, dt);
            break;

        case SYSTEM_MODE_MANUAL_CONTROL:
            // 手动控制模式，处理摇杆输入
            Motion_ProcessJoystickInput(&system->motion_controller, &system->joystick_data);
            Motion_Update(&system->motion_controller, &system->motion_command);

            // 融合运动控制和平衡控制
            Balance_UpdateWithMotion(&system->balance_controller,
                                   &system->sensor_data,
                                   &system->motion_command,
                                   dt);
            break;

        case SYSTEM_MODE_AUTO_CONTROL:
            // 自动控制模式 (暂时和手动模式相同)
            Motion_ProcessJoystickInput(&system->motion_controller, &system->joystick_data);
            Motion_Update(&system->motion_controller, &system->motion_command);
            Balance_UpdateWithMotion(&system->balance_controller,
                                   &system->sensor_data,
                                   &system->motion_command,
                                   dt);
            break;

        case SYSTEM_MODE_STANDBY:
        case SYSTEM_MODE_CALIBRATION:
        case SYSTEM_MODE_EMERGENCY:
        default:
            // 其他模式不执行控制
            break;
    }

    // 更新电机输出状态
    Balance_GetEnhancedDebugInfo(&system->balance_controller,
                               &system->status.left_motor_output,
                               &system->status.right_motor_output,
                               NULL, NULL, NULL, NULL);

    system->last_control_time = current_time;
    system->control_loop_counter++;

    return HAL_OK;
}

static HAL_StatusTypeDef check_safety_conditions(balance_car_system_t *system) {
    if (!system->config.enable_safety_check) {
        return HAL_OK;
    }

    // 检查角度限制
    if (fabsf(system->status.current_angle) > 45.0f) {
        system->status.error_code |= SYSTEM_ERROR_ANGLE_LIMIT;
        return HAL_ERROR;
    }

    // 检查传感器超时
    uint32_t sensor_timeout = HAL_GetTick() - system->last_sensor_time;
    if (sensor_timeout > 100) { // 100ms超时
        system->status.error_code |= SYSTEM_ERROR_TIMEOUT;
        return HAL_ERROR;
    }

    // 检查紧急停止标志
    if (system->emergency_stop_flag) {
        return HAL_ERROR;
    }

    return HAL_OK;
}

static void update_system_status(balance_car_system_t *system) {
    // 更新平衡和运动状态
    system->status.balance_active = (Balance_GetState(&system->balance_controller) == BALANCE_STATE_BALANCING);
    system->status.motion_active = (system->motion_controller.mode != MOTION_MODE_STOP);

    // 更新目标值
    if (system->status.motion_active) {
        system->status.target_tilt = system->motion_command.target_tilt_angle;
        system->status.turn_rate = system->motion_command.turn_rate;
    } else {
        system->status.target_tilt = 0.0f;
        system->status.turn_rate = 0.0f;
    }

    // 更新系统状态
    if (system->status.error_code != SYSTEM_ERROR_NONE) {
        system->status.state = SYSTEM_STATE_ERROR;
    } else if (system->status.balance_active || system->status.motion_active) {
        system->status.state = SYSTEM_STATE_RUNNING;
    } else {
        system->status.state = SYSTEM_STATE_STOPPED;
    }
}

static void handle_mode_transitions(balance_car_system_t *system) {
    // 处理摇杆按键切换模式
    if (system->joystick_data.button_pressed) {
        // 长按切换模式
        switch (system->status.mode) {
            case SYSTEM_MODE_STANDBY:
                BalanceCarSystem_SetMode(system, SYSTEM_MODE_BALANCE_ONLY);
                break;
            case SYSTEM_MODE_BALANCE_ONLY:
                BalanceCarSystem_SetMode(system, SYSTEM_MODE_MANUAL_CONTROL);
                break;
            case SYSTEM_MODE_MANUAL_CONTROL:
                BalanceCarSystem_SetMode(system, SYSTEM_MODE_STANDBY);
                break;
            default:
                break;
        }
    }
}

static void print_debug_info(balance_car_system_t *system) {
    uint32_t current_time = HAL_GetTick();

    if (current_time - system->last_debug_time >= system->debug_print_interval) {
        printf("=== Balance Car Debug Info ===\n");
        printf("Mode: %d, State: %d\n", system->status.mode, system->status.state);
        printf("Angle: %.2f°, AngVel: %.2f°/s\n",
               system->status.current_angle, system->status.current_angular_velocity);
        printf("Joystick: X=%d, Y=%d, Btn=%d\n",
               system->status.joystick_x, system->status.joystick_y, system->status.joystick_button);
        printf("Motors: L=%d, R=%d\n",
               system->status.left_motor_output, system->status.right_motor_output);
        printf("Target: Tilt=%.2f°, Turn=%.2f\n",
               system->status.target_tilt, system->status.turn_rate);
        printf("Loops: Ctrl=%lu, Sensor=%lu\n",
               system->control_loop_counter, system->sensor_loop_counter);
        printf("Errors: 0x%08lX\n", system->status.error_code);
        printf("==============================\n\n");

        system->last_debug_time = current_time;
    }
}
