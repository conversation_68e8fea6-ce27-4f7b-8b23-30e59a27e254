/**
 ******************************************************************************
 * @file           : balance_car_tuning.c
 * @brief          : 平衡车参数调优和测试工具实现
 * @version        : v1.0
 * @date           : 2025-01-27
 ******************************************************************************
 */

#include "balance_car_tuning.h"
#include <math.h>
#include <string.h>
#include <stdio.h>
#include <stdlib.h>

// 内部函数声明
static float calculate_pid_score(balance_car_tuner_t *tuner, test_result_t *result);
static float calculate_motion_score(balance_car_tuner_t *tuner, test_result_t *result);
static void generate_next_pid_params(balance_car_tuner_t *tuner);
static void generate_next_motion_params(balance_car_tuner_t *tuner);
static void record_angle_data(balance_car_tuner_t *tuner, float angle);
static void analyze_test_data(balance_car_tuner_t *tuner, test_result_t *result);
static float random_float(float min, float max);

HAL_StatusTypeDef BalanceCarTuner_Init(balance_car_tuner_t *tuner, 
                                      balance_car_system_t *system,
                                      tuning_config_t *config) {
    if (!tuner || !system || !config) {
        return HAL_ERROR;
    }
    
    // 保存配置和系统引用
    tuner->config = *config;
    tuner->system = system;
    
    // 初始化状态
    tuner->state = TUNING_STATE_IDLE;
    tuner->current_iteration = 0;
    tuner->best_score = 0.0f;
    
    // 初始化数据记录
    tuner->history_index = 0;
    tuner->history_count = 0;
    memset(tuner->angle_history, 0, sizeof(tuner->angle_history));
    
    // 获取推荐的初始参数
    BalanceCarTuner_GetRecommendedParams(&tuner->current_pid, &tuner->current_motion);
    tuner->best_pid = tuner->current_pid;
    tuner->best_motion = tuner->current_motion;
    
    return HAL_OK;
}

HAL_StatusTypeDef BalanceCarTuner_Start(balance_car_tuner_t *tuner, tuning_mode_t mode) {
    if (!tuner || tuner->state == TUNING_STATE_RUNNING) {
        return HAL_ERROR;
    }
    
    // 设置调优模式
    tuner->config.mode = mode;
    tuner->state = TUNING_STATE_RUNNING;
    tuner->current_iteration = 0;
    tuner->test_start_time = HAL_GetTick();
    
    // 重置数据记录
    tuner->history_index = 0;
    tuner->history_count = 0;
    
    printf("开始调优，模式: %d\n", mode);
    
    return HAL_OK;
}

HAL_StatusTypeDef BalanceCarTuner_Stop(balance_car_tuner_t *tuner) {
    if (!tuner) {
        return HAL_ERROR;
    }
    
    tuner->state = TUNING_STATE_ABORTED;
    
    // 应用最佳参数
    if (tuner->best_score > 0.0f) {
        BalanceCarTuner_SetPIDParams(tuner, &tuner->best_pid);
        BalanceCarTuner_SetMotionParams(tuner, &tuner->best_motion);
        printf("应用最佳参数，评分: %.2f\n", tuner->best_score);
    }
    
    return HAL_OK;
}

HAL_StatusTypeDef BalanceCarTuner_Update(balance_car_tuner_t *tuner) {
    if (!tuner || tuner->state != TUNING_STATE_RUNNING) {
        return HAL_OK;
    }
    
    uint32_t current_time = HAL_GetTick();
    uint32_t elapsed_time = current_time - tuner->test_start_time;
    
    // 记录当前角度数据
    record_angle_data(tuner, tuner->system->status.current_angle);
    
    // 检查是否完成当前测试
    if (elapsed_time >= tuner->config.test_duration_ms) {
        // 分析测试数据
        analyze_test_data(tuner, &tuner->current_result);
        
        // 计算评分
        float score = 0.0f;
        switch (tuner->config.mode) {
            case TUNING_MODE_AUTO_BALANCE:
                score = calculate_pid_score(tuner, &tuner->current_result);
                break;
            case TUNING_MODE_AUTO_MOTION:
                score = calculate_motion_score(tuner, &tuner->current_result);
                break;
            default:
                score = (calculate_pid_score(tuner, &tuner->current_result) + 
                        calculate_motion_score(tuner, &tuner->current_result)) / 2.0f;
                break;
        }
        
        printf("迭代 %lu: 评分 %.2f\n", tuner->current_iteration + 1, score);
        
        // 更新最佳参数
        if (score > tuner->best_score) {
            tuner->best_score = score;
            tuner->best_pid = tuner->current_pid;
            tuner->best_motion = tuner->current_motion;
            tuner->best_result = tuner->current_result;
            
            printf("发现更好参数！评分: %.2f\n", score);
            
            if (tuner->config.enable_auto_save) {
                BalanceCarTuner_SaveBestParams(tuner);
            }
        }
        
        // 检查是否完成调优
        tuner->current_iteration++;
        if (tuner->current_iteration >= tuner->config.max_iterations || 
            score >= tuner->config.target_score) {
            tuner->state = TUNING_STATE_COMPLETED;
            printf("调优完成！最佳评分: %.2f\n", tuner->best_score);
            
            // 应用最佳参数
            BalanceCarTuner_SetPIDParams(tuner, &tuner->best_pid);
            BalanceCarTuner_SetMotionParams(tuner, &tuner->best_motion);
            
            return HAL_OK;
        }
        
        // 生成下一组参数
        switch (tuner->config.mode) {
            case TUNING_MODE_AUTO_BALANCE:
                generate_next_pid_params(tuner);
                break;
            case TUNING_MODE_AUTO_MOTION:
                generate_next_motion_params(tuner);
                break;
            default:
                generate_next_pid_params(tuner);
                generate_next_motion_params(tuner);
                break;
        }
        
        // 应用新参数并开始下一次测试
        BalanceCarTuner_SetPIDParams(tuner, &tuner->current_pid);
        BalanceCarTuner_SetMotionParams(tuner, &tuner->current_motion);
        
        // 重置测试
        tuner->test_start_time = current_time;
        tuner->history_index = 0;
        tuner->history_count = 0;
    }
    
    return HAL_OK;
}

HAL_StatusTypeDef BalanceCarTuner_SetPIDParams(balance_car_tuner_t *tuner, 
                                              pid_parameter_set_t *pid_params) {
    if (!tuner || !pid_params || !tuner->system) {
        return HAL_ERROR;
    }
    
    // 应用PID参数到平衡控制器
    Balance_SetPIDParams(&tuner->system->balance_controller,
                        pid_params->angle_kp, pid_params->angle_kd,
                        pid_params->speed_kp, pid_params->speed_ki);
    
    // 保存当前参数
    tuner->current_pid = *pid_params;
    
    return HAL_OK;
}

HAL_StatusTypeDef BalanceCarTuner_SetMotionParams(balance_car_tuner_t *tuner,
                                                 motion_parameter_set_t *motion_params) {
    if (!tuner || !motion_params || !tuner->system) {
        return HAL_ERROR;
    }
    
    // 应用运动参数到运动控制器
    Motion_SetMaxTiltAngle(&tuner->system->motion_controller, motion_params->max_tilt_angle);
    Motion_SetMaxTurnRate(&tuner->system->motion_controller, motion_params->max_turn_rate);
    Motion_SetAccelerationLimit(&tuner->system->motion_controller, motion_params->acceleration_limit);
    Motion_SetResponseSpeed(&tuner->system->motion_controller, motion_params->response_speed);
    
    // 保存当前参数
    tuner->current_motion = *motion_params;
    
    return HAL_OK;
}

HAL_StatusTypeDef BalanceCarTuner_RunSingleTest(balance_car_tuner_t *tuner,
                                               uint32_t duration_ms,
                                               test_result_t *result) {
    if (!tuner || !result) {
        return HAL_ERROR;
    }
    
    // 重置数据记录
    tuner->history_index = 0;
    tuner->history_count = 0;
    
    uint32_t start_time = HAL_GetTick();
    uint32_t current_time;
    
    // 运行测试
    do {
        // 更新系统
        BalanceCarSystem_Update(tuner->system);
        
        // 记录数据
        record_angle_data(tuner, tuner->system->status.current_angle);
        
        current_time = HAL_GetTick();
        HAL_Delay(5); // 200Hz更新频率
        
    } while (current_time - start_time < duration_ms);
    
    // 分析测试数据
    analyze_test_data(tuner, result);
    
    return HAL_OK;
}

HAL_StatusTypeDef BalanceCarTuner_GetProgress(balance_car_tuner_t *tuner,
                                             uint8_t *progress,
                                             uint32_t *eta_seconds) {
    if (!tuner || !progress || !eta_seconds) {
        return HAL_ERROR;
    }
    
    if (tuner->state != TUNING_STATE_RUNNING) {
        *progress = 100;
        *eta_seconds = 0;
        return HAL_OK;
    }
    
    // 计算进度
    *progress = (tuner->current_iteration * 100) / tuner->config.max_iterations;
    
    // 估算剩余时间
    uint32_t elapsed_total = HAL_GetTick() - tuner->test_start_time;
    uint32_t avg_iteration_time = elapsed_total / (tuner->current_iteration + 1);
    uint32_t remaining_iterations = tuner->config.max_iterations - tuner->current_iteration;
    *eta_seconds = (remaining_iterations * avg_iteration_time) / 1000;
    
    return HAL_OK;
}

void BalanceCarTuner_GetRecommendedParams(pid_parameter_set_t *pid_params,
                                         motion_parameter_set_t *motion_params) {
    if (pid_params) {
        // 推荐的PID参数 (基于经验值)
        pid_params->angle_kp = 80.0f;
        pid_params->angle_ki = 0.0f;
        pid_params->angle_kd = 3.0f;

        pid_params->speed_kp = 0.8f;
        pid_params->speed_ki = 0.1f;
        pid_params->speed_kd = 0.0f;

        pid_params->turn_kp = 2.5f;
        pid_params->turn_ki = 0.0f;
        pid_params->turn_kd = 0.0f;

        pid_params->stability_score = 0.0f;
        pid_params->response_score = 0.0f;
        pid_params->overall_score = 0.0f;
    }

    if (motion_params) {
        // 推荐的运动参数
        motion_params->max_tilt_angle = 15.0f;
        motion_params->max_turn_rate = 5.0f;
        motion_params->acceleration_limit = 10.0f;
        motion_params->deceleration_limit = 15.0f;
        motion_params->response_speed = 2.0f;

        motion_params->smoothness_score = 0.0f;
        motion_params->accuracy_score = 0.0f;
        motion_params->overall_score = 0.0f;
    }
}

// ========== 内部函数实现 ==========

static float calculate_pid_score(balance_car_tuner_t *tuner, test_result_t *result) {
    float stability_score = 0.0f;
    float response_score = 0.0f;

    // 稳定性评分 (基于角度偏差和振荡)
    if (result->max_angle_deviation < 2.0f) {
        stability_score += 40.0f;
    } else if (result->max_angle_deviation < 5.0f) {
        stability_score += 30.0f;
    } else if (result->max_angle_deviation < 10.0f) {
        stability_score += 20.0f;
    } else {
        stability_score += 10.0f;
    }

    // 振荡评分
    if (result->max_oscillation < 1.0f) {
        stability_score += 30.0f;
    } else if (result->max_oscillation < 2.0f) {
        stability_score += 20.0f;
    } else if (result->max_oscillation < 5.0f) {
        stability_score += 10.0f;
    }

    // 响应性评分 (基于稳定时间)
    if (result->settling_time_ms < 1000.0f) {
        response_score += 30.0f;
    } else if (result->settling_time_ms < 2000.0f) {
        response_score += 20.0f;
    } else if (result->settling_time_ms < 5000.0f) {
        response_score += 10.0f;
    }

    // 安全性评分
    if (result->fall_count == 0) {
        response_score += 20.0f;
    } else if (result->fall_count < 3) {
        response_score += 10.0f;
    }

    return stability_score + response_score;
}

static float calculate_motion_score(balance_car_tuner_t *tuner, test_result_t *result) {
    float smoothness_score = 0.0f;
    float accuracy_score = 0.0f;

    // 平滑性评分 (基于振荡和稳定时间)
    if (result->max_oscillation < 1.0f) {
        smoothness_score += 35.0f;
    } else if (result->max_oscillation < 2.0f) {
        smoothness_score += 25.0f;
    } else if (result->max_oscillation < 3.0f) {
        smoothness_score += 15.0f;
    }

    // 精确性评分 (基于平均偏差)
    if (result->avg_angle_deviation < 1.0f) {
        accuracy_score += 35.0f;
    } else if (result->avg_angle_deviation < 2.0f) {
        accuracy_score += 25.0f;
    } else if (result->avg_angle_deviation < 5.0f) {
        accuracy_score += 15.0f;
    }

    // 安全性评分
    if (result->safety_violations == 0) {
        accuracy_score += 30.0f;
    } else if (result->safety_violations < 3) {
        accuracy_score += 15.0f;
    }

    return smoothness_score + accuracy_score;
}

static void generate_next_pid_params(balance_car_tuner_t *tuner) {
    // 简化的随机搜索算法
    // 在最佳参数附近生成新参数

    float variation = 0.1f; // 10%变化范围

    if (tuner->best_score > 0.0f) {
        // 基于最佳参数生成
        tuner->current_pid.angle_kp = tuner->best_pid.angle_kp * (1.0f + random_float(-variation, variation));
        tuner->current_pid.angle_kd = tuner->best_pid.angle_kd * (1.0f + random_float(-variation, variation));
        tuner->current_pid.speed_kp = tuner->best_pid.speed_kp * (1.0f + random_float(-variation, variation));
        tuner->current_pid.speed_ki = tuner->best_pid.speed_ki * (1.0f + random_float(-variation, variation));
    } else {
        // 随机生成
        tuner->current_pid.angle_kp = random_float(ANGLE_KP_MIN, ANGLE_KP_MAX);
        tuner->current_pid.angle_kd = random_float(ANGLE_KD_MIN, ANGLE_KD_MAX);
        tuner->current_pid.speed_kp = random_float(SPEED_KP_MIN, SPEED_KP_MAX);
        tuner->current_pid.speed_ki = random_float(SPEED_KI_MIN, SPEED_KI_MAX);
    }

    // 限制参数范围
    if (tuner->current_pid.angle_kp < ANGLE_KP_MIN) tuner->current_pid.angle_kp = ANGLE_KP_MIN;
    if (tuner->current_pid.angle_kp > ANGLE_KP_MAX) tuner->current_pid.angle_kp = ANGLE_KP_MAX;
    if (tuner->current_pid.angle_kd < ANGLE_KD_MIN) tuner->current_pid.angle_kd = ANGLE_KD_MIN;
    if (tuner->current_pid.angle_kd > ANGLE_KD_MAX) tuner->current_pid.angle_kd = ANGLE_KD_MAX;
}

static void generate_next_motion_params(balance_car_tuner_t *tuner) {
    float variation = 0.15f; // 15%变化范围

    if (tuner->best_score > 0.0f) {
        // 基于最佳参数生成
        tuner->current_motion.max_tilt_angle = tuner->best_motion.max_tilt_angle * (1.0f + random_float(-variation, variation));
        tuner->current_motion.max_turn_rate = tuner->best_motion.max_turn_rate * (1.0f + random_float(-variation, variation));
        tuner->current_motion.response_speed = tuner->best_motion.response_speed * (1.0f + random_float(-variation, variation));
    } else {
        // 使用推荐参数作为基础
        tuner->current_motion.max_tilt_angle = 15.0f * (1.0f + random_float(-variation, variation));
        tuner->current_motion.max_turn_rate = 5.0f * (1.0f + random_float(-variation, variation));
        tuner->current_motion.response_speed = 2.0f * (1.0f + random_float(-variation, variation));
    }

    // 限制参数范围
    if (tuner->current_motion.max_tilt_angle < 5.0f) tuner->current_motion.max_tilt_angle = 5.0f;
    if (tuner->current_motion.max_tilt_angle > 25.0f) tuner->current_motion.max_tilt_angle = 25.0f;
    if (tuner->current_motion.max_turn_rate < 1.0f) tuner->current_motion.max_turn_rate = 1.0f;
    if (tuner->current_motion.max_turn_rate > 10.0f) tuner->current_motion.max_turn_rate = 10.0f;
}

static void record_angle_data(balance_car_tuner_t *tuner, float angle) {
    if (tuner->history_count < 1000) {
        tuner->angle_history[tuner->history_index] = angle;
        tuner->history_index = (tuner->history_index + 1) % 1000;
        tuner->history_count++;
    } else {
        tuner->angle_history[tuner->history_index] = angle;
        tuner->history_index = (tuner->history_index + 1) % 1000;
    }
}

static void analyze_test_data(balance_car_tuner_t *tuner, test_result_t *result) {
    if (tuner->history_count == 0) {
        memset(result, 0, sizeof(test_result_t));
        return;
    }

    float sum = 0.0f;
    float max_deviation = 0.0f;
    float max_oscillation = 0.0f;
    uint32_t fall_count = 0;
    uint32_t stable_samples = 0;

    // 分析角度数据
    for (uint16_t i = 0; i < tuner->history_count; i++) {
        float angle = tuner->angle_history[i];
        float abs_angle = fabsf(angle);

        sum += abs_angle;

        if (abs_angle > max_deviation) {
            max_deviation = abs_angle;
        }

        if (abs_angle > 45.0f) {
            fall_count++;
        }

        if (abs_angle < 2.0f) {
            stable_samples++;
        }

        // 计算振荡 (相邻样本的差值)
        if (i > 0) {
            float oscillation = fabsf(angle - tuner->angle_history[i-1]);
            if (oscillation > max_oscillation) {
                max_oscillation = oscillation;
            }
        }
    }

    // 填充结果
    result->test_duration_ms = tuner->config.test_duration_ms;
    result->stable_time_ms = (stable_samples * tuner->config.test_duration_ms) / tuner->history_count;
    result->max_angle_deviation = max_deviation;
    result->avg_angle_deviation = sum / tuner->history_count;
    result->max_oscillation = max_oscillation;
    result->settling_time_ms = tuner->config.test_duration_ms - result->stable_time_ms;
    result->fall_count = fall_count;
    result->safety_violations = (fall_count > 0) ? 1 : 0;
}

static float random_float(float min, float max) {
    return min + ((float)rand() / RAND_MAX) * (max - min);
}
