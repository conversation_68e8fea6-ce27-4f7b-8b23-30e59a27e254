/**
 ******************************************************************************
 * @file           : balance_controller.c
 * @brief          : 平衡车控制器实现
 * @version        : v1.0
 * @date           : 2025-01-27
 ******************************************************************************
 * @description    :
 * 三环PID平衡车控制算法实现：
 * 
 * 控制流程：
 * 1. 速度环：目标速度 → 角度修正量
 * 2. 角度环：(目标角度+角度修正) → 基础电机输出
 * 3. 转向环：转向指令 → 左右差速
 * 4. 电机输出：基础输出 ± 差速 → 左右电机PWM
 * 
 * 安全机制：
 * - 角度保护：超过阈值自动停机
 * - 启动保护：必须在平衡位置才能启动
 * - 时间保护：连续平衡一定时间才认为稳定
 ******************************************************************************
 */

#include "balance_controller.h"
#include "app_config.h"
#include <math.h>

// 默认PID参数 (需要根据实际车辆调整)
#define DEFAULT_ANGLE_KP        80.0f       // 角度环P参数
#define DEFAULT_ANGLE_KI        0.0f        // 角度环I参数(通常为0)
#define DEFAULT_ANGLE_KD        3.0f        // 角度环D参数

#define DEFAULT_SPEED_KP        0.8f        // 速度环P参数
#define DEFAULT_SPEED_KI        0.1f        // 速度环I参数
#define DEFAULT_SPEED_KD        0.0f        // 速度环D参数

#define DEFAULT_TURN_KP         2.5f        // 转向环P参数
#define DEFAULT_TURN_KI         0.0f        // 转向环I参数
#define DEFAULT_TURN_KD         0.0f        // 转向环D参数

// 控制限制
#define MOTOR_MAX_PWM           1000        // 电机最大PWM值
#define SPEED_ANGLE_LIMIT       15.0f       // 速度环角度修正限制(°)
#define TURN_SPEED_LIMIT        300         // 转向差速限制

// 安全参数
#define MAX_BALANCE_ANGLE       45.0f       // 最大平衡角度(°)
#define MIN_BALANCE_TIME_MS     1000        // 最小平衡时间(ms)
#define STARTUP_ANGLE_LIMIT     5.0f        // 启动角度限制(°)

void Balance_Init(balance_controller_t *controller, motor_interface_t motors) {
    // 初始化PID控制器
    PID_Init(&controller->angle_pid, DEFAULT_ANGLE_KP, DEFAULT_ANGLE_KI, DEFAULT_ANGLE_KD, MOTOR_MAX_PWM);
    PID_Init(&controller->speed_pid, DEFAULT_SPEED_KP, DEFAULT_SPEED_KI, DEFAULT_SPEED_KD, SPEED_ANGLE_LIMIT);
    PID_Init(&controller->turn_pid, DEFAULT_TURN_KP, DEFAULT_TURN_KI, DEFAULT_TURN_KD, TURN_SPEED_LIMIT);
    
    // 设置默认目标
    controller->target_angle = 0.0f;
    controller->target_speed = 0.0f;
    controller->target_turn_rate = 0.0f;
    
    // 初始化状态
    controller->state = BALANCE_STATE_DISABLED;
    controller->current_speed = 0.0f;
    controller->balance_start_time = 0;
    
    // 设置电机接口
    controller->motors = motors;
    
    // 设置安全参数
    controller->max_angle = MAX_BALANCE_ANGLE;
    controller->min_balance_time_ms = MIN_BALANCE_TIME_MS;
    
    // 初始化调试信息
    controller->last_left_motor = 0;
    controller->last_right_motor = 0;
}

void Balance_Update(balance_controller_t *controller, mpu6050_data_t *sensor_data, float dt) {
    uint32_t current_time = HAL_GetTick();
    
    // 检查传感器数据有效性
    if (!sensor_data) {
        controller->state = BALANCE_STATE_FALLEN;
        controller->motors.set_motor_speed(0, 0);
        return;
    }
    
    // 状态机处理
    switch (controller->state) {
        case BALANCE_STATE_DISABLED:
            // 禁用状态，停止电机
            controller->motors.set_motor_speed(0, 0);
            return;
            
        case BALANCE_STATE_STANDBY:
            // 待机状态，检查是否可以开始平衡
            if (fabs(sensor_data->pitch) < STARTUP_ANGLE_LIMIT) {
                controller->state = BALANCE_STATE_BALANCING;
                controller->balance_start_time = current_time;
                // 重置PID控制器
                PID_Reset(&controller->angle_pid);
                PID_Reset(&controller->speed_pid);
                PID_Reset(&controller->turn_pid);
            } else {
                controller->motors.set_motor_speed(0, 0);
                return;
            }
            break;
            
        case BALANCE_STATE_BALANCING:
            // 检查是否倾倒
            if (fabs(sensor_data->pitch) > controller->max_angle) {
                controller->state = BALANCE_STATE_FALLEN;
                controller->motors.set_motor_speed(0, 0);
                return;
            }
            break;
            
        case BALANCE_STATE_FALLEN:
            // 倾倒状态，停止电机，等待重新启动
            controller->motors.set_motor_speed(0, 0);
            return;
    }
    
    // 执行平衡控制算法
    
    // 1. 速度环控制 - 根据目标速度调整平衡角度
    // 注意：这里应该使用实际速度反馈，暂时使用角速度估算
    float estimated_speed = sensor_data->pitch_rate * 2.0f; // 简化的速度估算
    PID_SetSetpoint(&controller->speed_pid, controller->target_speed);
    float speed_output = PID_Compute(&controller->speed_pid, estimated_speed, dt);
    
    // 2. 角度环控制 - 保持平衡
    float adjusted_target_angle = controller->target_angle + speed_output;
    PID_SetSetpoint(&controller->angle_pid, adjusted_target_angle);
    float angle_output = PID_Compute(&controller->angle_pid, sensor_data->pitch, dt);
    
    // 3. 转向环控制 - 左右转向
    PID_SetSetpoint(&controller->turn_pid, controller->target_turn_rate);
    float turn_output = PID_Compute(&controller->turn_pid, sensor_data->gz, dt);
    
    // 4. 计算最终电机输出
    int16_t base_motor_speed = (int16_t)angle_output;
    int16_t turn_differential = (int16_t)turn_output;
    
    controller->last_left_motor = base_motor_speed - turn_differential;
    controller->last_right_motor = base_motor_speed + turn_differential;
    
    // 电机输出限幅
    if (controller->last_left_motor > MOTOR_MAX_PWM) {
        controller->last_left_motor = MOTOR_MAX_PWM;
    } else if (controller->last_left_motor < -MOTOR_MAX_PWM) {
        controller->last_left_motor = -MOTOR_MAX_PWM;
    }
    
    if (controller->last_right_motor > MOTOR_MAX_PWM) {
        controller->last_right_motor = MOTOR_MAX_PWM;
    } else if (controller->last_right_motor < -MOTOR_MAX_PWM) {
        controller->last_right_motor = -MOTOR_MAX_PWM;
    }
    
    // 设置电机速度
    controller->motors.set_motor_speed(controller->last_left_motor, controller->last_right_motor);
    
    // 更新当前速度估计
    controller->current_speed = estimated_speed;
}

void Balance_SetTarget(balance_controller_t *controller, float angle, float speed, float turn_rate) {
    controller->target_angle = angle;
    controller->target_speed = speed;
    controller->target_turn_rate = turn_rate;
}

void Balance_Enable(balance_controller_t *controller, uint8_t enable) {
    if (enable && controller->state == BALANCE_STATE_DISABLED) {
        controller->state = BALANCE_STATE_STANDBY;
        controller->motors.enable_motors(1);
    } else if (!enable) {
        controller->state = BALANCE_STATE_DISABLED;
        controller->motors.enable_motors(0);
        controller->motors.set_motor_speed(0, 0);
        
        // 重置PID控制器
        PID_Reset(&controller->angle_pid);
        PID_Reset(&controller->speed_pid);
        PID_Reset(&controller->turn_pid);
    }
}

balance_state_t Balance_GetState(balance_controller_t *controller) {
    return controller->state;
}

void Balance_SetPIDParams(balance_controller_t *controller, 
                         float angle_kp, float angle_kd,
                         float speed_kp, float speed_ki) {
    PID_SetParams(&controller->angle_pid, angle_kp, 0.0f, angle_kd);
    PID_SetParams(&controller->speed_pid, speed_kp, speed_ki, 0.0f);
}

void Balance_GetDebugInfo(balance_controller_t *controller,
                         int16_t *left_motor, int16_t *right_motor,
                         float *angle_output, float *speed_output) {
    if (left_motor) *left_motor = controller->last_left_motor;
    if (right_motor) *right_motor = controller->last_right_motor;
    if (angle_output) *angle_output = controller->angle_pid.last_output;
    if (speed_output) *speed_output = controller->speed_pid.last_output;
}