#include "error_handler.h"
#include "app_config.h"
#include <stdio.h>

static const char* error_msgs[] = {
    "OK",
    "Init failed",
    "I2C error",
    "Sensor error",
    "Data invalid"
};

const char* get_error_msg(app_error_t error) {
    if (error < 5) return error_msgs[error];
    return "Unknown";
}

void print_error(app_error_t error) {
    #if DEBUG_ENABLE
    printf("ERROR: %s\r\n", get_error_msg(error));
    #endif
}