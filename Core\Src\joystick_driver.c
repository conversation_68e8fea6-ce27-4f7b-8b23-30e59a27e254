/**
 ******************************************************************************
 * @file           : joystick_driver.c
 * @brief          : 十字摇杆驱动实现
 * @version        : v1.0
 * @date           : 2025-01-27
 ******************************************************************************
 * @description    :
 * 十字摇杆驱动实现，包含：
 * - ADC多通道读取
 * - 数字滤波算法
 * - 死区处理
 * - 按键防抖
 * - 校准功能
 ******************************************************************************
 */

#include "joystick_driver.h"
#include <math.h>
#include <string.h>
#include <stdlib.h>
#include <stdio.h>

// 静态变量
static joystick_config_t joystick_cfg;
static joystick_calibration_t calibration_data;
static joystick_data_t last_data;

// 滤波缓冲区
#define FILTER_BUFFER_SIZE 8
static int16_t x_filter_buffer[FILTER_BUFFER_SIZE];
static int16_t y_filter_buffer[FILTER_BUFFER_SIZE];
static uint8_t filter_index = 0;

// 按键状态
static uint8_t button_state = 0;
static uint8_t last_button_state = 0;
static uint32_t button_press_time = 0;
static uint32_t button_debounce_time = 50; // 50ms防抖

// 校准状态
static uint8_t calibration_mode = 0;
static uint32_t calibration_samples = 0;
static uint32_t calibration_sum_x = 0;
static uint32_t calibration_sum_y = 0;

// 内部函数声明
static uint16_t read_adc_channel(uint32_t channel);
static int16_t apply_deadzone(int16_t value, uint16_t deadzone);
static int16_t apply_filter(int16_t new_value, int16_t *buffer);
static void update_button_state(void);
static int16_t map_adc_to_output(uint16_t adc_value, uint16_t min_val, uint16_t center_val, uint16_t max_val);

HAL_StatusTypeDef Joystick_Init(joystick_config_t *config) {
    if (!config || !config->hadc) {
        return HAL_ERROR;
    }
    
    // 保存配置
    joystick_cfg = *config;
    
    // 初始化校准数据为默认值
    calibration_data.x_center = JOYSTICK_ADC_RESOLUTION / 2;
    calibration_data.y_center = JOYSTICK_ADC_RESOLUTION / 2;
    calibration_data.x_min = 0;
    calibration_data.x_max = JOYSTICK_ADC_RESOLUTION - 1;
    calibration_data.y_min = 0;
    calibration_data.y_max = JOYSTICK_ADC_RESOLUTION - 1;
    calibration_data.is_calibrated = 0;
    
    // 初始化滤波缓冲区
    memset(x_filter_buffer, 0, sizeof(x_filter_buffer));
    memset(y_filter_buffer, 0, sizeof(y_filter_buffer));
    filter_index = 0;
    
    // 初始化按键状态
    button_state = 0;
    last_button_state = 0;
    button_press_time = 0;
    
    // 初始化数据结构
    memset(&last_data, 0, sizeof(last_data));
    
    return HAL_OK;
}

HAL_StatusTypeDef Joystick_ReadData(joystick_data_t *data) {
    if (!data) {
        return HAL_ERROR;
    }
    
    // 读取ADC原始值
    data->raw_x = read_adc_channel(joystick_cfg.x_channel);
    data->raw_y = read_adc_channel(joystick_cfg.y_channel);
    
    // 映射到输出范围
    int16_t x_mapped = map_adc_to_output(data->raw_x, 
                                        calibration_data.x_min,
                                        calibration_data.x_center,
                                        calibration_data.x_max);
    int16_t y_mapped = map_adc_to_output(data->raw_y,
                                        calibration_data.y_min,
                                        calibration_data.y_center,
                                        calibration_data.y_max);
    
    // 应用轴反向
    if (joystick_cfg.invert_x) {
        x_mapped = -x_mapped;
    }
    if (joystick_cfg.invert_y) {
        y_mapped = -y_mapped;
    }
    
    // 应用滤波
    if (joystick_cfg.filter_strength > 0) {
        x_mapped = apply_filter(x_mapped, x_filter_buffer);
        y_mapped = apply_filter(y_mapped, y_filter_buffer);
    }
    
    // 应用死区
    data->x_axis = apply_deadzone(x_mapped, joystick_cfg.deadzone);
    data->y_axis = apply_deadzone(y_mapped, joystick_cfg.deadzone);
    
    // 更新按键状态
    update_button_state();
    data->button_pressed = button_state;
    data->button_clicked = (button_state && !last_button_state) ? 1 : 0;
    last_button_state = button_state;
    
    // 设置状态信息
    data->is_valid = 1;
    data->timestamp = HAL_GetTick();
    
    // 保存为上次数据
    last_data = *data;
    
    return HAL_OK;
}

joystick_direction_t Joystick_GetDirection(joystick_data_t *data) {
    if (!data || !data->is_valid) {
        return JOYSTICK_DIR_CENTER;
    }
    
    // 定义方向阈值
    const int16_t threshold = 300;
    
    int16_t x = data->x_axis;
    int16_t y = data->y_axis;
    
    // 判断主要方向
    if (abs(x) < threshold && abs(y) < threshold) {
        return JOYSTICK_DIR_CENTER;
    }
    
    // 8方向判断
    if (y > threshold) {  // 向前
        if (x > threshold) {
            return JOYSTICK_DIR_UP_RIGHT;
        } else if (x < -threshold) {
            return JOYSTICK_DIR_UP_LEFT;
        } else {
            return JOYSTICK_DIR_UP;
        }
    } else if (y < -threshold) {  // 向后
        if (x > threshold) {
            return JOYSTICK_DIR_DOWN_RIGHT;
        } else if (x < -threshold) {
            return JOYSTICK_DIR_DOWN_LEFT;
        } else {
            return JOYSTICK_DIR_DOWN;
        }
    } else {  // 左右
        if (x > threshold) {
            return JOYSTICK_DIR_RIGHT;
        } else {
            return JOYSTICK_DIR_LEFT;
        }
    }
}

HAL_StatusTypeDef Joystick_StartCalibration(void) {
    calibration_mode = 1;
    calibration_samples = 0;
    calibration_sum_x = 0;
    calibration_sum_y = 0;
    
    // 重置校准数据
    calibration_data.x_min = JOYSTICK_ADC_RESOLUTION;
    calibration_data.x_max = 0;
    calibration_data.y_min = JOYSTICK_ADC_RESOLUTION;
    calibration_data.y_max = 0;
    calibration_data.is_calibrated = 0;
    
    return HAL_OK;
}

HAL_StatusTypeDef Joystick_FinishCalibration(void) {
    if (!calibration_mode || calibration_samples < 100) {
        return HAL_ERROR;
    }
    
    // 计算中心值
    calibration_data.x_center = calibration_sum_x / calibration_samples;
    calibration_data.y_center = calibration_sum_y / calibration_samples;
    calibration_data.is_calibrated = 1;
    calibration_mode = 0;
    
    return HAL_OK;
}

void Joystick_SetDeadzone(uint16_t deadzone) {
    if (deadzone <= 500) {
        joystick_cfg.deadzone = deadzone;
    }
}

void Joystick_SetFilterStrength(uint8_t strength) {
    if (strength <= 10) {
        joystick_cfg.filter_strength = strength;
    }
}

void Joystick_SetAxisInvert(uint8_t invert_x, uint8_t invert_y) {
    joystick_cfg.invert_x = invert_x;
    joystick_cfg.invert_y = invert_y;
}

uint8_t Joystick_SelfTest(void) {
    joystick_data_t test_data;
    
    // 读取数据测试
    if (Joystick_ReadData(&test_data) != HAL_OK) {
        return 0;
    }
    
    // 检查ADC值是否在合理范围内
    if (test_data.raw_x > JOYSTICK_ADC_RESOLUTION || 
        test_data.raw_y > JOYSTICK_ADC_RESOLUTION) {
        return 0;
    }
    
    // 检查按键功能
    uint8_t initial_button = test_data.button_pressed;
    HAL_Delay(10);
    Joystick_ReadData(&test_data);
    
    return 1; // 自检通过
}

// 平衡车专用接口实现

int16_t Joystick_GetForwardCommand(joystick_data_t *data) {
    if (!data || !data->is_valid) {
        return 0;
    }
    
    // Y轴正方向为前进
    return data->y_axis;
}

int16_t Joystick_GetTurnCommand(joystick_data_t *data) {
    if (!data || !data->is_valid) {
        return 0;
    }
    
    // X轴正方向为右转
    return data->x_axis;
}

uint16_t Joystick_GetMovementIntensity(joystick_data_t *data) {
    if (!data || !data->is_valid) {
        return 0;
    }
    
    // 计算向量长度
    float intensity = sqrtf(data->x_axis * data->x_axis + data->y_axis * data->y_axis);
    
    // 限制在0-1000范围内
    if (intensity > 1000.0f) {
        intensity = 1000.0f;
    }
    
    return (uint16_t)intensity;
}

uint8_t Joystick_IsInDeadzone(joystick_data_t *data) {
    if (!data || !data->is_valid) {
        return 1;
    }
    
    return (abs(data->x_axis) < joystick_cfg.deadzone && 
            abs(data->y_axis) < joystick_cfg.deadzone);
}

// 内部函数实现

static uint16_t read_adc_channel(uint32_t channel) {
    ADC_ChannelConfTypeDef sConfig = {0};
    
    sConfig.Channel = channel;
    sConfig.Rank = 1;
    sConfig.SamplingTime = ADC_SAMPLETIME_3CYCLES;
    
    if (HAL_ADC_ConfigChannel(joystick_cfg.hadc, &sConfig) != HAL_OK) {
        return 0;
    }
    
    HAL_ADC_Start(joystick_cfg.hadc);
    if (HAL_ADC_PollForConversion(joystick_cfg.hadc, 10) == HAL_OK) {
        uint16_t value = HAL_ADC_GetValue(joystick_cfg.hadc);
        HAL_ADC_Stop(joystick_cfg.hadc);
        
        // 校准模式下收集数据
        if (calibration_mode) {
            calibration_samples++;
            calibration_sum_x += (channel == joystick_cfg.x_channel) ? value : 0;
            calibration_sum_y += (channel == joystick_cfg.y_channel) ? value : 0;
            
            // 更新最值
            if (channel == joystick_cfg.x_channel) {
                if (value < calibration_data.x_min) calibration_data.x_min = value;
                if (value > calibration_data.x_max) calibration_data.x_max = value;
            } else if (channel == joystick_cfg.y_channel) {
                if (value < calibration_data.y_min) calibration_data.y_min = value;
                if (value > calibration_data.y_max) calibration_data.y_max = value;
            }
        }
        
        return value;
    }
    
    return 0;
}

static int16_t apply_deadzone(int16_t value, uint16_t deadzone) {
    if (abs(value) < deadzone) {
        return 0;
    }

    // 线性映射，去除死区后重新映射到全范围
    if (value > 0) {
        return (int16_t)(((value - deadzone) * JOYSTICK_OUTPUT_RANGE) / (JOYSTICK_OUTPUT_RANGE - deadzone));
    } else {
        return (int16_t)(((value + deadzone) * JOYSTICK_OUTPUT_RANGE) / (JOYSTICK_OUTPUT_RANGE - deadzone));
    }
}

static int16_t apply_filter(int16_t new_value, int16_t *buffer) {
    // 移动平均滤波
    buffer[filter_index] = new_value;
    filter_index = (filter_index + 1) % FILTER_BUFFER_SIZE;

    int32_t sum = 0;
    for (int i = 0; i < FILTER_BUFFER_SIZE; i++) {
        sum += buffer[i];
    }

    return (int16_t)(sum / FILTER_BUFFER_SIZE);
}

static void update_button_state(void) {
    uint8_t current_state = !HAL_GPIO_ReadPin(joystick_cfg.button_port, joystick_cfg.button_pin);
    uint32_t current_time = HAL_GetTick();

    // 防抖处理
    if (current_state != button_state) {
        if (current_time - button_press_time > button_debounce_time) {
            button_state = current_state;
            button_press_time = current_time;
        }
    }
}

static int16_t map_adc_to_output(uint16_t adc_value, uint16_t min_val, uint16_t center_val, uint16_t max_val) {
    int16_t result;

    if (adc_value <= center_val) {
        // 负半轴映射
        if (center_val > min_val) {
            result = (int16_t)(((int32_t)(adc_value - center_val) * JOYSTICK_OUTPUT_RANGE) / (center_val - min_val));
        } else {
            result = 0;
        }
    } else {
        // 正半轴映射
        if (max_val > center_val) {
            result = (int16_t)(((int32_t)(adc_value - center_val) * JOYSTICK_OUTPUT_RANGE) / (max_val - center_val));
        } else {
            result = 0;
        }
    }

    // 限制输出范围
    if (result > JOYSTICK_OUTPUT_RANGE) result = JOYSTICK_OUTPUT_RANGE;
    if (result < -JOYSTICK_OUTPUT_RANGE) result = -JOYSTICK_OUTPUT_RANGE;

    return result;
}

HAL_StatusTypeDef Joystick_LoadCalibration(joystick_calibration_t *calibration) {
    if (!calibration) {
        return HAL_ERROR;
    }

    calibration_data = *calibration;
    return HAL_OK;
}

HAL_StatusTypeDef Joystick_SaveCalibration(joystick_calibration_t *calibration) {
    if (!calibration) {
        return HAL_ERROR;
    }

    *calibration = calibration_data;
    return HAL_OK;
}

void Joystick_GetStatusInfo(char *info_str, uint16_t max_len) {
    if (!info_str || max_len < 100) {
        return;
    }

    snprintf(info_str, max_len,
        "Joystick Status:\n"
        "X: %d, Y: %d\n"
        "Raw X: %d, Raw Y: %d\n"
        "Button: %s\n"
        "Calibrated: %s\n"
        "Deadzone: %d\n",
        last_data.x_axis, last_data.y_axis,
        last_data.raw_x, last_data.raw_y,
        last_data.button_pressed ? "Pressed" : "Released",
        calibration_data.is_calibrated ? "Yes" : "No",
        joystick_cfg.deadzone
    );
}

void Joystick_Reset(void) {
    // 重置滤波缓冲区
    memset(x_filter_buffer, 0, sizeof(x_filter_buffer));
    memset(y_filter_buffer, 0, sizeof(y_filter_buffer));
    filter_index = 0;

    // 重置按键状态
    button_state = 0;
    last_button_state = 0;
    button_press_time = 0;

    // 重置数据
    memset(&last_data, 0, sizeof(last_data));
}
