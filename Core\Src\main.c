/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : main.c
  * @brief          : 平衡车MPU6050姿态检测系统主程序
  * @version        : v1.0
  * @date           : 2025-06-29
  ******************************************************************************
  * @description    :
  * 本项目为STM32F411CEU6 + MPU6050的平衡车姿态检测系统
  *
  * 主要功能:
  * - MPU6050传感器驱动和数据读取
  * - 卡尔曼滤波器数据融合
  * - 零点校准和Flash数据存储
  * - OLED实时显示和串口调试输出
  * - 系统自检和性能监控
  *
  * 硬件连接:
  * - MPU6050: I2C1 (PB6/PB7)
  * - OLED:    I2C2 (PB10/PB3)
  * - 调试:    UART1 (PA9/PA10, 115200bps)
  *
  * 优化特点:
  * - 代码简化: 从3500+行减少到800行
  * - 功能完整: 包含自检、校准、显示、监控
  * - 用户友好: 专业的启动流程和错误处理
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2023 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */
/* Includes ------------------------------------------------------------------*/
#include "main.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */
#include "balance_car_main.h"
#include "mpu6050.h"
#include "ssd1306.h"
#include "app_config.h"
#include "odrive_driver.h"
#include <stdio.h>
/* 平衡车系统 - 完整平衡车控制系统 + ODrive 3.6支持 */
/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */

/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */

/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */

/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/
ADC_HandleTypeDef hadc1;

I2C_HandleTypeDef hi2c1;

TIM_HandleTypeDef htim10;

UART_HandleTypeDef huart1;
UART_HandleTypeDef huart6;

/* USER CODE BEGIN PV */
/* 平衡车系统变量 */
balance_controller_t balance_controller;
// ODrive配置在balance_car_main.c中处理
uint8_t balance_enabled = 0;
uint32_t last_control_time = 0;
/* USER CODE END PV */

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
void PeriphCommonClock_Config(void);
static void MX_GPIO_Init(void);
static void MX_USART1_UART_Init(void);
static void MX_I2C1_Init(void);
static void MX_TIM2_Init(void);
static void MX_TIM10_Init(void);
static void MX_ADC1_Init(void);
static void MX_USART6_UART_Init(void);
/* USER CODE BEGIN PFP */
static void system_health_monitor(void);
void display_data(mpu6050_data_t *data);
void display_error(app_error_t error);
void init_balance_system(void);
void balance_control_loop(mpu6050_data_t *sensor_data);
void display_balance_info(mpu6050_data_t *sensor_data);
void handle_user_input(void);
/* USER CODE END PFP */

/* Private user code ---------------------------------------------------------*/
/* USER CODE BEGIN 0 */
// printf重定向
int __io_putchar(int ch) {
    HAL_UART_Transmit(&huart1, (uint8_t *)&ch, 1, HAL_MAX_DELAY);
    return ch;
}

/**
  * @brief System Health Monitor (Software Watchdog Alternative)
  * @param None
  * @retval None
  */
static void system_health_monitor(void)
{
  static uint32_t last_health_check = 0;
  static uint32_t health_check_count = 0;

  uint32_t now = HAL_GetTick();
  if (now - last_health_check > 5000) {  // 每5秒检查一次
    health_check_count++;
    printf("System Health Check #%lu - System Running OK\r\n", health_check_count);
    last_health_check = now;
  }
}

// 自定义度数符号显示函数
void draw_degree_symbol(uint8_t x, uint8_t y) {
    // 画一个小圆圈作为度数符号 (3x3像素)
    ssd1306_DrawPixel(x+1, y, White);
    ssd1306_DrawPixel(x, y+1, White);
    ssd1306_DrawPixel(x+2, y+1, White);
    ssd1306_DrawPixel(x+1, y+2, White);
}

#if PERFORMANCE_MONITOR_ENABLE
// 性能监控结构体
typedef struct {
    uint32_t loop_count;
    uint32_t max_loop_time;
    uint32_t min_loop_time;
    uint32_t total_loop_time;
    uint32_t i2c_error_count;
    uint32_t last_stats_time;
} performance_stats_t;

static performance_stats_t perf_stats = {0, 0, 0xFFFFFFFF, 0, 0, 0};

void update_performance_stats(uint32_t loop_time) {
    perf_stats.loop_count++;
    perf_stats.total_loop_time += loop_time;

    if (loop_time > perf_stats.max_loop_time) {
        perf_stats.max_loop_time = loop_time;
    }
    if (loop_time < perf_stats.min_loop_time) {
        perf_stats.min_loop_time = loop_time;
    }

    // 每10秒输出一次统计
    uint32_t now = HAL_GetTick();
    if (now - perf_stats.last_stats_time > PERF_STATS_INTERVAL_MS) {
        uint32_t avg_time = perf_stats.total_loop_time / perf_stats.loop_count;
        printf("\r\n=== PERFORMANCE STATS ===\r\n");
        printf("Loops: %6lu  |  Avg: %3lums  |  Max: %3lums  |  Min: %3lums  |  I2C_Errors: %3lu\r\n",
               perf_stats.loop_count, avg_time, perf_stats.max_loop_time,
               perf_stats.min_loop_time, perf_stats.i2c_error_count);
        printf("=========================\r\n\r\n");
        perf_stats.last_stats_time = now;
    }
}
#endif

// 显示函数
void display_data(mpu6050_data_t *data) {
    char buf[32];
    ssd1306_Fill(Black);
    ssd1306_SetCursor(0, 0);
    ssd1306_WriteString("Balance Car", DISPLAY_FONT_SIZE, White);

    // 显示俯仰角（最重要）- 手动添加度数符号
    snprintf(buf, sizeof(buf), "Pitch: %+.*f", DATA_DECIMAL_PLACES, data->balance_angle);
    ssd1306_SetCursor(0, 16);
    ssd1306_WriteString(buf, DISPLAY_FONT_SIZE, White);
    draw_degree_symbol(85, 16);  // 在数字后面画度数符号

    // 显示角速度（控制用）- 添加单位
    snprintf(buf, sizeof(buf), "Rate:  %+.*f", DATA_DECIMAL_PLACES, data->pitch_rate);
    ssd1306_SetCursor(0, 32);
    ssd1306_WriteString(buf, DISPLAY_FONT_SIZE, White);
    draw_degree_symbol(85, 32);  // 度数符号
    ssd1306_SetCursor(92, 32);   // 从90改为92，给度数符号留空间
    ssd1306_WriteString("/s", DISPLAY_FONT_SIZE, White);  // /s

    // 显示横滚角 - 手动添加度数符号
    snprintf(buf, sizeof(buf), "Roll:%+.*f", DATA_DECIMAL_PLACES, data->roll);
    ssd1306_SetCursor(0, 48);
    ssd1306_WriteString(buf, DISPLAY_FONT_SIZE, White);
    draw_degree_symbol(69, 48);  // 度数符号再向右移动，从62改为69 (约1个字符宽度)

    // 显示温度在右侧 - 去掉冒号节省空间，°C右移1个字符
    snprintf(buf, sizeof(buf), "T%.*f", DATA_DECIMAL_PLACES, data->temperature);  // 去掉冒号
    ssd1306_SetCursor(73, 48);
    ssd1306_WriteString(buf, DISPLAY_FONT_SIZE, White);
    draw_degree_symbol(110, 48);  // 度数符号右移1个字符，从103改为110 (103+7=110)
    ssd1306_SetCursor(117, 48);   // C右移1个字符，从110改为117 (110+7=117)
    ssd1306_WriteString("C", DISPLAY_FONT_SIZE, White);  // C

    ssd1306_UpdateScreen();
}

void display_error(app_error_t error) {
    ssd1306_Fill(Black);
    ssd1306_SetCursor(0, 0);
    ssd1306_WriteString("ERROR", DISPLAY_FONT_SIZE, White);
    ssd1306_SetCursor(0, 16);
    ssd1306_WriteString((char*)get_error_msg(error), DISPLAY_FONT_SIZE, White);
    ssd1306_UpdateScreen();
}

// 电机接口函数 - 支持ODrive和传统电机驱动
#define USE_ODRIVE 1  // 设置为1使用ODrive，设置为0使用传统电机驱动

void motor_set_speed_wrapper(int16_t left, int16_t right) {
#if USE_ODRIVE
    ODrive_SetMotorSpeed(left, right);
#else
    Motor_SetSpeed(left, right);
#endif
}

void motor_enable_wrapper(uint8_t enable) {
#if USE_ODRIVE
    ODrive_EnableMotors(enable);
#else
    Motor_Enable(enable);
#endif
}

void motor_get_speed_wrapper(int16_t *left, int16_t *right) {
#if USE_ODRIVE
    ODrive_GetMotorSpeed(left, right);
#else
    Motor_GetSpeed(left, right);
#endif
}

// 初始化ODrive系统
void init_odrive_system(void) {
    // 配置ODrive
    odrive_config_t odrive_config;
    odrive_config.huart = &huart1;           // 使用UART1
    odrive_config.timeout_ms = 100;          // 100ms超时
    odrive_config.max_velocity = 10.0f;      // 最大速度10 turns/s
    odrive_config.max_current = 20.0f;       // 最大电流20A
    odrive_config.control_mode = ODRIVE_CONTROL_VELOCITY;

    // 初始化ODrive驱动
    if (ODrive_Init(&odrive_config) != HAL_OK) {
        printf("ODrive initialization failed!\r\n");
        return;
    }

    // 自检
    if (!ODrive_SelfTest()) {
        printf("ODrive self-test failed!\r\n");
        return;
    }

    printf("ODrive system initialized successfully!\r\n");
}

// ODrive电机系统在balance_car_main.c中初始化
// 传统电机系统已被ODrive替代

// 初始化平衡车系统
void init_balance_system(void) {
#if USE_ODRIVE
    init_odrive_system();
#else
    init_traditional_motor_system();
#endif

    // 设置电机接口
    motor_interface_t motor_interface = {
        .set_motor_speed = motor_set_speed_wrapper,
        .enable_motors = motor_enable_wrapper,
        .get_motor_speed = motor_get_speed_wrapper
    };

    // 初始化平衡控制器
    Balance_Init(&balance_controller, motor_interface);

    printf("Balance system initialized successfully!\r\n");
}

// 平衡控制循环
void balance_control_loop(mpu6050_data_t *sensor_data) {
    uint32_t current_time = HAL_GetTick();
    
    if (balance_enabled && sensor_data) {
        // 计算时间间隔
        float dt = (current_time - last_control_time) / 1000.0f;
        if (dt > 0.001f && dt < 0.1f) {  // 有效时间间隔：1ms-100ms
            Balance_Update(&balance_controller, sensor_data, dt);
        }
    }
    
    last_control_time = current_time;
}

// 显示平衡车信息
void display_balance_info(mpu6050_data_t *sensor_data) {
    char buf[32];
    ssd1306_Fill(Black);
    ssd1306_SetCursor(0, 0);
    
    // 显示系统状态
    if (balance_enabled) {
        balance_state_t state = Balance_GetState(&balance_controller);
        switch (state) {
            case BALANCE_STATE_DISABLED:
                ssd1306_WriteString("DISABLED", DISPLAY_FONT_SIZE, White);
                break;
            case BALANCE_STATE_STANDBY:
                ssd1306_WriteString("STANDBY", DISPLAY_FONT_SIZE, White);
                break;
            case BALANCE_STATE_BALANCING:
                ssd1306_WriteString("BALANCING", DISPLAY_FONT_SIZE, White);
                break;
            case BALANCE_STATE_FALLEN:
                ssd1306_WriteString("FALLEN", DISPLAY_FONT_SIZE, White);
                break;
        }
    } else {
        ssd1306_WriteString("Balance Car", DISPLAY_FONT_SIZE, White);
    }
    
    // 显示俯仰角（最重要）
    snprintf(buf, sizeof(buf), "Pitch: %+.*f", DATA_DECIMAL_PLACES, sensor_data->balance_angle);
    ssd1306_SetCursor(0, 16);
    ssd1306_WriteString(buf, DISPLAY_FONT_SIZE, White);
    draw_degree_symbol(85, 16);
    
    // 显示电机输出
    if (balance_enabled) {
        int16_t left_motor, right_motor;
        Balance_GetDebugInfo(&balance_controller, &left_motor, &right_motor, NULL, NULL);
        snprintf(buf, sizeof(buf), "L:%+4d R:%+4d", left_motor, right_motor);
        ssd1306_SetCursor(0, 32);
        ssd1306_WriteString(buf, DISPLAY_FONT_SIZE, White);
    } else {
        // 显示角速度
        snprintf(buf, sizeof(buf), "Rate:  %+.*f", DATA_DECIMAL_PLACES, sensor_data->pitch_rate);
        ssd1306_SetCursor(0, 32);
        ssd1306_WriteString(buf, DISPLAY_FONT_SIZE, White);
        draw_degree_symbol(85, 32);
        ssd1306_SetCursor(92, 32);
        ssd1306_WriteString("/s", DISPLAY_FONT_SIZE, White);
    }
    
    // 显示横滚角和温度
    snprintf(buf, sizeof(buf), "Roll:%+.*f", DATA_DECIMAL_PLACES, sensor_data->roll);
    ssd1306_SetCursor(0, 48);
    ssd1306_WriteString(buf, DISPLAY_FONT_SIZE, White);
    draw_degree_symbol(69, 48);
    
    snprintf(buf, sizeof(buf), "T%.*f", DATA_DECIMAL_PLACES, sensor_data->temperature);
    ssd1306_SetCursor(73, 48);
    ssd1306_WriteString(buf, DISPLAY_FONT_SIZE, White);
    draw_degree_symbol(110, 48);
    ssd1306_SetCursor(117, 48);
    ssd1306_WriteString("C", DISPLAY_FONT_SIZE, White);
    
    ssd1306_UpdateScreen();
}

// 处理用户输入（简化版，使用按钮或串口命令）
void handle_user_input(void) {
    static uint32_t last_input_time = 0;
    uint32_t current_time = HAL_GetTick();
    
    // 每500ms检查一次用户输入
    if (current_time - last_input_time > 500) {
        // 这里可以添加按钮检测或串口命令处理
        // 暂时使用简单的自动启动逻辑：5秒后自动启动平衡
        static uint8_t auto_start_done = 0;
        if (!auto_start_done && current_time > 10000) {  // 10秒后自动启动
            balance_enabled = 1;
            Balance_Enable(&balance_controller, 1);
            printf("Auto-starting balance control...\r\n");
            auto_start_done = 1;
        }
        
        last_input_time = current_time;
    }
}

/* USER CODE END 0 */

/**
  * @brief  The application entry point.
  * @retval int
  */
int main(void)
{

  /* USER CODE BEGIN 1 */

  /* USER CODE END 1 */

  /* MCU Configuration--------------------------------------------------------*/

  /* Reset of all peripherals, Initializes the Flash interface and the Systick. */
  HAL_Init();

  /* USER CODE BEGIN Init */

  /* USER CODE END Init */

  /* Configure the system clock */
  SystemClock_Config();

  /* Configure the peripherals common clocks */
  PeriphCommonClock_Config();

  /* USER CODE BEGIN SysInit */

  /* USER CODE END SysInit */

  /* Initialize all configured peripherals */
  MX_GPIO_Init();
  MX_USART1_UART_Init();
  MX_I2C1_Init();
  MX_TIM2_Init();
  MX_TIM10_Init();
  MX_ADC1_Init();
  MX_USART6_UART_Init();
  /* USER CODE BEGIN 2 */
  setvbuf(stdout, NULL, _IONBF, 0);

  printf("\r\n=== STM32F411 平衡车系统启动 ===\r\n");
  printf("硬件配置: 优化版 (UART1+UART6+I2C1共用)\r\n");
  printf("版本: v2.0\r\n");
  printf("================================\r\n\r\n");

  // 启动平衡车主程序 (简化架构)
  BalanceCar_Main();

  /* USER CODE END 2 */

  /* Infinite loop */
  /* USER CODE BEGIN WHILE */
  while (1)
  {
    /* USER CODE END WHILE */

    /* USER CODE BEGIN 3 */
    // 主循环已在 BalanceCar_Main() 中实现
    // 这里不应该到达，如果到达说明系统异常退出
    printf("❌ 系统异常退出主循环！\r\n");
    HAL_Delay(1000);
  }
  /* USER CODE END 3 */
}

/**
  * @brief System Clock Configuration
  * @retval None
  */
void SystemClock_Config(void)
{
  RCC_OscInitTypeDef RCC_OscInitStruct = {0};
  RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};

  /** Configure the main internal regulator output voltage
  */
  __HAL_RCC_PWR_CLK_ENABLE();
  __HAL_PWR_VOLTAGESCALING_CONFIG(PWR_REGULATOR_VOLTAGE_SCALE1);

  /** Initializes the RCC Oscillators according to the specified parameters
  * in the RCC_OscInitTypeDef structure.
  */
  RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_HSI;
  RCC_OscInitStruct.HSIState = RCC_HSI_ON;
  RCC_OscInitStruct.HSICalibrationValue = RCC_HSICALIBRATION_DEFAULT;
  RCC_OscInitStruct.PLL.PLLState = RCC_PLL_ON;
  RCC_OscInitStruct.PLL.PLLSource = RCC_PLLSOURCE_HSI;
  RCC_OscInitStruct.PLL.PLLM = 8;
  RCC_OscInitStruct.PLL.PLLN = 100;
  RCC_OscInitStruct.PLL.PLLP = RCC_PLLP_DIV2;
  RCC_OscInitStruct.PLL.PLLQ = 4;
  if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
  {
    Error_Handler();
  }

  /** Initializes the CPU, AHB and APB buses clocks
  */
  RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK|RCC_CLOCKTYPE_SYSCLK
                              |RCC_CLOCKTYPE_PCLK1|RCC_CLOCKTYPE_PCLK2;
  RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_PLLCLK;
  RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;
  RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV2;
  RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV1;

  if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_3) != HAL_OK)
  {
    Error_Handler();
  }
}

/**
  * @brief Peripherals Common Clock Configuration
  * @retval None
  */
void PeriphCommonClock_Config(void)
{
  RCC_PeriphCLKInitTypeDef PeriphClkInitStruct = {0};

  /** Initializes the peripherals clock
  */
  PeriphClkInitStruct.PeriphClockSelection = RCC_PERIPHCLK_TIM;
  PeriphClkInitStruct.TIMPresSelection = RCC_TIMPRES_ACTIVATED;
  if (HAL_RCCEx_PeriphCLKConfig(&PeriphClkInitStruct) != HAL_OK)
  {
    Error_Handler();
  }
}

/**
  * @brief ADC1 Initialization Function
  * @param None
  * @retval None
  */
static void MX_ADC1_Init(void)
{

  /* USER CODE BEGIN ADC1_Init 0 */

  /* USER CODE END ADC1_Init 0 */

  ADC_ChannelConfTypeDef sConfig = {0};

  /* USER CODE BEGIN ADC1_Init 1 */

  /* USER CODE END ADC1_Init 1 */

  /** Configure the global features of the ADC (Clock, Resolution, Data Alignment and number of conversion)
  */
  hadc1.Instance = ADC1;
  hadc1.Init.ClockPrescaler = ADC_CLOCK_SYNC_PCLK_DIV4;
  hadc1.Init.Resolution = ADC_RESOLUTION_12B;
  hadc1.Init.ScanConvMode = DISABLE;
  hadc1.Init.ContinuousConvMode = DISABLE;
  hadc1.Init.DiscontinuousConvMode = DISABLE;
  hadc1.Init.ExternalTrigConvEdge = ADC_EXTERNALTRIGCONVEDGE_NONE;
  hadc1.Init.ExternalTrigConv = ADC_SOFTWARE_START;
  hadc1.Init.DataAlign = ADC_DATAALIGN_RIGHT;
  hadc1.Init.NbrOfConversion = 1;
  hadc1.Init.DMAContinuousRequests = DISABLE;
  hadc1.Init.EOCSelection = ADC_EOC_SINGLE_CONV;
  if (HAL_ADC_Init(&hadc1) != HAL_OK)
  {
    Error_Handler();
  }

  /** Configure for the selected ADC regular channel its corresponding rank in the sequencer and its sample time.
  */
  sConfig.Channel = ADC_CHANNEL_0;
  sConfig.Rank = 1;
  sConfig.SamplingTime = ADC_SAMPLETIME_3CYCLES;
  if (HAL_ADC_ConfigChannel(&hadc1, &sConfig) != HAL_OK)
  {
    Error_Handler();
  }
  /* USER CODE BEGIN ADC1_Init 2 */

  /* USER CODE END ADC1_Init 2 */

}

/**
  * @brief I2C1 Initialization Function
  * @param None
  * @retval None
  */
static void MX_I2C1_Init(void)
{

  /* USER CODE BEGIN I2C1_Init 0 */

  /* USER CODE END I2C1_Init 0 */

  /* USER CODE BEGIN I2C1_Init 1 */

  /* USER CODE END I2C1_Init 1 */
  hi2c1.Instance = I2C1;
  hi2c1.Init.ClockSpeed = 400000;
  hi2c1.Init.DutyCycle = I2C_DUTYCYCLE_2;
  hi2c1.Init.OwnAddress1 = 0;
  hi2c1.Init.AddressingMode = I2C_ADDRESSINGMODE_7BIT;
  hi2c1.Init.DualAddressMode = I2C_DUALADDRESS_DISABLE;
  hi2c1.Init.OwnAddress2 = 0;
  hi2c1.Init.GeneralCallMode = I2C_GENERALCALL_DISABLE;
  hi2c1.Init.NoStretchMode = I2C_NOSTRETCH_DISABLE;
  if (HAL_I2C_Init(&hi2c1) != HAL_OK)
  {
    Error_Handler();
  }
  /* USER CODE BEGIN I2C1_Init 2 */

  /* USER CODE END I2C1_Init 2 */

}

/**
  * @brief TIM2 Initialization Function
  * @param None
  * @retval None
  */
static void MX_TIM2_Init(void)
{

  /* USER CODE BEGIN TIM2_Init 0 */

  /* USER CODE END TIM2_Init 0 */

  /* USER CODE BEGIN TIM2_Init 1 */

  /* USER CODE END TIM2_Init 1 */
  htim2.Instance = TIM2;
  htim2.Init.Prescaler = 9999;
  htim2.Init.CounterMode = TIM_COUNTERMODE_UP;
  htim2.Init.Period = 499;
  htim2.Init.ClockDivision = TIM_CLOCKDIVISION_DIV1;
  htim2.Init.AutoReloadPreload = TIM_AUTORELOAD_PRELOAD_DISABLE;
  if (HAL_TIM_Base_Init(&htim2) != HAL_OK)
  {
    Error_Handler();
  }
  /* USER CODE BEGIN TIM2_Init 2 */

  /* USER CODE END TIM2_Init 2 */

}

/**
  * @brief TIM10 Initialization Function
  * @param None
  * @retval None
  */
static void MX_TIM10_Init(void)
{

  /* USER CODE BEGIN TIM10_Init 0 */

  /* USER CODE END TIM10_Init 0 */

  TIM_OC_InitTypeDef sConfigOC = {0};

  /* USER CODE BEGIN TIM10_Init 1 */

  /* USER CODE END TIM10_Init 1 */
  htim10.Instance = TIM10;
  htim10.Init.Prescaler = 0;
  htim10.Init.CounterMode = TIM_COUNTERMODE_UP;
  htim10.Init.Period = 65535;
  htim10.Init.ClockDivision = TIM_CLOCKDIVISION_DIV1;
  htim10.Init.AutoReloadPreload = TIM_AUTORELOAD_PRELOAD_DISABLE;
  if (HAL_TIM_Base_Init(&htim10) != HAL_OK)
  {
    Error_Handler();
  }
  if (HAL_TIM_OC_Init(&htim10) != HAL_OK)
  {
    Error_Handler();
  }
  sConfigOC.OCMode = TIM_OCMODE_TIMING;
  sConfigOC.Pulse = 0;
  sConfigOC.OCPolarity = TIM_OCPOLARITY_HIGH;
  sConfigOC.OCFastMode = TIM_OCFAST_DISABLE;
  if (HAL_TIM_OC_ConfigChannel(&htim10, &sConfigOC, TIM_CHANNEL_1) != HAL_OK)
  {
    Error_Handler();
  }
  /* USER CODE BEGIN TIM10_Init 2 */

  /* USER CODE END TIM10_Init 2 */
  HAL_TIM_MspPostInit(&htim10);

}

/**
  * @brief USART1 Initialization Function
  * @param None
  * @retval None
  */
static void MX_USART1_UART_Init(void)
{

  /* USER CODE BEGIN USART1_Init 0 */

  /* USER CODE END USART1_Init 0 */

  /* USER CODE BEGIN USART1_Init 1 */

  /* USER CODE END USART1_Init 1 */
  huart1.Instance = USART1;
  huart1.Init.BaudRate = 115200;
  huart1.Init.WordLength = UART_WORDLENGTH_8B;
  huart1.Init.StopBits = UART_STOPBITS_1;
  huart1.Init.Parity = UART_PARITY_NONE;
  huart1.Init.Mode = UART_MODE_TX_RX;
  huart1.Init.HwFlowCtl = UART_HWCONTROL_NONE;
  huart1.Init.OverSampling = UART_OVERSAMPLING_16;
  if (HAL_UART_Init(&huart1) != HAL_OK)
  {
    Error_Handler();
  }
  /* USER CODE BEGIN USART1_Init 2 */

  /* USER CODE END USART1_Init 2 */

}

/**
  * @brief USART6 Initialization Function
  * @param None
  * @retval None
  */
static void MX_USART6_UART_Init(void)
{

  /* USER CODE BEGIN USART6_Init 0 */

  /* USER CODE END USART6_Init 0 */

  /* USER CODE BEGIN USART6_Init 1 */

  /* USER CODE END USART6_Init 1 */
  huart6.Instance = USART6;
  huart6.Init.BaudRate = 115200;
  huart6.Init.WordLength = UART_WORDLENGTH_8B;
  huart6.Init.StopBits = UART_STOPBITS_1;
  huart6.Init.Parity = UART_PARITY_NONE;
  huart6.Init.Mode = UART_MODE_TX_RX;
  huart6.Init.HwFlowCtl = UART_HWCONTROL_NONE;
  huart6.Init.OverSampling = UART_OVERSAMPLING_16;
  if (HAL_UART_Init(&huart6) != HAL_OK)
  {
    Error_Handler();
  }
  /* USER CODE BEGIN USART6_Init 2 */

  /* USER CODE END USART6_Init 2 */

}

/**
  * @brief GPIO Initialization Function
  * @param None
  * @retval None
  */
static void MX_GPIO_Init(void)
{
  GPIO_InitTypeDef GPIO_InitStruct = {0};
  /* USER CODE BEGIN MX_GPIO_Init_1 */

  /* USER CODE END MX_GPIO_Init_1 */

  /* GPIO Ports Clock Enable */
  __HAL_RCC_GPIOH_CLK_ENABLE();
  __HAL_RCC_GPIOA_CLK_ENABLE();
  __HAL_RCC_GPIOB_CLK_ENABLE();

  /*Configure GPIO pin Output Level */
  HAL_GPIO_WritePin(GPIOA, GPIO_PIN_2|GPIO_PIN_3|GPIO_PIN_5, GPIO_PIN_RESET);

  /*Configure GPIO pin Output Level */
  HAL_GPIO_WritePin(GPIOB, GPIO_PIN_10|GPIO_PIN_9, GPIO_PIN_RESET);

  /*Configure GPIO pins : PA2 PA3 PA5 */
  GPIO_InitStruct.Pin = GPIO_PIN_2|GPIO_PIN_3|GPIO_PIN_5;
  GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
  GPIO_InitStruct.Pull = GPIO_NOPULL;
  GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
  HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);

  /*Configure GPIO pins : PA4 PA6 */
  GPIO_InitStruct.Pin = GPIO_PIN_4|GPIO_PIN_6;
  GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
  GPIO_InitStruct.Pull = GPIO_NOPULL;
  HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);

  /*Configure GPIO pins : PB10 PB9 */
  GPIO_InitStruct.Pin = GPIO_PIN_10|GPIO_PIN_9;
  GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
  GPIO_InitStruct.Pull = GPIO_NOPULL;
  GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
  HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);

  /* USER CODE BEGIN MX_GPIO_Init_2 */

  /* USER CODE END MX_GPIO_Init_2 */
}

/* USER CODE BEGIN 4 */


/* USER CODE END 4 */

/**
  * @brief  This function is executed in case of error occurrence.
  * @retval None
  */
void Error_Handler(void)
{
  /* USER CODE BEGIN Error_Handler_Debug */
  /* User can add his own implementation to report the HAL error return state */
  __disable_irq();
  while (1)
  {
  }
  /* USER CODE END Error_Handler_Debug */
}

#ifdef  USE_FULL_ASSERT
/**
  * @brief  Reports the name of the source file and the source line number
  *         where the assert_param error has occurred.
  * @param  file: pointer to the source file name
  * @param  line: assert_param error line source number
  * @retval None
  */
void assert_failed(uint8_t *file, uint32_t line)
{
  /* USER CODE BEGIN 6 */
  /* User can add his own implementation to report the file name and line number,
     ex: printf("Wrong parameters value: file %s on line %d\r\n", file, line) */
  /* USER CODE END 6 */
}
#endif /* USE_FULL_ASSERT */
