/**
 ******************************************************************************
 * @file    mobile_app.c
 * @brief   手机APP通信模块实现
 ******************************************************************************
 */

#include "mobile_app.h"
#include "balance_car_system.h"
#include <string.h>
#include <stdio.h>
#include <math.h>

// 内部函数声明
static uint8_t MobileApp_CalculateChecksum_Internal(const uint8_t *data, uint16_t length);
static mobile_app_result_t MobileApp_SendFrame(mobile_app_t *app, uint8_t command, uint8_t *payload, uint16_t length);

/**
 * @brief 初始化手机APP模块
 */
mobile_app_result_t MobileApp_Init(mobile_app_t *app, UART_HandleTypeDef *uart)
{
    if (app == NULL || uart == NULL) {
        return MOBILE_APP_ERROR_NULL_POINTER;
    }
    
    // 清零结构体
    memset(app, 0, sizeof(mobile_app_t));
    
    // 设置UART句柄
    app->uart = uart;
    
    // 设置初始状态
    app->is_connected = 0;
    app->last_ping_time = 0;
    app->connection_timeout_ms = APP_DEFAULT_TIMEOUT_MS;
    app->rx_index = 0;
    app->rx_state = APP_RX_STATE_IDLE;
    
    // 设置默认数据流配置
    app->stream_config.enable_attitude = 1;
    app->stream_config.enable_motor = 1;
    app->stream_config.enable_system = 1;
    app->stream_config.interval_ms = APP_DEFAULT_STREAM_INTERVAL;
    
    // 标记为已初始化
    app->is_initialized = 1;
    app->error_code = 0;
    
    return MOBILE_APP_OK;
}

/**
 * @brief 启动手机APP接口
 */
mobile_app_result_t MobileApp_Start(mobile_app_t *app)
{
    if (app == NULL || !app->is_initialized) {
        return MOBILE_APP_ERROR_NULL_POINTER;
    }
    
    app->last_ping_time = HAL_GetTick();
    return MOBILE_APP_OK;
}

/**
 * @brief 停止手机APP接口
 */
mobile_app_result_t MobileApp_Stop(mobile_app_t *app)
{
    if (app == NULL) {
        return MOBILE_APP_ERROR_NULL_POINTER;
    }
    
    app->is_connected = 0;
    return MOBILE_APP_OK;
}

/**
 * @brief 更新手机APP模块
 */
mobile_app_result_t MobileApp_Update(mobile_app_t *app)
{
    if (app == NULL || !app->is_initialized) {
        return MOBILE_APP_ERROR_NULL_POINTER;
    }
    
    uint32_t current_time = HAL_GetTick();
    
    // 检查连接超时
    if (app->is_connected && 
        (current_time - app->last_ping_time >= app->connection_timeout_ms)) {
        app->is_connected = 0;
    }
    
    return MOBILE_APP_OK;
}

/**
 * @brief 发送状态信息
 */
mobile_app_result_t MobileApp_SendStatus(mobile_app_t *app, app_system_status_t *status)
{
    if (app == NULL || status == NULL) {
        return MOBILE_APP_ERROR_NULL_POINTER;
    }
    
    if (!app->is_connected) {
        return MOBILE_APP_ERROR_NOT_CONNECTED;
    }
    
    // 构建状态数据包
    uint8_t payload[32];
    uint16_t index = 0;
    
    // 添加系统状态数据
    payload[index++] = status->system_mode;
    payload[index++] = status->battery_level;
    
    // 发送状态帧
    return MobileApp_SendFrame(app, APP_CMD_STATUS_REPORT, payload, index);
}

/**
 * @brief 发送响应
 */
mobile_app_result_t MobileApp_SendResponse(mobile_app_t *app, uint8_t command, uint8_t result, uint8_t *data, uint16_t length)
{
    if (app == NULL) {
        return MOBILE_APP_ERROR_NULL_POINTER;
    }
    
    uint8_t payload[APP_MAX_FRAME_SIZE - 10];
    payload[0] = command;
    payload[1] = result;
    
    if (data && length > 0) {
        memcpy(&payload[2], data, length);
        length += 2;
    } else {
        length = 2;
    }
    
    return MobileApp_SendFrame(app, APP_CMD_RESPONSE, payload, length);
}

/**
 * @brief 发送数据流
 */
mobile_app_result_t MobileApp_SendDataStream(mobile_app_t *app)
{
    if (app == NULL || !app->is_initialized) {
        return MOBILE_APP_ERROR_NULL_POINTER;
    }
    
    uint32_t current_time = HAL_GetTick();
    if (current_time - app->last_stream_time < app->stream_config.interval_ms) {
        return MOBILE_APP_OK;  // 还没到发送时间
    }
    
    app->last_stream_time = current_time;
    
    // 构建数据流包
    uint8_t payload[64];
    uint16_t index = 0;
    
    // 添加时间戳
    uint32_t timestamp = current_time;
    memcpy(&payload[index], &timestamp, 4);
    index += 4;
    
    // 发送数据流
    return MobileApp_SendFrame(app, APP_CMD_DATA_STREAM, payload, index);
}

/**
 * @brief 处理接收到的数据
 */
mobile_app_result_t MobileApp_ProcessReceivedData(mobile_app_t *app, uint8_t *data, uint16_t length)
{
    if (app == NULL || data == NULL || length == 0) {
        return MOBILE_APP_ERROR_NULL_POINTER;
    }
    
    // 简化的数据处理 - 只处理心跳包
    if (length >= 4 && data[0] == APP_FRAME_HEADER && data[1] == APP_FRAME_VERSION) {
        app->is_connected = 1;
        app->last_ping_time = HAL_GetTick();
        app->frames_received++;
    }
    
    return MOBILE_APP_OK;
}

/**
 * @brief 设置数据流配置
 */
mobile_app_result_t MobileApp_SetStreamConfig(mobile_app_t *app, app_data_stream_config_t *config)
{
    if (app == NULL || config == NULL) {
        return MOBILE_APP_ERROR_NULL_POINTER;
    }
    
    memcpy(&app->stream_config, config, sizeof(app_data_stream_config_t));
    return MOBILE_APP_OK;
}

/**
 * @brief 设置超时时间
 */
mobile_app_result_t MobileApp_SetTimeout(mobile_app_t *app, uint32_t timeout_ms)
{
    if (app == NULL) {
        return MOBILE_APP_ERROR_NULL_POINTER;
    }
    
    app->connection_timeout_ms = timeout_ms;
    return MOBILE_APP_OK;
}

/**
 * @brief 注册回调函数
 */
mobile_app_result_t MobileApp_RegisterCallbacks(mobile_app_t *app,
    void (*on_control)(app_control_cmd_t *),
    void (*on_mode_change)(app_system_mode_t),
    void (*on_config)(app_config_params_t *),
    void (*on_emergency)(void))
{
    if (app == NULL) {
        return MOBILE_APP_ERROR_NULL_POINTER;
    }
    
    app->on_control_command = on_control;
    app->on_mode_change = on_mode_change;
    app->on_config_update = on_config;
    app->on_emergency_stop = on_emergency;
    
    return MOBILE_APP_OK;
}

/**
 * @brief 检查连接状态
 */
uint8_t MobileApp_IsConnected(mobile_app_t *app)
{
    if (app == NULL) {
        return 0;
    }
    return app->is_connected;
}

/**
 * @brief 获取连接时间
 */
uint32_t MobileApp_GetConnectionTime(mobile_app_t *app)
{
    if (app == NULL || !app->is_connected) {
        return 0;
    }
    return HAL_GetTick() - app->last_ping_time;
}

/**
 * @brief 获取统计信息
 */
void MobileApp_GetStatistics(mobile_app_t *app, uint32_t *rx_frames, uint32_t *tx_frames, uint32_t *errors)
{
    if (app == NULL) {
        return;
    }
    
    if (rx_frames) *rx_frames = app->frames_received;
    if (tx_frames) *tx_frames = app->frames_sent;
    if (errors) *errors = app->frames_error;
}

/**
 * @brief 计算校验和
 */
uint8_t MobileApp_CalculateChecksum(uint8_t *data, uint16_t length)
{
    return MobileApp_CalculateChecksum_Internal(data, length);
}

/**
 * @brief 内部校验和计算
 */
static uint8_t MobileApp_CalculateChecksum_Internal(const uint8_t *data, uint16_t length)
{
    uint8_t checksum = 0;
    for (uint16_t i = 0; i < length; i++) {
        checksum ^= data[i];
    }
    return checksum;
}

/**
 * @brief 发送帧
 */
static mobile_app_result_t MobileApp_SendFrame(mobile_app_t *app, uint8_t command, uint8_t *payload, uint16_t length)
{
    if (app == NULL || app->uart == NULL) {
        return MOBILE_APP_ERROR_NULL_POINTER;
    }
    
    // 构建帧
    app->tx_buffer[0] = APP_FRAME_HEADER;
    app->tx_buffer[1] = APP_FRAME_VERSION;
    app->tx_buffer[2] = command;
    app->tx_buffer[3] = (uint8_t)(length & 0xFF);
    app->tx_buffer[4] = (uint8_t)((length >> 8) & 0xFF);
    
    if (payload && length > 0) {
        memcpy(&app->tx_buffer[5], payload, length);
    }
    
    uint8_t checksum = MobileApp_CalculateChecksum_Internal(app->tx_buffer, 5 + length);
    app->tx_buffer[5 + length] = checksum;
    
    // 发送数据
    HAL_StatusTypeDef status = HAL_UART_Transmit(app->uart, app->tx_buffer, 6 + length, 1000);
    
    if (status == HAL_OK) {
        app->frames_sent++;
        return MOBILE_APP_OK;
    } else {
        app->frames_error++;
        return MOBILE_APP_ERROR_TRANSMISSION_FAILED;
    }
}

/**
 * @brief 构建帧
 */
mobile_app_result_t MobileApp_BuildFrame(app_frame_t *frame, uint8_t command, uint8_t *payload, uint16_t length)
{
    if (frame == NULL) {
        return MOBILE_APP_ERROR_NULL_POINTER;
    }

    frame->header = APP_FRAME_HEADER;
    frame->version = APP_FRAME_VERSION;
    frame->command = command;
    frame->length = length;

    if (payload && length > 0) {
        memcpy(frame->payload, payload, length);
    }

    return MOBILE_APP_OK;
}

/**
 * @brief 解析帧
 */
mobile_app_result_t MobileApp_ParseFrame(uint8_t *buffer, uint16_t length, app_frame_t *frame)
{
    if (buffer == NULL || frame == NULL || length < 6) {
        return MOBILE_APP_ERROR_NULL_POINTER;
    }

    frame->header = buffer[0];
    frame->version = buffer[1];
    frame->command = buffer[2];
    frame->length = buffer[3] | (buffer[4] << 8);

    if (frame->length > 0 && length >= 6 + frame->length) {
        memcpy(frame->payload, &buffer[5], frame->length);
    }

    return MOBILE_APP_OK;
}

/**
 * @brief 打印状态
 */
void MobileApp_PrintStatus(mobile_app_t *app)
{
    if (app == NULL) {
        return;
    }

    printf("Mobile App Status:\r\n");
    printf("  Connected: %s\r\n", app->is_connected ? "Yes" : "No");
    printf("  RX Frames: %lu\r\n", app->frames_received);
    printf("  TX Frames: %lu\r\n", app->frames_sent);
    printf("  Errors: %lu\r\n", app->frames_error);
}

/**
 * @brief 打印帧信息
 */
void MobileApp_PrintFrame(app_frame_t *frame)
{
    if (frame == NULL) {
        return;
    }

    printf("Frame: Header=0x%02X, Version=0x%02X, Command=0x%02X, Length=%u\r\n",
           frame->header, frame->version, frame->command, frame->length);
}

/**
 * @brief 自测试
 */
mobile_app_result_t MobileApp_SelfTest(mobile_app_t *app)
{
    if (app == NULL) {
        return MOBILE_APP_ERROR_NULL_POINTER;
    }

    // 简单的自测试
    if (!app->is_initialized) {
        return MOBILE_APP_ERROR_INVALID_STATE;
    }

    return MOBILE_APP_OK;
}
