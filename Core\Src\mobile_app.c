/**
 * @file mobile_app.c
 * @brief 手机APP接口实现
 * <AUTHOR> Agent
 * @date 2025-01-05
 * 
 * 硬件配置:
 * - 蓝牙通信: UART6 (PA11/PA12)
 * - 波特率: 115200bps
 * - 协议: 自定义帧格式
 */

#include "mobile_app.h"
#include "balance_car_main.h"
#include <stdio.h>
#include <string.h>

/* 协议定义 */
#define MOBILE_APP_FRAME_HEADER     0xAA
#define MOBILE_APP_FRAME_TAIL       0x55
#define MOBILE_APP_MAX_PAYLOAD_SIZE 32

/* 命令类型 */
typedef enum {
    CMD_HEARTBEAT = 0x01,
    CMD_CONTROL = 0x02,
    CMD_STATUS_REQUEST = 0x03,
    CMD_CONFIG = 0x04,
    CMD_EMERGENCY_STOP = 0x05
} mobile_app_cmd_t;

/* 帧结构 */
typedef struct {
    uint8_t header;
    uint8_t length;
    uint8_t cmd;
    uint8_t payload[MOBILE_APP_MAX_PAYLOAD_SIZE];
    uint8_t checksum;
    uint8_t tail;
} __attribute__((packed)) mobile_app_frame_t;

/* 外部变量 */
extern UART_HandleTypeDef huart6;

/* 私有变量 */
static uint8_t rx_buffer[64];
static uint8_t tx_buffer[64];
static volatile bool frame_received = false;

/* 私有函数声明 */
static mobile_app_result_t MobileApp_InitUART(mobile_app_t *app);
static mobile_app_result_t MobileApp_ProcessFrame(mobile_app_t *app, const mobile_app_frame_t *frame);
static mobile_app_result_t MobileApp_SendFrame(mobile_app_t *app, uint8_t cmd, const uint8_t *payload, uint8_t payload_len);
static uint8_t MobileApp_CalculateChecksum(const uint8_t *data, uint8_t length);
static mobile_app_result_t MobileApp_HandleControlCommand(mobile_app_t *app, const uint8_t *payload, uint8_t length);
static mobile_app_result_t MobileApp_HandleStatusRequest(mobile_app_t *app);
static mobile_app_result_t MobileApp_HandleConfigCommand(mobile_app_t *app, const uint8_t *payload, uint8_t length);

/**
 * @brief 获取默认配置
 */
mobile_app_result_t MobileApp_GetDefaultConfig(mobile_app_config_t *config)
{
    if (config == NULL) {
        return MOBILE_APP_ERROR_NULL_POINTER;
    }
    
    /* 通信参数 */
    config->baudrate = 115200;
    config->timeout_ms = 100;
    config->heartbeat_interval_ms = 1000;
    config->connection_timeout_ms = 5000;
    
    /* 控制参数 */
    config->max_forward_speed = 2.0f;
    config->max_turn_speed = 3.0f;
    config->control_deadzone = 0.1f;
    config->control_sensitivity = 1.0f;
    
    return MOBILE_APP_OK;
}

/**
 * @brief 初始化手机APP模块
 */
mobile_app_result_t MobileApp_Init(mobile_app_t *app, const mobile_app_config_t *config)
{
    if (app == NULL || config == NULL) {
        return MOBILE_APP_ERROR_NULL_POINTER;
    }
    
    /* 清零结构体 */
    memset(app, 0, sizeof(mobile_app_t));
    
    /* 复制配置 */
    memcpy(&app->config, config, sizeof(mobile_app_config_t));
    
    /* 设置初始状态 */
    app->state = MOBILE_APP_STATE_DISCONNECTED;
    app->connected = false;
    app->last_heartbeat_time = 0;
    app->last_command_time = 0;
    
    /* 初始化UART */
    mobile_app_result_t result = MobileApp_InitUART(app);
    if (result != MOBILE_APP_OK) {
        return result;
    }
    
    printf("Mobile App Module Initialized\r\n");
    return MOBILE_APP_OK;
}

/**
 * @brief 启动手机APP接口
 */
mobile_app_result_t MobileApp_Start(mobile_app_t *app)
{
    if (app == NULL) {
        return MOBILE_APP_ERROR_NULL_POINTER;
    }
    
    if (app->state != MOBILE_APP_STATE_DISCONNECTED) {
        return MOBILE_APP_ERROR_INVALID_STATE;
    }
    
    /* 启动UART接收 */
    HAL_UART_Receive_IT(&huart6, rx_buffer, 1);
    
    app->state = MOBILE_APP_STATE_WAITING_CONNECTION;
    app->last_heartbeat_time = HAL_GetTick();
    
    printf("Mobile App Interface Started - Waiting for connection\r\n");
    return MOBILE_APP_OK;
}

/**
 * @brief 停止手机APP接口
 */
mobile_app_result_t MobileApp_Stop(mobile_app_t *app)
{
    if (app == NULL) {
        return MOBILE_APP_ERROR_NULL_POINTER;
    }
    
    /* 停止UART接收 */
    HAL_UART_AbortReceive_IT(&huart6);
    
    app->state = MOBILE_APP_STATE_DISCONNECTED;
    app->connected = false;
    app->control_output.forward_speed = 0.0f;
    app->control_output.turn_speed = 0.0f;
    
    printf("Mobile App Interface Stopped\r\n");
    return MOBILE_APP_OK;
}

/**
 * @brief 更新手机APP接口
 */
mobile_app_result_t MobileApp_Update(mobile_app_t *app)
{
    if (app == NULL) {
        return MOBILE_APP_ERROR_NULL_POINTER;
    }
    
    if (app->state == MOBILE_APP_STATE_DISCONNECTED) {
        return MOBILE_APP_OK;
    }
    
    uint32_t current_time = HAL_GetTick();
    
    /* 检查连接超时 */
    if (app->connected && 
        (current_time - app->last_heartbeat_time >= app->config.connection_timeout_ms)) {
        app->connected = false;
        app->state = MOBILE_APP_STATE_WAITING_CONNECTION;
        app->control_output.forward_speed = 0.0f;
        app->control_output.turn_speed = 0.0f;
        printf("Mobile App Connection Lost\r\n");
    }
    
    /* 处理接收到的帧 */
    if (frame_received) {
        frame_received = false;
        // 这里应该处理完整的帧解析
        // 简化版本暂时跳过
    }
    
    /* 发送心跳 */
    if (app->connected && 
        (current_time - app->last_heartbeat_time >= app->config.heartbeat_interval_ms)) {
        MobileApp_SendFrame(app, CMD_HEARTBEAT, NULL, 0);
        app->last_heartbeat_time = current_time;
    }
    
    return MOBILE_APP_OK;
}

/**
 * @brief 获取控制输出
 */
mobile_app_result_t MobileApp_GetControlOutput(mobile_app_t *app, mobile_app_control_t *control)
{
    if (app == NULL || control == NULL) {
        return MOBILE_APP_ERROR_NULL_POINTER;
    }
    
    if (!app->connected) {
        control->forward_speed = 0.0f;
        control->turn_speed = 0.0f;
        control->valid = false;
    } else {
        *control = app->control_output;
    }
    
    return MOBILE_APP_OK;
}

/**
 * @brief 获取连接状态
 */
mobile_app_result_t MobileApp_GetStatus(mobile_app_t *app, mobile_app_status_t *status)
{
    if (app == NULL || status == NULL) {
        return MOBILE_APP_ERROR_NULL_POINTER;
    }
    
    status->state = app->state;
    status->connected = app->connected;
    status->last_heartbeat_time = app->last_heartbeat_time;
    status->last_command_time = app->last_command_time;
    status->forward_speed = app->control_output.forward_speed;
    status->turn_speed = app->control_output.turn_speed;
    
    return MOBILE_APP_OK;
}

/**
 * @brief 发送状态数据到手机
 */
mobile_app_result_t MobileApp_SendStatus(mobile_app_t *app, const balance_car_status_t *car_status)
{
    if (app == NULL || car_status == NULL) {
        return MOBILE_APP_ERROR_NULL_POINTER;
    }
    
    if (!app->connected) {
        return MOBILE_APP_ERROR_NOT_CONNECTED;
    }
    
    /* 构造状态数据包 */
    uint8_t payload[16];
    payload[0] = (uint8_t)car_status->state;
    payload[1] = (uint8_t)car_status->mode;
    
    /* 角度数据 (转换为整数) */
    int16_t pitch = (int16_t)(car_status->pitch_angle * 100);
    int16_t roll = (int16_t)(car_status->roll_angle * 100);
    payload[2] = (uint8_t)(pitch >> 8);
    payload[3] = (uint8_t)(pitch & 0xFF);
    payload[4] = (uint8_t)(roll >> 8);
    payload[5] = (uint8_t)(roll & 0xFF);
    
    /* 电池电压 */
    uint16_t voltage = (uint16_t)(car_status->battery_voltage * 100);
    payload[6] = (uint8_t)(voltage >> 8);
    payload[7] = (uint8_t)(voltage & 0xFF);
    
    /* 运行时间 */
    uint32_t uptime = car_status->uptime_ms;
    payload[8] = (uint8_t)(uptime >> 24);
    payload[9] = (uint8_t)(uptime >> 16);
    payload[10] = (uint8_t)(uptime >> 8);
    payload[11] = (uint8_t)(uptime & 0xFF);
    
    return MobileApp_SendFrame(app, CMD_STATUS_REQUEST, payload, 12);
}

/**
 * @brief 初始化UART (私有函数)
 */
static mobile_app_result_t MobileApp_InitUART(mobile_app_t *app)
{
    /* UART6已在CubeMX中配置 */
    /* 波特率: 115200, 数据位: 8, 停止位: 1, 校验: 无 */
    
    printf("  - UART6 (蓝牙通信) 初始化完成\r\n");
    return MOBILE_APP_OK;
}

/**
 * @brief 发送帧 (私有函数)
 */
static mobile_app_result_t MobileApp_SendFrame(mobile_app_t *app, uint8_t cmd, const uint8_t *payload, uint8_t payload_len)
{
    if (payload_len > MOBILE_APP_MAX_PAYLOAD_SIZE) {
        return MOBILE_APP_ERROR_INVALID_PARAMETER;
    }
    
    mobile_app_frame_t *frame = (mobile_app_frame_t *)tx_buffer;
    
    frame->header = MOBILE_APP_FRAME_HEADER;
    frame->length = payload_len + 3; // cmd + checksum + tail
    frame->cmd = cmd;
    
    if (payload && payload_len > 0) {
        memcpy(frame->payload, payload, payload_len);
    }
    
    frame->checksum = MobileApp_CalculateChecksum((uint8_t *)frame, 3 + payload_len);
    frame->tail = MOBILE_APP_FRAME_TAIL;
    
    HAL_StatusTypeDef status = HAL_UART_Transmit(&huart6, (uint8_t *)frame, 
                                                 4 + payload_len, app->config.timeout_ms);
    
    return (status == HAL_OK) ? MOBILE_APP_OK : MOBILE_APP_ERROR_COMMUNICATION;
}

/**
 * @brief 计算校验和 (私有函数)
 */
static uint8_t MobileApp_CalculateChecksum(const uint8_t *data, uint8_t length)
{
    uint8_t checksum = 0;
    for (uint8_t i = 0; i < length; i++) {
        checksum ^= data[i];
    }
    return checksum;
}

/**
 * @brief 处理控制命令 (私有函数)
 */
static mobile_app_result_t MobileApp_HandleControlCommand(mobile_app_t *app, const uint8_t *payload, uint8_t length)
{
    if (length < 8) {
        return MOBILE_APP_ERROR_INVALID_PARAMETER;
    }
    
    /* 解析控制数据 */
    int16_t forward_raw = (int16_t)((payload[0] << 8) | payload[1]);
    int16_t turn_raw = (int16_t)((payload[2] << 8) | payload[3]);
    
    /* 转换为浮点数 (-1.0 到 1.0) */
    app->control_output.forward_speed = (float)forward_raw / 32767.0f * app->config.max_forward_speed;
    app->control_output.turn_speed = (float)turn_raw / 32767.0f * app->config.max_turn_speed;
    
    /* 死区处理 */
    if (fabs(app->control_output.forward_speed) < app->config.control_deadzone) {
        app->control_output.forward_speed = 0.0f;
    }
    if (fabs(app->control_output.turn_speed) < app->config.control_deadzone) {
        app->control_output.turn_speed = 0.0f;
    }
    
    app->control_output.valid = true;
    app->last_command_time = HAL_GetTick();
    
    return MOBILE_APP_OK;
}

/**
 * @brief UART接收完成回调
 */
void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart)
{
    if (huart->Instance == USART6) {
        frame_received = true;
        /* 继续接收下一个字节 */
        HAL_UART_Receive_IT(&huart6, rx_buffer, 1);
    }
}
