/**
 ******************************************************************************
 * @file           : motion_controller.c
 * @brief          : 运动控制器实现
 * @version        : v1.0
 * @date           : 2025-01-27
 ******************************************************************************
 * @description    :
 * 平衡车运动控制器实现，包含：
 * - 摇杆指令解析和映射
 * - 前后运动控制算法
 * - 左右转向控制算法
 * - 安全保护和限制
 * - 多种运动模式支持
 ******************************************************************************
 */

#include "motion_controller.h"
#include <math.h>
#include <string.h>

// 内部函数声明
static float apply_low_pass_filter(float new_value, float old_value, float filter_coeff);
static float apply_deadzone_and_scale(int16_t input, int16_t deadzone, float scale);
static void update_motion_state(motion_controller_t *controller);
static uint8_t check_timeout(motion_controller_t *controller);

HAL_StatusTypeDef Motion_Init(motion_controller_t *controller, motion_config_t *config) {
    if (!controller || !config) {
        return HAL_ERROR;
    }
    
    // 保存配置
    controller->config = *config;
    
    // 初始化指令
    memset(&controller->command, 0, sizeof(motion_command_t));
    
    // 初始化状态
    controller->status.state = MOTION_STATE_IDLE;
    controller->status.mode = MOTION_MODE_BALANCE;
    controller->status.current_tilt_angle = 0.0f;
    controller->status.current_speed = 0.0f;
    controller->status.current_turn_rate = 0.0f;
    controller->status.last_update_time = HAL_GetTick();
    controller->status.is_stable = 1;
    
    // 初始化内部状态
    controller->filtered_forward = 0.0f;
    controller->filtered_turn = 0.0f;
    controller->last_forward_cmd = 0.0f;
    controller->last_turn_cmd = 0.0f;
    controller->last_command_time = HAL_GetTick();
    controller->safety_enabled = 1;
    controller->initialized = 1;
    
    return HAL_OK;
}

HAL_StatusTypeDef Motion_Update(motion_controller_t *controller, 
                               joystick_data_t *joystick_data,
                               float current_angle,
                               float dt) {
    if (!controller || !controller->initialized || !joystick_data) {
        return HAL_ERROR;
    }
    
    uint32_t current_time = HAL_GetTick();
    
    // 检查超时
    if (check_timeout(controller)) {
        Motion_EmergencyStop(controller);
        return HAL_TIMEOUT;
    }
    
    // 更新当前状态
    controller->status.current_tilt_angle = current_angle;
    controller->status.last_update_time = current_time;
    
    // 安全检查
    if (controller->safety_enabled && !Motion_CheckSafety(controller, current_angle)) {
        Motion_EmergencyStop(controller);
        return HAL_ERROR;
    }
    
    // 根据运动模式处理指令
    switch (controller->status.mode) {
        case MOTION_MODE_STOP:
            // 停止模式：清零所有指令
            controller->command.target_tilt_angle = 0.0f;
            controller->command.turn_rate = 0.0f;
            controller->command.enable_motion = 0;
            break;
            
        case MOTION_MODE_BALANCE:
            // 纯平衡模式：只维持平衡，不响应摇杆
            controller->command.target_tilt_angle = 0.0f;
            controller->command.turn_rate = 0.0f;
            controller->command.enable_motion = 1;
            break;
            
        case MOTION_MODE_MANUAL:
            // 手动控制模式：响应摇杆输入
            if (joystick_data->is_valid) {
                // 获取摇杆指令
                int16_t forward_cmd = Joystick_GetForwardCommand(joystick_data);
                int16_t turn_cmd = Joystick_GetTurnCommand(joystick_data);
                
                // 应用死区和缩放
                float forward_scaled = apply_deadzone_and_scale(forward_cmd, 50, 
                                                              controller->config.tilt_sensitivity);
                float turn_scaled = apply_deadzone_and_scale(turn_cmd, 50,
                                                           controller->config.turn_sensitivity);
                
                // 应用滤波
                controller->filtered_forward = apply_low_pass_filter(forward_scaled,
                                                                   controller->filtered_forward,
                                                                   controller->config.command_filter);
                controller->filtered_turn = apply_low_pass_filter(turn_scaled,
                                                                 controller->filtered_turn,
                                                                 controller->config.command_filter);
                
                // 计算目标倾角和转向速率
                controller->command.target_tilt_angle = Motion_CalculateTargetTilt(
                    (int16_t)(controller->filtered_forward * 1000), &controller->config);
                controller->command.turn_rate = Motion_CalculateTurnRate(
                    (int16_t)(controller->filtered_turn * 1000), &controller->config);
                
                // 应用限制
                Motion_ApplyLimits(controller, &controller->command.target_tilt_angle,
                                 &controller->command.turn_rate);
                
                controller->command.enable_motion = 1;
                controller->last_command_time = current_time;
            }
            break;
            
        case MOTION_MODE_EMERGENCY:
            // 紧急模式：立即停止
            Motion_EmergencyStop(controller);
            break;
            
        default:
            controller->status.mode = MOTION_MODE_BALANCE;
            break;
    }
    
    // 更新运动状态
    update_motion_state(controller);
    
    return HAL_OK;
}

HAL_StatusTypeDef Motion_GetCommand(motion_controller_t *controller, motion_command_t *command) {
    if (!controller || !controller->initialized || !command) {
        return HAL_ERROR;
    }
    
    *command = controller->command;
    return HAL_OK;
}

HAL_StatusTypeDef Motion_SetMode(motion_controller_t *controller, motion_mode_t mode) {
    if (!controller || !controller->initialized) {
        return HAL_ERROR;
    }
    
    // 模式切换时重置状态
    if (controller->status.mode != mode) {
        controller->filtered_forward = 0.0f;
        controller->filtered_turn = 0.0f;
        controller->last_forward_cmd = 0.0f;
        controller->last_turn_cmd = 0.0f;
    }
    
    controller->status.mode = mode;
    return HAL_OK;
}

HAL_StatusTypeDef Motion_EmergencyStop(motion_controller_t *controller) {
    if (!controller) {
        return HAL_ERROR;
    }
    
    // 清零所有运动指令
    controller->command.target_tilt_angle = 0.0f;
    controller->command.turn_rate = 0.0f;
    controller->command.enable_motion = 0;
    controller->command.emergency_stop = 1;
    
    // 重置内部状态
    controller->filtered_forward = 0.0f;
    controller->filtered_turn = 0.0f;
    
    // 设置紧急状态
    controller->status.state = MOTION_STATE_ERROR;
    controller->status.mode = MOTION_MODE_EMERGENCY;
    
    return HAL_OK;
}

HAL_StatusTypeDef Motion_Reset(motion_controller_t *controller) {
    if (!controller) {
        return HAL_ERROR;
    }
    
    // 重置指令
    memset(&controller->command, 0, sizeof(motion_command_t));
    
    // 重置状态
    controller->status.state = MOTION_STATE_IDLE;
    controller->status.mode = MOTION_MODE_BALANCE;
    controller->status.current_speed = 0.0f;
    controller->status.current_turn_rate = 0.0f;
    controller->status.is_stable = 1;
    
    // 重置内部状态
    controller->filtered_forward = 0.0f;
    controller->filtered_turn = 0.0f;
    controller->last_forward_cmd = 0.0f;
    controller->last_turn_cmd = 0.0f;
    controller->last_command_time = HAL_GetTick();
    
    return HAL_OK;
}

HAL_StatusTypeDef Motion_SetSafetyParams(motion_controller_t *controller,
                                        float emergency_angle,
                                        float max_speed,
                                        uint32_t timeout_ms) {
    if (!controller) {
        return HAL_ERROR;
    }
    
    controller->config.emergency_angle = emergency_angle;
    controller->config.max_speed = max_speed;
    controller->config.timeout_ms = timeout_ms;
    
    return HAL_OK;
}

HAL_StatusTypeDef Motion_GetStatus(motion_controller_t *controller, motion_status_t *status) {
    if (!controller || !status) {
        return HAL_ERROR;
    }
    
    *status = controller->status;
    return HAL_OK;
}

uint8_t Motion_SelfTest(motion_controller_t *controller) {
    if (!controller || !controller->initialized) {
        return 0;
    }
    
    // 检查配置参数合理性
    if (controller->config.max_tilt_angle <= 0 || controller->config.max_tilt_angle > 45.0f) {
        return 0;
    }
    
    if (controller->config.max_turn_rate <= 0 || controller->config.max_turn_rate > 2.0f) {
        return 0;
    }
    
    if (controller->config.emergency_angle <= 0 || controller->config.emergency_angle > 90.0f) {
        return 0;
    }
    
    return 1; // 自检通过
}

// 平衡车专用接口实现

float Motion_CalculateTargetTilt(int16_t forward_command, motion_config_t *config) {
    if (!config) {
        return 0.0f;
    }
    
    // 将-1000~1000映射到-max_tilt_angle~max_tilt_angle
    float normalized = forward_command / 1000.0f;
    float target_tilt = normalized * config->max_tilt_angle * config->tilt_sensitivity;
    
    // 限制范围
    if (target_tilt > config->max_tilt_angle) {
        target_tilt = config->max_tilt_angle;
    } else if (target_tilt < -config->max_tilt_angle) {
        target_tilt = -config->max_tilt_angle;
    }
    
    return target_tilt;
}

float Motion_CalculateTurnRate(int16_t turn_command, motion_config_t *config) {
    if (!config) {
        return 0.0f;
    }
    
    // 将-1000~1000映射到-max_turn_rate~max_turn_rate
    float normalized = turn_command / 1000.0f;
    float turn_rate = normalized * config->max_turn_rate * config->turn_sensitivity;
    
    // 限制范围
    if (turn_rate > config->max_turn_rate) {
        turn_rate = config->max_turn_rate;
    } else if (turn_rate < -config->max_turn_rate) {
        turn_rate = -config->max_turn_rate;
    }
    
    return turn_rate;
}

void Motion_ApplyLimits(motion_controller_t *controller, float *target_tilt, float *turn_rate) {
    if (!controller || !target_tilt || !turn_rate) {
        return;
    }

    // 倾角限制
    if (*target_tilt > controller->config.max_tilt_angle) {
        *target_tilt = controller->config.max_tilt_angle;
    } else if (*target_tilt < -controller->config.max_tilt_angle) {
        *target_tilt = -controller->config.max_tilt_angle;
    }

    // 转向速率限制
    if (*turn_rate > controller->config.max_turn_rate) {
        *turn_rate = controller->config.max_turn_rate;
    } else if (*turn_rate < -controller->config.max_turn_rate) {
        *turn_rate = -controller->config.max_turn_rate;
    }

    // 加速度限制（简化版）
    float tilt_change = *target_tilt - controller->last_forward_cmd;
    if (tilt_change > controller->config.acceleration_limit) {
        *target_tilt = controller->last_forward_cmd + controller->config.acceleration_limit;
    } else if (tilt_change < -controller->config.deceleration_limit) {
        *target_tilt = controller->last_forward_cmd - controller->config.deceleration_limit;
    }

    // 更新上次指令
    controller->last_forward_cmd = *target_tilt;
    controller->last_turn_cmd = *turn_rate;
}

uint8_t Motion_CheckSafety(motion_controller_t *controller, float current_angle) {
    if (!controller) {
        return 0;
    }

    // 检查倾角是否超过紧急限制
    if (fabsf(current_angle) > controller->config.emergency_angle) {
        return 0;
    }

    // 检查控制器状态
    if (controller->status.state == MOTION_STATE_ERROR) {
        return 0;
    }

    return 1; // 安全
}

HAL_StatusTypeDef Motion_SetCruiseSpeed(motion_controller_t *controller, float cruise_speed) {
    if (!controller || !controller->initialized) {
        return HAL_ERROR;
    }

    // 限制巡航速度范围
    if (cruise_speed > 1.0f) cruise_speed = 1.0f;
    if (cruise_speed < -1.0f) cruise_speed = -1.0f;

    // 设置巡航模式
    controller->status.mode = MOTION_MODE_CRUISE;
    controller->command.target_tilt_angle = cruise_speed * controller->config.max_tilt_angle;
    controller->command.turn_rate = 0.0f;
    controller->command.enable_motion = 1;

    return HAL_OK;
}

HAL_StatusTypeDef Motion_ExecuteAction(motion_controller_t *controller, uint8_t action_id) {
    if (!controller || !controller->initialized) {
        return HAL_ERROR;
    }

    switch (action_id) {
        case 0: // 停止
            Motion_EmergencyStop(controller);
            break;

        case 1: // 前进
            Motion_SetCruiseSpeed(controller, 0.3f);
            break;

        case 2: // 后退
            Motion_SetCruiseSpeed(controller, -0.3f);
            break;

        case 3: // 左转
            controller->command.turn_rate = -0.5f;
            break;

        case 4: // 右转
            controller->command.turn_rate = 0.5f;
            break;

        case 5: // 重置
            Motion_Reset(controller);
            break;

        default:
            return HAL_ERROR;
    }

    return HAL_OK;
}

void Motion_GetStatistics(motion_controller_t *controller, char *info_str, uint16_t max_len) {
    if (!controller || !info_str || max_len < 200) {
        return;
    }

    const char* mode_names[] = {"STOP", "BALANCE", "MANUAL", "AUTO", "CRUISE", "EMERGENCY"};
    const char* state_names[] = {"IDLE", "MOVING", "TURNING", "BRAKING", "ERROR"};

    snprintf(info_str, max_len,
        "Motion Controller Status:\n"
        "Mode: %s\n"
        "State: %s\n"
        "Target Tilt: %.2f°\n"
        "Turn Rate: %.2f\n"
        "Current Angle: %.2f°\n"
        "Speed: %.2f\n"
        "Stable: %s\n"
        "Safety: %s\n",
        mode_names[controller->status.mode],
        state_names[controller->status.state],
        controller->command.target_tilt_angle,
        controller->command.turn_rate,
        controller->status.current_tilt_angle,
        controller->status.current_speed,
        controller->status.is_stable ? "Yes" : "No",
        controller->safety_enabled ? "Enabled" : "Disabled"
    );
}

// 内部函数实现

static float apply_low_pass_filter(float new_value, float old_value, float filter_coeff) {
    // 一阶低通滤波器
    return old_value + filter_coeff * (new_value - old_value);
}

static float apply_deadzone_and_scale(int16_t input, int16_t deadzone, float scale) {
    if (abs(input) < deadzone) {
        return 0.0f;
    }

    // 去除死区后重新映射
    float normalized;
    if (input > 0) {
        normalized = (float)(input - deadzone) / (1000.0f - deadzone);
    } else {
        normalized = (float)(input + deadzone) / (1000.0f - deadzone);
    }

    return normalized * scale;
}

static void update_motion_state(motion_controller_t *controller) {
    if (!controller) {
        return;
    }

    // 根据指令更新运动状态
    float tilt_threshold = 1.0f;
    float turn_threshold = 0.1f;

    if (controller->command.emergency_stop) {
        controller->status.state = MOTION_STATE_ERROR;
    } else if (fabsf(controller->command.target_tilt_angle) > tilt_threshold) {
        controller->status.state = MOTION_STATE_MOVING;
    } else if (fabsf(controller->command.turn_rate) > turn_threshold) {
        controller->status.state = MOTION_STATE_TURNING;
    } else {
        controller->status.state = MOTION_STATE_IDLE;
    }

    // 更新稳定状态
    controller->status.is_stable = (controller->status.state == MOTION_STATE_IDLE) &&
                                  (fabsf(controller->status.current_tilt_angle) < 5.0f);
}

static uint8_t check_timeout(motion_controller_t *controller) {
    if (!controller || controller->config.timeout_ms == 0) {
        return 0;
    }

    uint32_t current_time = HAL_GetTick();
    return (current_time - controller->last_command_time) > controller->config.timeout_ms;
}
