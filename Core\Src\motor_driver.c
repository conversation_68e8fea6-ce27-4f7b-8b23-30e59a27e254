/**
 ******************************************************************************
 * @file           : motor_driver.c
 * @brief          : 电机驱动实现
 * @version        : v1.0
 * @date           : 2025-01-27
 ******************************************************************************
 * @description    :
 * 双电机PWM驱动实现，支持多种H桥驱动芯片：
 * 
 * L298N驱动方式：
 * - 正转：DIR1=1, DIR2=0, PWM=速度
 * - 反转：DIR1=0, DIR2=1, PWM=速度
 * - 停止：DIR1=0, DIR2=0, PWM=0
 * 
 * TB6612FNG驱动方式：
 * - 正转：DIR1=1, DIR2=0, PWM=速度
 * - 反转：DIR1=0, DIR2=1, PWM=速度
 * - 刹车：DIR1=1, DIR2=1, PWM=0
 ******************************************************************************
 */

#include "motor_driver.h"

// 静态变量
static motor_config_t motor_cfg;
static int16_t current_left_speed = 0;
static int16_t current_right_speed = 0;
static uint8_t motor_enabled = 0;

// 内部函数声明
static void set_motor_direction(uint8_t motor, int16_t speed);
static void set_motor_pwm(uint8_t motor, uint16_t pwm_value);

HAL_StatusTypeDef Motor_Init(motor_config_t *config) {
    if (!config || !config->htim) {
        return HAL_ERROR;
    }
    
    // 保存配置
    motor_cfg = *config;
    
    // 启动PWM输出
    HAL_StatusTypeDef status;
    status = HAL_TIM_PWM_Start(motor_cfg.htim, motor_cfg.left_pwm_channel);
    if (status != HAL_OK) return status;
    
    status = HAL_TIM_PWM_Start(motor_cfg.htim, motor_cfg.right_pwm_channel);
    if (status != HAL_OK) return status;
    
    // 初始化GPIO状态
    HAL_GPIO_WritePin(motor_cfg.left_dir1_port, motor_cfg.left_dir1_pin, GPIO_PIN_RESET);
    HAL_GPIO_WritePin(motor_cfg.left_dir2_port, motor_cfg.left_dir2_pin, GPIO_PIN_RESET);
    HAL_GPIO_WritePin(motor_cfg.right_dir1_port, motor_cfg.right_dir1_pin, GPIO_PIN_RESET);
    HAL_GPIO_WritePin(motor_cfg.right_dir2_port, motor_cfg.right_dir2_pin, GPIO_PIN_RESET);
    HAL_GPIO_WritePin(motor_cfg.enable_port, motor_cfg.enable_pin, GPIO_PIN_RESET);
    
    // 初始状态：停止
    Motor_Stop();
    
    return HAL_OK;
}

void Motor_SetSpeed(int16_t left_speed, int16_t right_speed) {
    // 限制速度范围
    if (left_speed > 1000) left_speed = 1000;
    if (left_speed < -1000) left_speed = -1000;
    if (right_speed > 1000) right_speed = 1000;
    if (right_speed < -1000) right_speed = -1000;
    
    current_left_speed = left_speed;
    current_right_speed = right_speed;
    
    // 如果电机未启用，不设置PWM
    if (!motor_enabled) {
        return;
    }
    
    // 设置左电机
    set_motor_direction(0, left_speed);  // 0 = 左电机
    set_motor_pwm(0, abs(left_speed));
    
    // 设置右电机
    set_motor_direction(1, right_speed); // 1 = 右电机
    set_motor_pwm(1, abs(right_speed));
}

void Motor_Enable(uint8_t enable) {
    motor_enabled = enable;
    
    if (enable) {
        // 启用电机驱动
        HAL_GPIO_WritePin(motor_cfg.enable_port, motor_cfg.enable_pin, GPIO_PIN_SET);
        // 恢复之前的速度设定
        Motor_SetSpeed(current_left_speed, current_right_speed);
    } else {
        // 禁用电机驱动
        Motor_Stop();
        HAL_GPIO_WritePin(motor_cfg.enable_port, motor_cfg.enable_pin, GPIO_PIN_RESET);
    }
}

void Motor_Stop(void) {
    // 停止PWM输出
    set_motor_pwm(0, 0);  // 左电机
    set_motor_pwm(1, 0);  // 右电机
    
    // 根据驱动器类型设置停止状态
    switch (motor_cfg.driver_type) {
        case MOTOR_DRIVER_L298N:
            // L298N: DIR1=0, DIR2=0 为停止
            HAL_GPIO_WritePin(motor_cfg.left_dir1_port, motor_cfg.left_dir1_pin, GPIO_PIN_RESET);
            HAL_GPIO_WritePin(motor_cfg.left_dir2_port, motor_cfg.left_dir2_pin, GPIO_PIN_RESET);
            HAL_GPIO_WritePin(motor_cfg.right_dir1_port, motor_cfg.right_dir1_pin, GPIO_PIN_RESET);
            HAL_GPIO_WritePin(motor_cfg.right_dir2_port, motor_cfg.right_dir2_pin, GPIO_PIN_RESET);
            break;
            
        case MOTOR_DRIVER_TB6612FNG:
            // TB6612FNG: DIR1=1, DIR2=1 为刹车
            HAL_GPIO_WritePin(motor_cfg.left_dir1_port, motor_cfg.left_dir1_pin, GPIO_PIN_SET);
            HAL_GPIO_WritePin(motor_cfg.left_dir2_port, motor_cfg.left_dir2_pin, GPIO_PIN_SET);
            HAL_GPIO_WritePin(motor_cfg.right_dir1_port, motor_cfg.right_dir1_pin, GPIO_PIN_SET);
            HAL_GPIO_WritePin(motor_cfg.right_dir2_port, motor_cfg.right_dir2_pin, GPIO_PIN_SET);
            break;
            
        default:
            // 默认停止方式
            HAL_GPIO_WritePin(motor_cfg.left_dir1_port, motor_cfg.left_dir1_pin, GPIO_PIN_RESET);
            HAL_GPIO_WritePin(motor_cfg.left_dir2_port, motor_cfg.left_dir2_pin, GPIO_PIN_RESET);
            HAL_GPIO_WritePin(motor_cfg.right_dir1_port, motor_cfg.right_dir1_pin, GPIO_PIN_RESET);
            HAL_GPIO_WritePin(motor_cfg.right_dir2_port, motor_cfg.right_dir2_pin, GPIO_PIN_RESET);
            break;
    }
}

void Motor_GetSpeed(int16_t *left_speed, int16_t *right_speed) {
    if (left_speed) *left_speed = current_left_speed;
    if (right_speed) *right_speed = current_right_speed;
}

uint8_t Motor_SelfTest(void) {
    // 简单的自检：测试PWM输出和GPIO控制
    
    // 测试左电机正转
    Motor_Enable(1);
    Motor_SetSpeed(200, 0);
    HAL_Delay(100);
    
    // 测试左电机反转
    Motor_SetSpeed(-200, 0);
    HAL_Delay(100);
    
    // 测试右电机正转
    Motor_SetSpeed(0, 200);
    HAL_Delay(100);
    
    // 测试右电机反转
    Motor_SetSpeed(0, -200);
    HAL_Delay(100);
    
    // 停止测试
    Motor_Stop();
    Motor_Enable(0);
    
    return 0; // 简化实现，总是返回成功
}

void Motor_SetDeadTime(uint16_t dead_time_us) {
    // 这个功能需要根据具体的定时器配置实现
    // 对于基本的H桥驱动，通常不需要软件死区
    (void)dead_time_us; // 避免未使用参数警告
}

// 内部函数实现

static void set_motor_direction(uint8_t motor, int16_t speed) {
    GPIO_TypeDef *dir1_port, *dir2_port;
    uint16_t dir1_pin, dir2_pin;
    
    if (motor == 0) { // 左电机
        dir1_port = motor_cfg.left_dir1_port;
        dir1_pin = motor_cfg.left_dir1_pin;
        dir2_port = motor_cfg.left_dir2_port;
        dir2_pin = motor_cfg.left_dir2_pin;
    } else { // 右电机
        dir1_port = motor_cfg.right_dir1_port;
        dir1_pin = motor_cfg.right_dir1_pin;
        dir2_port = motor_cfg.right_dir2_port;
        dir2_pin = motor_cfg.right_dir2_pin;
    }
    
    if (speed > 0) {
        // 正转：DIR1=1, DIR2=0
        HAL_GPIO_WritePin(dir1_port, dir1_pin, GPIO_PIN_SET);
        HAL_GPIO_WritePin(dir2_port, dir2_pin, GPIO_PIN_RESET);
    } else if (speed < 0) {
        // 反转：DIR1=0, DIR2=1
        HAL_GPIO_WritePin(dir1_port, dir1_pin, GPIO_PIN_RESET);
        HAL_GPIO_WritePin(dir2_port, dir2_pin, GPIO_PIN_SET);
    } else {
        // 停止：根据驱动器类型设置
        switch (motor_cfg.driver_type) {
            case MOTOR_DRIVER_TB6612FNG:
                // 刹车：DIR1=1, DIR2=1
                HAL_GPIO_WritePin(dir1_port, dir1_pin, GPIO_PIN_SET);
                HAL_GPIO_WritePin(dir2_port, dir2_pin, GPIO_PIN_SET);
                break;
            default:
                // 停止：DIR1=0, DIR2=0
                HAL_GPIO_WritePin(dir1_port, dir1_pin, GPIO_PIN_RESET);
                HAL_GPIO_WritePin(dir2_port, dir2_pin, GPIO_PIN_RESET);
                break;
        }
    }
}

static void set_motor_pwm(uint8_t motor, uint16_t pwm_value) {
    // 将0-1000的速度值转换为PWM占空比
    uint32_t pwm_compare = (pwm_value * motor_cfg.pwm_resolution) / 1000;
    
    if (motor == 0) { // 左电机
        __HAL_TIM_SET_COMPARE(motor_cfg.htim, motor_cfg.left_pwm_channel, pwm_compare);
    } else { // 右电机
        __HAL_TIM_SET_COMPARE(motor_cfg.htim, motor_cfg.right_pwm_channel, pwm_compare);
    }
}