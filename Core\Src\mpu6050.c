/**
  ******************************************************************************
  * @file           : mpu6050.c
  * @brief          : MPU6050传感器驱动实现
  * @version        : v1.0
  * @date           : 2025-06-29
  ******************************************************************************
  * @description    :
  * MPU6050六轴传感器驱动程序，专为平衡车应用优化
  *
  * 主要功能:
  * - MPU6050初始化和配置
  * - 原始数据读取和转换
  * - 卡尔曼滤波器数据融合
  * - 陀螺仪零点校准
  * - 角度零点校准 (新增)
  * - Flash校准数据存储
  * - 平衡车专用参数计算
  *
  * 技术特点:
  * - 双重校准机制 (陀螺仪+角度)
  * - 校准数据持久化存储
  * - 实时角度和角速度计算
  * - 平衡车控制参数输出
  ******************************************************************************
  */

#include "mpu6050.h"
#include "mpu6050_config.h"
#include "app_config.h"
#include "Kalman.h"
#include <math.h>
#include <string.h>

#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

// 卡尔曼滤波器
Kalman_t kalman_x = {.Q_angle = 0.001f, .Q_bias = 0.003f, .R_measure = 0.03f};
Kalman_t kalman_y = {.Q_angle = 0.001f, .Q_bias = 0.003f, .R_measure = 0.03f};
uint32_t last_time = 0;

// 校准数据
static float gyro_offset_x = 0.0f;
static float gyro_offset_y = 0.0f;
static float gyro_offset_z = 0.0f;
static float angle_offset_pitch = 0.0f;  // 俯仰角零点偏移
static float angle_offset_roll = 0.0f;   // 横滚角零点偏移

// 平衡车控制参数
static float last_pitch = 0.0f;
static uint32_t last_calc_time = 0;

// Flash校准数据保存
#define CALIBRATION_FLASH_ADDR 0x0807F800  // STM32F411最后一页
#define CALIBRATION_MAGIC 0x12345678       // 校准数据有效性标识

typedef struct {
    float gyro_offset_x;
    float gyro_offset_y;
    float gyro_offset_z;
    float angle_offset_pitch;
    float angle_offset_roll;
    uint32_t magic_number;
} calibration_flash_t;

app_error_t mpu6050_init(I2C_HandleTypeDef *hi2c) {
    uint8_t data;

    if (!hi2c) return APP_ERROR_INIT;

    // 检查设备ID
    if (HAL_I2C_Mem_Read(hi2c, MPU6050_I2C_ADDR, MPU6050_REG_WHO_AM_I, 1, &data, 1, I2C_TIMEOUT_MS) != HAL_OK) {
        return APP_ERROR_I2C;
    }
    if (data != MPU6050_DEVICE_ID) {
        return APP_ERROR_SENSOR;
    }

    // 唤醒设备
    data = 0x00;
    if (HAL_I2C_Mem_Write(hi2c, MPU6050_I2C_ADDR, MPU6050_REG_PWR_MGMT_1, 1, &data, 1, I2C_TIMEOUT_MS) != HAL_OK) {
        return APP_ERROR_I2C;
    }
    HAL_Delay(10);

    // 配置加速度计 (±2g)
    data = 0x00;
    HAL_I2C_Mem_Write(hi2c, MPU6050_I2C_ADDR, MPU6050_REG_ACCEL_CONFIG, 1, &data, 1, I2C_TIMEOUT_MS);

    // 配置陀螺仪 (±250°/s)
    data = 0x00;
    HAL_I2C_Mem_Write(hi2c, MPU6050_I2C_ADDR, MPU6050_REG_GYRO_CONFIG, 1, &data, 1, I2C_TIMEOUT_MS);

    // 初始化卡尔曼滤波器
    Kalman_Init(&kalman_x);
    Kalman_Init(&kalman_y);
    last_time = HAL_GetTick();

    return APP_OK;
}

app_error_t mpu6050_read_data(I2C_HandleTypeDef *hi2c, mpu6050_data_t *data) {
    uint8_t raw_data[14];

    if (!hi2c || !data) return APP_ERROR_INIT;

    // 读取所有数据
    if (HAL_I2C_Mem_Read(hi2c, MPU6050_I2C_ADDR, MPU6050_REG_ACCEL_XOUT_H, 1, raw_data, 14, I2C_TIMEOUT_MS) != HAL_OK) {
        return APP_ERROR_I2C;
    }

    // 解析原始数据
    data->accel_x_raw = (int16_t)(raw_data[0] << 8 | raw_data[1]);
    data->accel_y_raw = (int16_t)(raw_data[2] << 8 | raw_data[3]);
    data->accel_z_raw = (int16_t)(raw_data[4] << 8 | raw_data[5]);
    data->temp_raw = (int16_t)(raw_data[6] << 8 | raw_data[7]);
    data->gyro_x_raw = (int16_t)(raw_data[8] << 8 | raw_data[9]);
    data->gyro_y_raw = (int16_t)(raw_data[10] << 8 | raw_data[11]);
    data->gyro_z_raw = (int16_t)(raw_data[12] << 8 | raw_data[13]);

    // 转换为物理量
    data->ax = (float)data->accel_x_raw / MPU6050_ACCEL_LSB;
    data->ay = (float)data->accel_y_raw / MPU6050_ACCEL_LSB;
    data->az = (float)data->accel_z_raw / 14418.0f; // Z轴校正
    data->gx = (float)data->gyro_x_raw / MPU6050_GYRO_LSB - gyro_offset_x;
    data->gy = (float)data->gyro_y_raw / MPU6050_GYRO_LSB - gyro_offset_y;
    data->gz = (float)data->gyro_z_raw / MPU6050_GYRO_LSB - gyro_offset_z;
    data->temperature = (float)data->temp_raw / MPU6050_TEMP_LSB + MPU6050_TEMP_OFFSET;

    // 数据有效性检查
    if (fabs(data->ax) > MPU6050_ACCEL_MAX || fabs(data->ay) > MPU6050_ACCEL_MAX ||
        fabs(data->gx) > MPU6050_GYRO_MAX || fabs(data->gy) > MPU6050_GYRO_MAX) {
        return APP_ERROR_DATA;
    }

    // 计算姿态角
    uint32_t now = HAL_GetTick();
    float dt = (now - last_time) / 1000.0f;

    // 第一次调用时初始化时间
    if (last_time == 0) {
        last_time = now;
        dt = 0.01f; // 使用默认时间间隔
    } else {
        last_time = now;
    }

    // 限制dt范围，确保稳定性
    if (dt < 0.001f) dt = 0.001f;
    if (dt > 0.1f) dt = 0.1f;

    // 计算基于加速度的角度（度）
    double pitch_acc = atan2(data->ay, sqrt(data->ax*data->ax + data->az*data->az)) * 180.0/M_PI;
    double roll_acc = atan2(-data->ax, data->az) * 180.0/M_PI;

    // 使用卡尔曼滤波器融合数据
    data->pitch = Kalman_getAngle(&kalman_y, pitch_acc, data->gy, dt);
    data->roll = Kalman_getAngle(&kalman_x, roll_acc, data->gx, dt);

    // 如果卡尔曼滤波结果为0，使用简单的加速度计角度
    if (data->pitch == 0.0f && data->roll == 0.0f) {
        data->pitch = pitch_acc;
        data->roll = roll_acc;
    }

    // 应用角度零点校准偏移
    data->pitch -= angle_offset_pitch;
    data->roll -= angle_offset_roll;

    // 计算平衡车专用参数
    data->pitch_rate = data->gy;  // 俯仰角速度就是Y轴陀螺仪数据
    data->roll_rate = data->gx;   // 横滚角速度就是X轴陀螺仪数据

    // 计算俯仰角加速度（角度变化率）
    uint32_t calc_now = HAL_GetTick();
    if (last_calc_time != 0) {
        float calc_dt = (calc_now - last_calc_time) / 1000.0f;
        if (calc_dt > 0.001f && calc_dt < 0.1f) {
            data->pitch_accel = (data->pitch - last_pitch) / calc_dt;
        } else {
            data->pitch_accel = 0.0f;
        }
    } else {
        data->pitch_accel = 0.0f;
    }
    last_pitch = data->pitch;
    last_calc_time = calc_now;

    // 计算平衡角度（相对于垂直位置，垂直为0度）
    data->balance_angle = data->pitch;  // 对于平衡车，俯仰角就是平衡角

    return APP_OK;
}

// 自检功能
app_error_t mpu6050_self_test(I2C_HandleTypeDef *hi2c) {
    uint8_t device_id;
    mpu6050_data_t test_data;

    // 1. 检查设备ID
    if (HAL_I2C_Mem_Read(hi2c, MPU6050_I2C_ADDR, MPU6050_REG_WHO_AM_I, 1, &device_id, 1, I2C_TIMEOUT_MS) != HAL_OK) {
        return APP_ERROR_I2C;
    }
    if (device_id != MPU6050_DEVICE_ID) {
        return APP_ERROR_SENSOR;
    }

    // 2. 读取几次数据检查稳定性
    for (int i = 0; i < 5; i++) {
        app_error_t result = mpu6050_read_data(hi2c, &test_data);
        if (result != APP_OK) {
            return result;
        }
        HAL_Delay(10);
    }

    // 3. 检查数据范围是否合理
    if (fabs(test_data.ax) > 3.0f || fabs(test_data.ay) > 3.0f ||
        fabs(test_data.gx) > 300.0f || fabs(test_data.gy) > 300.0f ||
        test_data.temperature < -20.0f || test_data.temperature > 60.0f) {
        return APP_ERROR_DATA;
    }

    return APP_OK;
}

// 陀螺仪和角度零点校准
app_error_t mpu6050_calibrate(I2C_HandleTypeDef *hi2c) {
    const int CALIBRATION_SAMPLES = 100;
    mpu6050_data_t temp_data;
    float sum_gx = 0, sum_gy = 0, sum_gz = 0;
    float sum_pitch = 0, sum_roll = 0;

    // 采集校准数据
    for (int i = 0; i < CALIBRATION_SAMPLES; i++) {
        app_error_t result = mpu6050_read_data(hi2c, &temp_data);
        if (result != APP_OK) {
            return result;
        }

        sum_gx += temp_data.gx;
        sum_gy += temp_data.gy;
        sum_gz += temp_data.gz;

        // 计算当前的基于加速度计的角度（不使用卡尔曼滤波）
        double pitch_acc = atan2(temp_data.ay, sqrt(temp_data.ax*temp_data.ax + temp_data.az*temp_data.az)) * 180.0/M_PI;
        double roll_acc = atan2(-temp_data.ax, temp_data.az) * 180.0/M_PI;

        sum_pitch += pitch_acc;
        sum_roll += roll_acc;

        HAL_Delay(5);
    }

    // 计算陀螺仪偏移量
    gyro_offset_x = sum_gx / CALIBRATION_SAMPLES;
    gyro_offset_y = sum_gy / CALIBRATION_SAMPLES;
    gyro_offset_z = sum_gz / CALIBRATION_SAMPLES;

    // 计算角度零点偏移量（假设校准时传感器处于水平位置）
    angle_offset_pitch = sum_pitch / CALIBRATION_SAMPLES;
    angle_offset_roll = sum_roll / CALIBRATION_SAMPLES;

    return APP_OK;
}

// 保存校准数据到Flash
app_error_t mpu6050_save_calibration(void) {
    calibration_flash_t cal_data;
    HAL_StatusTypeDef status;

    // 准备校准数据
    cal_data.gyro_offset_x = gyro_offset_x;
    cal_data.gyro_offset_y = gyro_offset_y;
    cal_data.gyro_offset_z = gyro_offset_z;
    cal_data.angle_offset_pitch = angle_offset_pitch;
    cal_data.angle_offset_roll = angle_offset_roll;
    cal_data.magic_number = CALIBRATION_MAGIC;

    // 解锁Flash
    HAL_FLASH_Unlock();

    // 擦除扇区
    FLASH_EraseInitTypeDef erase_init;
    uint32_t sector_error;
    erase_init.TypeErase = FLASH_TYPEERASE_SECTORS;
    erase_init.Sector = 7;  // STM32F411扇区7 (最后一个扇区)
    erase_init.NbSectors = 1;
    erase_init.VoltageRange = FLASH_VOLTAGE_RANGE_3;

    status = HAL_FLASHEx_Erase(&erase_init, &sector_error);
    if (status != HAL_OK) {
        HAL_FLASH_Lock();
        return APP_ERROR_INIT;
    }

    // 写入数据
    uint32_t *data_ptr = (uint32_t*)&cal_data;
    for (int i = 0; i < sizeof(calibration_flash_t)/4; i++) {
        status = HAL_FLASH_Program(FLASH_TYPEPROGRAM_WORD,
                                   CALIBRATION_FLASH_ADDR + i*4,
                                   data_ptr[i]);
        if (status != HAL_OK) {
            HAL_FLASH_Lock();
            return APP_ERROR_INIT;
        }
    }

    HAL_FLASH_Lock();
    return APP_OK;
}

// 从Flash加载校准数据
app_error_t mpu6050_load_calibration(void) {
    calibration_flash_t *cal_data = (calibration_flash_t*)CALIBRATION_FLASH_ADDR;

    // 检查数据有效性
    if (cal_data->magic_number != CALIBRATION_MAGIC) {
        return APP_ERROR_DATA;  // 没有有效的校准数据
    }

    // 加载校准数据
    gyro_offset_x = cal_data->gyro_offset_x;
    gyro_offset_y = cal_data->gyro_offset_y;
    gyro_offset_z = cal_data->gyro_offset_z;
    angle_offset_pitch = cal_data->angle_offset_pitch;
    angle_offset_roll = cal_data->angle_offset_roll;

    return APP_OK;
}