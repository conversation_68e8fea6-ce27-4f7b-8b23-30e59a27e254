/**
 ******************************************************************************
 * @file           : odrive_driver.c
 * @brief          : ODrive 3.6电机控制板驱动实现
 * @version        : v1.0
 * @date           : 2025-01-27
 ******************************************************************************
 * @description    :
 * ODrive 3.6 UART通信协议实现：
 * 
 * 命令格式：
 * - 设置速度：v <axis> <velocity>\n
 * - 设置位置：p <axis> <position> <velocity_limit>\n
 * - 设置电流：c <axis> <current>\n
 * - 读取状态：r axis<axis>.encoder.vel_estimate\n
 * - 启动轴：w axis<axis>.requested_state 8\n
 * - 停止轴：w axis<axis>.requested_state 1\n
 * 
 * 响应格式：
 * - 数值响应：<value>\n
 * - 错误响应：ERROR\n
 ******************************************************************************
 */

#include "odrive_driver.h"
#include <stdio.h>
#include <string.h>
#include <stdlib.h>

// 静态变量
static odrive_config_t odrive_cfg;
static odrive_motor_status_t motor_status[2];
static int16_t current_left_speed = 0;
static int16_t current_right_speed = 0;
static uint8_t motors_enabled = 0;
static uint32_t last_comm_time = 0;

// 内部函数声明
static HAL_StatusTypeDef send_command(const char *cmd);
static HAL_StatusTypeDef send_command_with_response(const char *cmd, char *response, uint16_t max_len);
static float parse_float_response(const char *response);
static HAL_StatusTypeDef wait_for_response(char *buffer, uint16_t max_len, uint32_t timeout);

HAL_StatusTypeDef ODrive_Init(odrive_config_t *config) {
    if (!config || !config->huart) {
        return HAL_ERROR;
    }
    
    // 保存配置
    odrive_cfg = *config;
    
    // 初始化状态
    memset(motor_status, 0, sizeof(motor_status));
    current_left_speed = 0;
    current_right_speed = 0;
    motors_enabled = 0;
    last_comm_time = HAL_GetTick();
    
    // 清除UART缓冲区
    __HAL_UART_FLUSH_DRREGISTER(odrive_cfg.huart);
    
    // 测试连接
    if (!ODrive_IsConnected()) {
        return HAL_ERROR;
    }
    
    // 清除所有错误
    ODrive_ClearErrors(0);
    ODrive_ClearErrors(1);
    
    // 设置控制模式为速度控制
    ODrive_SetControlMode(0, ODRIVE_CONTROL_VELOCITY);
    ODrive_SetControlMode(1, ODRIVE_CONTROL_VELOCITY);
    
    return HAL_OK;
}

HAL_StatusTypeDef ODrive_SetVelocity(uint8_t axis, float velocity) {
    if (axis > 1) return HAL_ERROR;
    
    // 限制速度范围
    if (velocity > odrive_cfg.max_velocity) velocity = odrive_cfg.max_velocity;
    if (velocity < -odrive_cfg.max_velocity) velocity = -odrive_cfg.max_velocity;
    
    char cmd[64];
    snprintf(cmd, sizeof(cmd), "v %d %.3f\n", axis, velocity);
    
    HAL_StatusTypeDef status = send_command(cmd);
    if (status == HAL_OK) {
        last_comm_time = HAL_GetTick();
    }
    
    return status;
}

HAL_StatusTypeDef ODrive_SetPosition(uint8_t axis, float position, float velocity_limit) {
    if (axis > 1) return HAL_ERROR;
    
    char cmd[64];
    snprintf(cmd, sizeof(cmd), "p %d %.3f %.3f\n", axis, position, velocity_limit);
    
    return send_command(cmd);
}

HAL_StatusTypeDef ODrive_SetCurrent(uint8_t axis, float current) {
    if (axis > 1) return HAL_ERROR;
    
    // 限制电流范围
    if (current > odrive_cfg.max_current) current = odrive_cfg.max_current;
    if (current < -odrive_cfg.max_current) current = -odrive_cfg.max_current;
    
    char cmd[64];
    snprintf(cmd, sizeof(cmd), "c %d %.3f\n", axis, current);
    
    return send_command(cmd);
}

HAL_StatusTypeDef ODrive_StartAxis(uint8_t axis) {
    if (axis > 1) return HAL_ERROR;
    
    char cmd[64];
    snprintf(cmd, sizeof(cmd), "w axis%d.requested_state 8\n", axis);
    
    return send_command(cmd);
}

HAL_StatusTypeDef ODrive_StopAxis(uint8_t axis) {
    if (axis > 1) return HAL_ERROR;
    
    char cmd[64];
    snprintf(cmd, sizeof(cmd), "w axis%d.requested_state 1\n", axis);
    
    return send_command(cmd);
}

HAL_StatusTypeDef ODrive_GetStatus(uint8_t axis, odrive_motor_status_t *status) {
    if (axis > 1 || !status) return HAL_ERROR;
    
    char cmd[64];
    char response[32];
    
    // 读取速度
    snprintf(cmd, sizeof(cmd), "r axis%d.encoder.vel_estimate\n", axis);
    if (send_command_with_response(cmd, response, sizeof(response)) == HAL_OK) {
        status->velocity = parse_float_response(response);
        status->is_valid = 1;
    } else {
        status->is_valid = 0;
        return HAL_ERROR;
    }
    
    // 读取位置
    snprintf(cmd, sizeof(cmd), "r axis%d.encoder.pos_estimate\n", axis);
    if (send_command_with_response(cmd, response, sizeof(response)) == HAL_OK) {
        status->position = parse_float_response(response);
    }
    
    // 读取电流
    snprintf(cmd, sizeof(cmd), "r axis%d.motor.current_control.Iq_measured\n", axis);
    if (send_command_with_response(cmd, response, sizeof(response)) == HAL_OK) {
        status->current = parse_float_response(response);
    }
    
    // 保存到全局状态
    motor_status[axis] = *status;
    
    return HAL_OK;
}

HAL_StatusTypeDef ODrive_ClearErrors(uint8_t axis) {
    if (axis > 1) return HAL_ERROR;
    
    char cmd[64];
    snprintf(cmd, sizeof(cmd), "w axis%d.error 0\n", axis);
    send_command(cmd);
    
    snprintf(cmd, sizeof(cmd), "w axis%d.motor.error 0\n", axis);
    send_command(cmd);
    
    snprintf(cmd, sizeof(cmd), "w axis%d.encoder.error 0\n", axis);
    send_command(cmd);
    
    return HAL_OK;
}

HAL_StatusTypeDef ODrive_EmergencyStop(void) {
    // 立即停止所有轴
    ODrive_SetVelocity(0, 0.0f);
    ODrive_SetVelocity(1, 0.0f);
    
    HAL_Delay(10);
    
    ODrive_StopAxis(0);
    ODrive_StopAxis(1);
    
    motors_enabled = 0;
    current_left_speed = 0;
    current_right_speed = 0;
    
    return HAL_OK;
}

uint8_t ODrive_IsConnected(void) {
    char response[16];
    
    // 发送简单的读取命令测试连接
    if (send_command_with_response("r vbus_voltage\n", response, sizeof(response)) == HAL_OK) {
        float voltage = parse_float_response(response);
        return (voltage > 5.0f && voltage < 60.0f); // 合理的电压范围
    }
    
    return 0;
}

HAL_StatusTypeDef ODrive_SetControlMode(uint8_t axis, odrive_control_mode_t mode) {
    if (axis > 1) return HAL_ERROR;
    
    char cmd[64];
    
    // 设置控制模式
    snprintf(cmd, sizeof(cmd), "w axis%d.controller.config.control_mode %d\n", axis, mode);
    send_command(cmd);
    
    // 设置输入模式为无源输入
    snprintf(cmd, sizeof(cmd), "w axis%d.controller.config.input_mode 1\n", axis);
    send_command(cmd);
    
    return HAL_OK;
}

// 平衡车专用接口实现

void ODrive_SetMotorSpeed(int16_t left_speed, int16_t right_speed) {
    // 限制速度范围
    if (left_speed > 1000) left_speed = 1000;
    if (left_speed < -1000) left_speed = -1000;
    if (right_speed > 1000) right_speed = 1000;
    if (right_speed < -1000) right_speed = -1000;
    
    current_left_speed = left_speed;
    current_right_speed = right_speed;
    
    if (!motors_enabled) {
        return;
    }
    
    // 将-1000到1000的范围转换为ODrive速度 (turns/s)
    // 假设最大速度为10 turns/s
    float left_velocity = (left_speed / 1000.0f) * odrive_cfg.max_velocity;
    float right_velocity = (right_speed / 1000.0f) * odrive_cfg.max_velocity;
    
    // 注意：根据电机安装方向，可能需要反转其中一个电机
    ODrive_SetVelocity(0, left_velocity);   // 左电机 - axis0
    ODrive_SetVelocity(1, -right_velocity); // 右电机 - axis1 (反转)
}

void ODrive_EnableMotors(uint8_t enable) {
    if (enable && !motors_enabled) {
        // 启动电机
        ODrive_StartAxis(0);
        ODrive_StartAxis(1);
        motors_enabled = 1;
        
        // 恢复之前的速度设定
        ODrive_SetMotorSpeed(current_left_speed, current_right_speed);
    } else if (!enable && motors_enabled) {
        // 停止电机
        ODrive_SetVelocity(0, 0.0f);
        ODrive_SetVelocity(1, 0.0f);
        HAL_Delay(50);
        
        ODrive_StopAxis(0);
        ODrive_StopAxis(1);
        motors_enabled = 0;
    }
}

void ODrive_GetMotorSpeed(int16_t *left_speed, int16_t *right_speed) {
    if (left_speed) *left_speed = current_left_speed;
    if (right_speed) *right_speed = current_right_speed;
}

// 内部函数实现

static HAL_StatusTypeDef send_command(const char *cmd) {
    uint16_t len = strlen(cmd);
    return HAL_UART_Transmit(odrive_cfg.huart, (uint8_t*)cmd, len, odrive_cfg.timeout_ms);
}

static HAL_StatusTypeDef send_command_with_response(const char *cmd, char *response, uint16_t max_len) {
    // 发送命令
    if (send_command(cmd) != HAL_OK) {
        return HAL_ERROR;
    }
    
    // 等待响应
    return wait_for_response(response, max_len, odrive_cfg.timeout_ms);
}

static float parse_float_response(const char *response) {
    return atof(response);
}

static HAL_StatusTypeDef wait_for_response(char *buffer, uint16_t max_len, uint32_t timeout) {
    uint16_t index = 0;
    uint32_t start_time = HAL_GetTick();

    memset(buffer, 0, max_len);

    while ((HAL_GetTick() - start_time) < timeout && index < (max_len - 1)) {
        uint8_t byte;
        if (HAL_UART_Receive(odrive_cfg.huart, &byte, 1, 1) == HAL_OK) {
            if (byte == '\n' || byte == '\r') {
                buffer[index] = '\0';
                return HAL_OK;
            } else {
                buffer[index++] = byte;
            }
        }
    }

    return HAL_TIMEOUT;
}

HAL_StatusTypeDef ODrive_SaveConfiguration(void) {
    return send_command("ss\n");
}

HAL_StatusTypeDef ODrive_Reboot(void) {
    return send_command("sr\n");
}

uint8_t ODrive_SelfTest(void) {
    char response[32];

    // 检查电源电压
    if (send_command_with_response("r vbus_voltage\n", response, sizeof(response)) != HAL_OK) {
        return 0;
    }
    float voltage = parse_float_response(response);
    if (voltage < 12.0f || voltage > 48.0f) {
        return 0;
    }

    // 检查轴0状态
    if (send_command_with_response("r axis0.current_state\n", response, sizeof(response)) != HAL_OK) {
        return 0;
    }

    // 检查轴1状态
    if (send_command_with_response("r axis1.current_state\n", response, sizeof(response)) != HAL_OK) {
        return 0;
    }

    // 检查错误状态
    if (send_command_with_response("r axis0.error\n", response, sizeof(response)) == HAL_OK) {
        if (parse_float_response(response) != 0) {
            return 0; // 有错误
        }
    }

    if (send_command_with_response("r axis1.error\n", response, sizeof(response)) == HAL_OK) {
        if (parse_float_response(response) != 0) {
            return 0; // 有错误
        }
    }

    return 1; // 自检通过
}

// 高级功能函数

/**
 * @brief 获取ODrive系统信息
 */
HAL_StatusTypeDef ODrive_GetSystemInfo(float *vbus_voltage, float *temperature) {
    char response[32];

    if (vbus_voltage) {
        if (send_command_with_response("r vbus_voltage\n", response, sizeof(response)) == HAL_OK) {
            *vbus_voltage = parse_float_response(response);
        } else {
            return HAL_ERROR;
        }
    }

    if (temperature) {
        if (send_command_with_response("r axis0.motor.get_inverter_temp()\n", response, sizeof(response)) == HAL_OK) {
            *temperature = parse_float_response(response);
        } else {
            return HAL_ERROR;
        }
    }

    return HAL_OK;
}

/**
 * @brief 设置速度限制
 */
HAL_StatusTypeDef ODrive_SetVelocityLimit(uint8_t axis, float velocity_limit) {
    if (axis > 1) return HAL_ERROR;

    char cmd[64];
    snprintf(cmd, sizeof(cmd), "w axis%d.controller.config.vel_limit %.3f\n", axis, velocity_limit);

    return send_command(cmd);
}

/**
 * @brief 设置电流限制
 */
HAL_StatusTypeDef ODrive_SetCurrentLimit(uint8_t axis, float current_limit) {
    if (axis > 1) return HAL_ERROR;

    char cmd[64];
    snprintf(cmd, sizeof(cmd), "w axis%d.motor.config.current_lim %.3f\n", axis, current_limit);

    return send_command(cmd);
}

/**
 * @brief 监控ODrive通信状态
 */
uint8_t ODrive_CheckCommunication(void) {
    uint32_t current_time = HAL_GetTick();

    // 如果超过500ms没有通信，认为连接异常
    if (current_time - last_comm_time > 500) {
        return 0;
    }

    return 1;
}
