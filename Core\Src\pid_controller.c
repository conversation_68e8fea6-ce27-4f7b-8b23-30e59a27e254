/**
 ******************************************************************************
 * @file           : pid_controller.c
 * @brief          : PID控制器实现
 * @version        : v1.0
 * @date           : 2025-01-27
 ******************************************************************************
 * @description    :
 * PID控制器实现，采用以下优化：
 * 1. 积分限幅：防止积分饱和
 * 2. 微分项优化：使用测量值变化而非误差变化
 * 3. 输出限幅：防止执行器饱和
 * 4. 调试信息：便于参数调试
 ******************************************************************************
 */

#include "pid_controller.h"
#include <math.h>

void PID_Init(PID_t *pid, float Kp, float Ki, float Kd, float output_limit) {
    pid->Kp = Kp;
    pid->Ki = Ki;
    pid->Kd = Kd;
    pid->output_limit = output_limit;
    
    // 积分限制通常设为输出限制的50%
    pid->integral_limit = output_limit * 0.5f;
    
    PID_Reset(pid);
}

float PID_Compute(PID_t *pid, float measurement, float dt) {
    // 输入验证
    if (dt <= 0.0f || dt > 0.1f) {
        dt = 0.01f; // 默认10ms
    }
    
    // 计算误差
    float error = pid->setpoint - measurement;
    
    // 比例项
    pid->last_p_term = pid->Kp * error;
    
    // 积分项 (带限幅)
    pid->integral += error * dt;
    
    // 积分限幅
    if (pid->integral > pid->integral_limit) {
        pid->integral = pid->integral_limit;
    } else if (pid->integral < -pid->integral_limit) {
        pid->integral = -pid->integral_limit;
    }
    
    pid->last_i_term = pid->Ki * pid->integral;
    
    // 微分项 (使用测量值变化，避免设定点突变引起的微分冲击)
    pid->last_d_term = 0.0f;
    if (dt > 0.0f) {
        pid->last_d_term = pid->Kd * (pid->prev_measurement - measurement) / dt;
        pid->prev_measurement = measurement;
    }
    
    // 计算总输出
    pid->last_output = pid->last_p_term + pid->last_i_term + pid->last_d_term;
    
    // 输出限幅
    if (pid->last_output > pid->output_limit) {
        pid->last_output = pid->output_limit;
    } else if (pid->last_output < -pid->output_limit) {
        pid->last_output = -pid->output_limit;
    }
    
    // 保存误差用于下次计算
    pid->prev_error = error;
    
    return pid->last_output;
}

void PID_Reset(PID_t *pid) {
    pid->integral = 0.0f;
    pid->prev_error = 0.0f;
    pid->prev_measurement = 0.0f;
    pid->last_p_term = 0.0f;
    pid->last_i_term = 0.0f;
    pid->last_d_term = 0.0f;
    pid->last_output = 0.0f;
}

void PID_SetSetpoint(PID_t *pid, float setpoint) {
    pid->setpoint = setpoint;
}

void PID_SetParams(PID_t *pid, float Kp, float Ki, float Kd) {
    pid->Kp = Kp;
    pid->Ki = Ki;
    pid->Kd = Kd;
}

void PID_GetDebugInfo(PID_t *pid, float *p_term, float *i_term, float *d_term) {
    if (p_term) *p_term = pid->last_p_term;
    if (i_term) *i_term = pid->last_i_term;
    if (d_term) *d_term = pid->last_d_term;
}