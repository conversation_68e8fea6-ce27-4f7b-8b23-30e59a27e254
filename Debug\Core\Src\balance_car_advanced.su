../Core/Src/balance_car_advanced.c:26:19:AdvancedFeatures_Init	16	static
../Core/Src/balance_car_advanced.c:70:19:AdvancedFeatures_Update	24	static
../Core/Src/balance_car_advanced.c:111:19:PathTracker_SetPath	24	static
../Core/Src/balance_car_advanced.c:128:19:PathTracker_Start	16	static
../Core/Src/balance_car_advanced.c:146:19:PathTracker_Stop	16	static
../Core/Src/balance_car_advanced.c:162:19:PathTracker_GetStatus	32	static
../Core/Src/balance_car_advanced.c:203:19:AdaptiveControl_Enable	16	static
../Core/Src/balance_car_advanced.c:222:19:AdaptiveControl_Disable	16	static
../Core/Src/balance_car_advanced.c:240:19:AdaptiveControl_GetParameters	24	static
../Core/Src/balance_car_advanced.c:257:19:RemoteControl_Enable	16	static
../Core/Src/balance_car_advanced.c:277:19:RemoteControl_Disable	16	static
../Core/Src/balance_car_advanced.c:290:19:RemoteControl_SendCommand	24	static
../Core/Src/balance_car_advanced.c:311:19:RemoteControl_GetStatus	32	static
../Core/Src/balance_car_advanced.c:327:26:update_path_tracking	88	static
../Core/Src/balance_car_advanced.c:404:26:update_adaptive_control	56	static
../Core/Src/balance_car_advanced.c:495:26:update_remote_control	48	static
../Core/Src/balance_car_advanced.c:523:13:calculate_position_estimate	48	static
../Core/Src/balance_car_advanced.c:552:14:calculate_distance	32	static
../Core/Src/balance_car_advanced.c:558:14:normalize_angle	16	static
../Core/Src/balance_car_advanced.c:564:13:update_performance_metrics	32	static
../Core/Src/balance_car_advanced.c:579:26:execute_remote_command	48	static
