../Core/Src/joystick_driver.c:54:19:Joystick_Init	24	static
../Core/Src/joystick_driver.c:87:19:Joystick_ReadData	32	static
../Core/Src/joystick_driver.c:140:22:Joystick_GetDirection	24	static
../Core/Src/joystick_driver.c:182:19:Joystick_StartCalibration	4	static
../Core/Src/joystick_driver.c:198:19:Joystick_FinishCalibration	4	static
../Core/Src/joystick_driver.c:212:6:Joystick_SetDeadzone	16	static
../Core/Src/joystick_driver.c:218:6:Joystick_SetFilterStrength	16	static
../Core/Src/joystick_driver.c:224:6:Joystick_SetAxisInvert	16	static
../Core/Src/joystick_driver.c:229:9:Joystick_SelfTest	32	static
../Core/Src/joystick_driver.c:253:9:Joystick_GetForwardCommand	16	static
../Core/Src/joystick_driver.c:262:9:Joystick_GetTurnCommand	16	static
../Core/Src/joystick_driver.c:271:10:Joystick_GetMovementIntensity	24	static
../Core/Src/joystick_driver.c:287:9:Joystick_IsInDeadzone	16	static
../Core/Src/joystick_driver.c:298:17:read_adc_channel	40	static
../Core/Src/joystick_driver.c:336:16:apply_deadzone	16	static
../Core/Src/joystick_driver.c:349:16:apply_filter	24	static
../Core/Src/joystick_driver.c:362:13:update_button_state	16	static
../Core/Src/joystick_driver.c:375:16:map_adc_to_output	24	static
../Core/Src/joystick_driver.c:401:19:Joystick_LoadCalibration	16	static
../Core/Src/joystick_driver.c:410:19:Joystick_SaveCalibration	16	static
../Core/Src/joystick_driver.c:419:6:Joystick_GetStatusInfo	56	static
../Core/Src/joystick_driver.c:439:6:Joystick_Reset	8	static
