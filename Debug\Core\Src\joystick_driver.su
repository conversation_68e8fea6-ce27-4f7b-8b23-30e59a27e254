../Core/Src/joystick_driver.c:52:19:Joystick_Init	24	static
../Core/Src/joystick_driver.c:85:19:Joystick_ReadData	32	static
../Core/Src/joystick_driver.c:138:22:Joystick_GetDirection	24	static
../Core/Src/joystick_driver.c:180:19:Joystick_StartCalibration	4	static
../Core/Src/joystick_driver.c:196:19:Joystick_FinishCalibration	4	static
../Core/Src/joystick_driver.c:210:6:Joystick_SetDeadzone	16	static
../Core/Src/joystick_driver.c:216:6:Joystick_SetFilterStrength	16	static
../Core/Src/joystick_driver.c:222:6:Joystick_SetAxisInvert	16	static
../Core/Src/joystick_driver.c:227:9:Joystick_SelfTest	32	static
../Core/Src/joystick_driver.c:251:9:Joystick_GetForwardCommand	16	static
../Core/Src/joystick_driver.c:260:9:Joystick_GetTurnCommand	16	static
../Core/Src/joystick_driver.c:269:10:Joystick_GetMovementIntensity	24	static
../Core/Src/joystick_driver.c:285:9:Joystick_IsInDeadzone	16	static
../Core/Src/joystick_driver.c:296:17:read_adc_channel	40	static
../Core/Src/joystick_driver.c:334:16:apply_deadzone	16	static
../Core/Src/joystick_driver.c:347:16:apply_filter	24	static
../Core/Src/joystick_driver.c:360:13:update_button_state	16	static
../Core/Src/joystick_driver.c:373:16:map_adc_to_output	24	static
../Core/Src/joystick_driver.c:399:19:Joystick_LoadCalibration	16	static
../Core/Src/joystick_driver.c:408:19:Joystick_SaveCalibration	16	static
../Core/Src/joystick_driver.c:417:6:Joystick_GetStatusInfo	56	static
../Core/Src/joystick_driver.c:437:6:Joystick_Reset	8	static
