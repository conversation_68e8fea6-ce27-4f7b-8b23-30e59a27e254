../Core/Src/main.c:106:5:__io_putchar	1
../Core/Src/main.c:116:13:system_health_monitor	2
../Core/Src/main.c:130:6:draw_degree_symbol	1
../Core/Src/main.c:151:6:update_performance_stats	4
../Core/Src/main.c:177:6:display_data	1
../Core/Src/main.c:214:6:display_error	1
../Core/Src/main.c:224:6:motor_set_speed_wrapper	1
../Core/Src/main.c:228:6:motor_enable_wrapper	1
../Core/Src/main.c:232:6:motor_get_speed_wrapper	1
../Core/Src/main.c:237:6:init_balance_system	2
../Core/Src/main.c:283:6:balance_control_loop	5
../Core/Src/main.c:298:6:display_balance_info	7
../Core/Src/main.c:364:6:handle_user_input	4
../Core/Src/main.c:390:5:main	11
../Core/Src/main.c:587:6:SystemClock_Config	3
../Core/Src/main.c:634:13:MX_I2C1_Init	2
../Core/Src/main.c:668:13:MX_I2C2_Init	2
../Core/Src/main.c:702:13:MX_TIM2_Init	7
../Core/Src/main.c:765:13:MX_USART1_UART_Init	2
../Core/Src/main.c:798:13:MX_GPIO_Init	1
../Core/Src/main.c:837:6:Error_Handler	1
