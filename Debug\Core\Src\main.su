../Core/Src/main.c:106:5:__io_putchar	16	static
../Core/Src/main.c:116:13:system_health_monitor	16	static
../Core/Src/main.c:130:6:draw_degree_symbol	16	static
../Core/Src/main.c:151:6:update_performance_stats	32	static
../Core/Src/main.c:177:6:display_data	56	static
../Core/Src/main.c:214:6:display_error	16	static
../Core/Src/main.c:224:6:motor_set_speed_wrapper	16	static
../Core/Src/main.c:228:6:motor_enable_wrapper	16	static
../Core/Src/main.c:232:6:motor_get_speed_wrapper	16	static
../Core/Src/main.c:237:6:init_balance_system	24	static
../Core/Src/main.c:283:6:balance_control_loop	24	static
../Core/Src/main.c:298:6:display_balance_info	64	static
../Core/Src/main.c:364:6:handle_user_input	16	static
../Core/Src/main.c:390:5:main	192	static
../Core/Src/main.c:587:6:<PERSON><PERSON><PERSON>_Config	88	static
../Core/Src/main.c:634:13:MX_I2C1_Init	8	static
../Core/Src/main.c:668:13:MX_I2C2_Init	8	static
../Core/Src/main.c:702:13:MX_TIM2_Init	64	static
../Core/Src/main.c:765:13:MX_USART1_UART_Init	8	static
../Core/Src/main.c:798:13:MX_GPIO_Init	40	static
../Core/Src/main.c:837:6:Error_Handler	4	static,ignoring_inline_asm
