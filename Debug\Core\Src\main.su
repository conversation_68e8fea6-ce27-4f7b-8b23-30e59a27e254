../Core/Src/main.c:111:5:__io_putchar	16	static
../Core/Src/main.c:121:13:system_health_monitor	16	static
../Core/Src/main.c:135:6:draw_degree_symbol	16	static
../Core/Src/main.c:156:6:update_performance_stats	32	static
../Core/Src/main.c:182:6:display_data	56	static
../Core/Src/main.c:219:6:display_error	16	static
../Core/Src/main.c:231:6:motor_set_speed_wrapper	16	static
../Core/Src/main.c:239:6:motor_enable_wrapper	16	static
../Core/Src/main.c:247:6:motor_get_speed_wrapper	16	static
../Core/Src/main.c:256:6:init_odrive_system	32	static
../Core/Src/main.c:284:6:init_balance_system	24	static
../Core/Src/main.c:305:6:balance_control_loop	24	static
../Core/Src/main.c:320:6:display_balance_info	64	static
../Core/Src/main.c:386:6:handle_user_input	16	static
../Core/Src/main.c:412:5:main	8	static
../Core/Src/main.c:478:6:SystemClock_Config	88	static
../Core/Src/main.c:524:6:PeriphCommonClock_Config	32	static
../Core/Src/main.c:543:13:MX_ADC1_Init	24	static
../Core/Src/main.c:595:13:MX_I2C1_Init	8	static
../Core/Src/main.c:629:13:MX_TIM2_Init	8	static
../Core/Src/main.c:660:13:MX_TIM10_Init	40	static
../Core/Src/main.c:706:13:MX_USART1_UART_Init	8	static
../Core/Src/main.c:739:13:MX_USART6_UART_Init	8	static
../Core/Src/main.c:772:13:MX_GPIO_Init	40	static
../Core/Src/main.c:824:6:Error_Handler	4	static,ignoring_inline_asm
