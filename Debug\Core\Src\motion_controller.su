../Core/Src/motion_controller.c:30:19:Motion_Init	24	static
../Core/Src/motion_controller.c:62:19:Motion_Update	40	static
../Core/Src/motion_controller.c:156:19:Motion_GetCommand	16	static
../Core/Src/motion_controller.c:165:19:Motion_SetMode	16	static
../Core/Src/motion_controller.c:182:19:Motion_EmergencyStop	16	static
../Core/Src/motion_controller.c:204:19:Motion_Reset	16	static
../Core/Src/motion_controller.c:229:19:Motion_SetSafetyParams	24	static
../Core/Src/motion_controller.c:244:19:Motion_GetStatus	24	static
../Core/Src/motion_controller.c:253:9:Motion_SelfTest	16	static
../Core/Src/motion_controller.c:276:7:Motion_CalculateTargetTilt	24	static
../Core/Src/motion_controller.c:295:7:Motion_CalculateTurnRate	24	static
../Core/Src/motion_controller.c:314:6:Motion_ApplyLimits	32	static
../Core/Src/motion_controller.c:346:9:Motion_CheckSafety	16	static
../Core/Src/motion_controller.c:364:19:Motion_SetCruiseSpeed	16	static
../Core/Src/motion_controller.c:382:19:Motion_ExecuteAction	16	static
../Core/Src/motion_controller.c:419:6:Motion_GetStatistics	160	static
../Core/Src/motion_controller.c:450:14:apply_low_pass_filter	24	static
../Core/Src/motion_controller.c:455:14:apply_deadzone_and_scale	24	static
../Core/Src/motion_controller.c:471:13:update_motion_state	24	static
../Core/Src/motion_controller.c:495:16:check_timeout	24	static
../Core/Src/motion_controller.c:507:19:Motion_SetMaxTiltAngle	16	static
../Core/Src/motion_controller.c:520:19:Motion_SetMaxTurnRate	16	static
../Core/Src/motion_controller.c:533:19:Motion_SetAccelerationLimit	16	static
../Core/Src/motion_controller.c:546:19:Motion_SetResponseSpeed	16	static
