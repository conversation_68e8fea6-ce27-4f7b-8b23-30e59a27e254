################################################################################
# Automatically-generated file. Do not edit!
# Toolchain: GNU Tools for STM32 (13.3.rel1)
################################################################################

# Add inputs and outputs from these tool invocations to the build variables 
C_SRCS += \
../Core/Src/auto_follow.c \
../Core/Src/balance_car_advanced.c \
../Core/Src/balance_car_demo.c \
../Core/Src/balance_car_main.c \
../Core/Src/balance_car_system.c \
../Core/Src/balance_car_tuning.c \
../Core/Src/balance_controller.c \
../Core/Src/error_handler.c \
../Core/Src/joystick_driver.c \
../Core/Src/main.c \
../Core/Src/mobile_app.c \
../Core/Src/motion_controller.c \
../Core/Src/motor_driver.c \
../Core/Src/mpu6050.c \
../Core/Src/odrive_driver.c \
../Core/Src/pid_controller.c \
../Core/Src/stm32f4xx_hal_msp.c \
../Core/Src/stm32f4xx_it.c \
../Core/Src/syscalls.c \
../Core/Src/sysmem.c \
../Core/Src/system_stm32f4xx.c 

OBJS += \
./Core/Src/auto_follow.o \
./Core/Src/balance_car_advanced.o \
./Core/Src/balance_car_demo.o \
./Core/Src/balance_car_main.o \
./Core/Src/balance_car_system.o \
./Core/Src/balance_car_tuning.o \
./Core/Src/balance_controller.o \
./Core/Src/error_handler.o \
./Core/Src/joystick_driver.o \
./Core/Src/main.o \
./Core/Src/mobile_app.o \
./Core/Src/motion_controller.o \
./Core/Src/motor_driver.o \
./Core/Src/mpu6050.o \
./Core/Src/odrive_driver.o \
./Core/Src/pid_controller.o \
./Core/Src/stm32f4xx_hal_msp.o \
./Core/Src/stm32f4xx_it.o \
./Core/Src/syscalls.o \
./Core/Src/sysmem.o \
./Core/Src/system_stm32f4xx.o 

C_DEPS += \
./Core/Src/auto_follow.d \
./Core/Src/balance_car_advanced.d \
./Core/Src/balance_car_demo.d \
./Core/Src/balance_car_main.d \
./Core/Src/balance_car_system.d \
./Core/Src/balance_car_tuning.d \
./Core/Src/balance_controller.d \
./Core/Src/error_handler.d \
./Core/Src/joystick_driver.d \
./Core/Src/main.d \
./Core/Src/mobile_app.d \
./Core/Src/motion_controller.d \
./Core/Src/motor_driver.d \
./Core/Src/mpu6050.d \
./Core/Src/odrive_driver.d \
./Core/Src/pid_controller.d \
./Core/Src/stm32f4xx_hal_msp.d \
./Core/Src/stm32f4xx_it.d \
./Core/Src/syscalls.d \
./Core/Src/sysmem.d \
./Core/Src/system_stm32f4xx.d 


# Each subdirectory must supply rules for building sources it contributes
Core/Src/%.o Core/Src/%.su Core/Src/%.cyclo: ../Core/Src/%.c Core/Src/subdir.mk
	arm-none-eabi-gcc "$<" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"$(@:%.o=%.d)" -MT"$@" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "$@"

clean: clean-Core-2f-Src

clean-Core-2f-Src:
	-$(RM) ./Core/Src/auto_follow.cyclo ./Core/Src/auto_follow.d ./Core/Src/auto_follow.o ./Core/Src/auto_follow.su ./Core/Src/balance_car_advanced.cyclo ./Core/Src/balance_car_advanced.d ./Core/Src/balance_car_advanced.o ./Core/Src/balance_car_advanced.su ./Core/Src/balance_car_demo.cyclo ./Core/Src/balance_car_demo.d ./Core/Src/balance_car_demo.o ./Core/Src/balance_car_demo.su ./Core/Src/balance_car_main.cyclo ./Core/Src/balance_car_main.d ./Core/Src/balance_car_main.o ./Core/Src/balance_car_main.su ./Core/Src/balance_car_system.cyclo ./Core/Src/balance_car_system.d ./Core/Src/balance_car_system.o ./Core/Src/balance_car_system.su ./Core/Src/balance_car_tuning.cyclo ./Core/Src/balance_car_tuning.d ./Core/Src/balance_car_tuning.o ./Core/Src/balance_car_tuning.su ./Core/Src/balance_controller.cyclo ./Core/Src/balance_controller.d ./Core/Src/balance_controller.o ./Core/Src/balance_controller.su ./Core/Src/error_handler.cyclo ./Core/Src/error_handler.d ./Core/Src/error_handler.o ./Core/Src/error_handler.su ./Core/Src/joystick_driver.cyclo ./Core/Src/joystick_driver.d ./Core/Src/joystick_driver.o ./Core/Src/joystick_driver.su ./Core/Src/main.cyclo ./Core/Src/main.d ./Core/Src/main.o ./Core/Src/main.su ./Core/Src/mobile_app.cyclo ./Core/Src/mobile_app.d ./Core/Src/mobile_app.o ./Core/Src/mobile_app.su ./Core/Src/motion_controller.cyclo ./Core/Src/motion_controller.d ./Core/Src/motion_controller.o ./Core/Src/motion_controller.su ./Core/Src/motor_driver.cyclo ./Core/Src/motor_driver.d ./Core/Src/motor_driver.o ./Core/Src/motor_driver.su ./Core/Src/mpu6050.cyclo ./Core/Src/mpu6050.d ./Core/Src/mpu6050.o ./Core/Src/mpu6050.su ./Core/Src/odrive_driver.cyclo ./Core/Src/odrive_driver.d ./Core/Src/odrive_driver.o ./Core/Src/odrive_driver.su ./Core/Src/pid_controller.cyclo ./Core/Src/pid_controller.d ./Core/Src/pid_controller.o ./Core/Src/pid_controller.su ./Core/Src/stm32f4xx_hal_msp.cyclo ./Core/Src/stm32f4xx_hal_msp.d ./Core/Src/stm32f4xx_hal_msp.o ./Core/Src/stm32f4xx_hal_msp.su ./Core/Src/stm32f4xx_it.cyclo ./Core/Src/stm32f4xx_it.d ./Core/Src/stm32f4xx_it.o ./Core/Src/stm32f4xx_it.su ./Core/Src/syscalls.cyclo ./Core/Src/syscalls.d ./Core/Src/syscalls.o ./Core/Src/syscalls.su ./Core/Src/sysmem.cyclo ./Core/Src/sysmem.d ./Core/Src/sysmem.o ./Core/Src/sysmem.su ./Core/Src/system_stm32f4xx.cyclo ./Core/Src/system_stm32f4xx.d ./Core/Src/system_stm32f4xx.o ./Core/Src/system_stm32f4xx.su

.PHONY: clean-Core-2f-Src

