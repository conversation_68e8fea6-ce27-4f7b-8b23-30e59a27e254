../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c:357:19:HAL_UART_Init	16	static
../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c:435:19:HAL_HalfDuplex_Init	16	static
../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c:509:19:HAL_LIN_Init	16	static
../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c:591:19:HAL_MultiProcessor_Init	24	static
../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c:669:19:HAL_UART_DeInit	16	static
../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c:715:13:HAL_UART_MspInit	16	static
../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c:730:13:HAL_UART_MspDeInit	16	static
../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c:1135:19:HAL_UART_Transmit	48	static
../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c:1221:19:HAL_UART_Receive	48	static
../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c:1308:19:HAL_UART_Transmit_IT	24	static
../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c:1347:19:HAL_UART_Receive_IT	24	static
../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c:1379:19:HAL_UART_Transmit_DMA	56	static,ignoring_inline_asm
../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c:1449:19:HAL_UART_Receive_DMA	24	static
../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c:1476:19:HAL_UART_DMAPause	120	static,ignoring_inline_asm
../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c:1507:19:HAL_UART_DMAResume	120	static,ignoring_inline_asm
../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c:1541:19:HAL_UART_DMAStop	72	static,ignoring_inline_asm
../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c:1596:19:HAL_UARTEx_ReceiveToIdle	40	static
../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c:1721:19:HAL_UARTEx_ReceiveToIdle_IT	56	static,ignoring_inline_asm
../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c:1781:19:HAL_UARTEx_ReceiveToIdle_DMA	56	static,ignoring_inline_asm
../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c:1846:29:HAL_UARTEx_GetRxEventType	16	static
../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c:1864:19:HAL_UART_Abort	136	static,ignoring_inline_asm
../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c:1953:19:HAL_UART_AbortTransmit	64	static,ignoring_inline_asm
../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c:2004:19:HAL_UART_AbortReceive	112	static,ignoring_inline_asm
../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c:2065:19:HAL_UART_Abort_IT	144	static,ignoring_inline_asm
../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c:2200:19:HAL_UART_AbortTransmit_IT	64	static,ignoring_inline_asm
../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c:2277:19:HAL_UART_AbortReceive_IT	112	static,ignoring_inline_asm
../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c:2355:6:HAL_UART_IRQHandler	240	static,ignoring_inline_asm
../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c:2619:13:HAL_UART_TxCpltCallback	16	static
../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c:2634:13:HAL_UART_TxHalfCpltCallback	16	static
../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c:2649:13:HAL_UART_RxCpltCallback	16	static
../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c:2664:13:HAL_UART_RxHalfCpltCallback	16	static
../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c:2679:13:HAL_UART_ErrorCallback	16	static
../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c:2693:13:HAL_UART_AbortCpltCallback	16	static
../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c:2708:13:HAL_UART_AbortTransmitCpltCallback	16	static
../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c:2723:13:HAL_UART_AbortReceiveCpltCallback	16	static
../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c:2740:13:HAL_UARTEx_RxEventCallback	16	static
../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c:2780:19:HAL_LIN_SendBreak	40	static,ignoring_inline_asm
../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c:2807:19:HAL_MultiProcessor_EnterMuteMode	40	static,ignoring_inline_asm
../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c:2835:19:HAL_MultiProcessor_ExitMuteMode	40	static,ignoring_inline_asm
../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c:2863:19:HAL_HalfDuplex_EnableTransmitter	24	static
../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c:2898:19:HAL_HalfDuplex_EnableReceiver	24	static
../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c:2955:23:HAL_UART_GetState	24	static
../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c:2970:10:HAL_UART_GetError	16	static
../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c:3015:13:UART_DMATransmitCplt	72	static,ignoring_inline_asm
../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c:3050:13:UART_DMATxHalfCplt	24	static
../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c:3069:13:UART_DMAReceiveCplt	120	static,ignoring_inline_asm
../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c:3131:13:UART_DMARxHalfCplt	24	static
../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c:3170:13:UART_DMAError	24	static
../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c:3212:26:UART_WaitOnFlagUntilTimeout	32	static
../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c:3263:19:UART_Start_Receive_IT	24	static
../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c:3298:19:UART_Start_Receive_DMA	104	static,ignoring_inline_asm
../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c:3356:13:UART_EndTxTransfer	40	static,ignoring_inline_asm
../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c:3370:13:UART_EndRxTransfer	88	static,ignoring_inline_asm
../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c:3394:13:UART_DMAAbortOnError	24	static
../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c:3417:13:UART_DMATxAbortCallback	24	static
../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c:3463:13:UART_DMARxAbortCallback	24	static
../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c:3509:13:UART_DMATxOnlyAbortCallback	24	static
../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c:3537:13:UART_DMARxOnlyAbortCallback	24	static
../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c:3563:26:UART_Transmit_IT	24	static
../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c:3603:26:UART_EndTransmit_IT	16	static
../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c:3628:26:UART_Receive_IT	56	static,ignoring_inline_asm
../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c:3731:13:UART_SetConfig	288	static
