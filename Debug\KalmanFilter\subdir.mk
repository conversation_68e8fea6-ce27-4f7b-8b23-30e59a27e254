################################################################################
# Automatically-generated file. Do not edit!
# Toolchain: GNU Tools for STM32 (13.3.rel1)
################################################################################

# Add inputs and outputs from these tool invocations to the build variables 
C_SRCS += \
../KalmanFilter/Kalman.c 

OBJS += \
./KalmanFilter/Kalman.o 

C_DEPS += \
./KalmanFilter/Kalman.d 


# Each subdirectory must supply rules for building sources it contributes
KalmanFilter/%.o KalmanFilter/%.su <PERSON><PERSON>Filter/%.cyclo: ../KalmanFilter/%.c <PERSON><PERSON>Filter/subdir.mk
	arm-none-eabi-gcc "$<" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"$(@:%.o=%.d)" -MT"$@" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "$@"

clean: clean-KalmanFilter

clean-KalmanFilter:
	-$(RM) ./KalmanFilter/Kalman.cyclo ./KalmanFilter/Kalman.d ./KalmanFilter/Kalman.o ./KalmanFilter/Kalman.su

.PHONY: clean-KalmanFilter

