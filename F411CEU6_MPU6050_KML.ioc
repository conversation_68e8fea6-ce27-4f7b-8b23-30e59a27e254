#MicroXplorer Configuration settings - do not modify
ADC1.Channel-1\#ChannelRegularConversion=ADC_CHANNEL_0
ADC1.ContinuousConvMode=DISABLE
ADC1.DiscontinuousConvMode=ENABLE
ADC1.IPParameters=Rank-1\#ChannelRegularConversion,master,Channel-1\#ChannelRegularConversion,SamplingTime-1\#ChannelRegularConversion,NbrOfConversionFlag,ScanConvMode,ContinuousConvMode,DiscontinuousConvMode
ADC1.NbrOfConversionFlag=1
ADC1.Rank-1\#ChannelRegularConversion=1
ADC1.SamplingTime-1\#ChannelRegularConversion=ADC_SAMPLETIME_3CYCLES
ADC1.ScanConvMode=ENABLE
ADC1.master=1
CAD.formats=
CAD.pinconfig=
CAD.provider=
File.Version=6
GPIO.groupedBy=Group By Peripherals
I2C1.I2C_Mode=I2C_Fast
I2C1.IPParameters=I2C_Mode
KeepUserPlacement=false
Mcu.CPN=STM32F411CEU6
Mcu.Family=STM32F4
Mcu.IP0=ADC1
Mcu.IP1=I2C1
Mcu.IP2=I2C2
Mcu.IP3=NVIC
Mcu.IP4=RCC
Mcu.IP5=SYS
Mcu.IP6=TIM3
Mcu.IP7=USART1
Mcu.IP8=USART2
Mcu.IPNb=9
Mcu.Name=STM32F411C(C-E)Ux
Mcu.Package=UFQFPN48
Mcu.Pin0=PH0 - OSC_IN
Mcu.Pin1=PH1 - OSC_OUT
Mcu.Pin10=PA9
Mcu.Pin11=PA10
Mcu.Pin12=PA13
Mcu.Pin13=PA14
Mcu.Pin14=PB3
Mcu.Pin15=PB6
Mcu.Pin16=PB7
Mcu.Pin17=VP_SYS_VS_Systick
Mcu.Pin18=VP_TIM3_VS_ClockSourceINT
Mcu.Pin2=PA0-WKUP
Mcu.Pin3=PA1
Mcu.Pin4=PA2
Mcu.Pin5=PA3
Mcu.Pin6=PA4
Mcu.Pin7=PA6
Mcu.Pin8=PA7
Mcu.Pin9=PB10
Mcu.PinsNb=19
Mcu.ThirdPartyNb=0
Mcu.UserConstants=
Mcu.UserName=STM32F411CEUx
MxCube.Version=6.14.1
MxDb.Version=DB.6.0.141
NVIC.ADC_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.BusFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.DebugMonitor_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.ForceEnableDMAVector=true
NVIC.HardFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.MemoryManagement_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.NonMaskableInt_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PendSV_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PriorityGroup=NVIC_PRIORITYGROUP_4
NVIC.SVCall_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.SysTick_IRQn=true\:15\:0\:false\:false\:true\:false\:true\:false
NVIC.USART2_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.UsageFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
PA0-WKUP.Locked=true
PA0-WKUP.Signal=ADCx_IN0
PA1.Locked=true
PA1.Signal=ADCx_IN1
PA10.Mode=Asynchronous
PA10.Signal=USART1_RX
PA13.Mode=Serial_Wire
PA13.Signal=SYS_JTMS-SWDIO
PA14.Mode=Serial_Wire
PA14.Signal=SYS_JTCK-SWCLK
PA2.Mode=Asynchronous
PA2.Signal=USART2_TX
PA3.Mode=Asynchronous
PA3.Signal=USART2_RX
PA4.Locked=true
PA4.Signal=ADCx_IN4
PA6.Signal=S_TIM3_CH1
PA7.Signal=S_TIM3_CH2
PA9.Mode=Asynchronous
PA9.Signal=USART1_TX
PB10.Mode=I2C
PB10.Signal=I2C2_SCL
PB3.Mode=I2C
PB3.Signal=I2C2_SDA
PB6.Mode=I2C
PB6.Signal=I2C1_SCL
PB7.Mode=I2C
PB7.Signal=I2C1_SDA
PH0\ -\ OSC_IN.Mode=HSE-External-Oscillator
PH0\ -\ OSC_IN.Signal=RCC_OSC_IN
PH1\ -\ OSC_OUT.Mode=HSE-External-Oscillator
PH1\ -\ OSC_OUT.Signal=RCC_OSC_OUT
PinOutPanel.RotationAngle=0
ProjectManager.AskForMigrate=true
ProjectManager.BackupPrevious=false
ProjectManager.CompilerLinker=GCC
ProjectManager.CompilerOptimize=6
ProjectManager.ComputerToolchain=false
ProjectManager.CoupleFile=false
ProjectManager.CustomerFirmwarePackage=
ProjectManager.DefaultFWLocation=true
ProjectManager.DeletePrevious=true
ProjectManager.DeviceId=STM32F411CEUx
ProjectManager.FirmwarePackage=STM32Cube FW_F4 V1.28.2
ProjectManager.FreePins=false
ProjectManager.HalAssertFull=false
ProjectManager.HeapSize=0x200
ProjectManager.KeepUserCode=true
ProjectManager.LastFirmware=true
ProjectManager.LibraryCopy=1
ProjectManager.MainLocation=Core/Src
ProjectManager.NoMain=false
ProjectManager.PreviousToolchain=
ProjectManager.ProjectBuild=false
ProjectManager.ProjectFileName=F411CEU6_MPU6050_KML.ioc
ProjectManager.ProjectName=F411CEU6_MPU6050_KML
ProjectManager.ProjectStructure=
ProjectManager.RegisterCallBack=
ProjectManager.StackSize=0x400
ProjectManager.TargetToolchain=STM32CubeIDE
ProjectManager.ToolChainLocation=
ProjectManager.UAScriptAfterPath=
ProjectManager.UAScriptBeforePath=
ProjectManager.UnderRoot=true
ProjectManager.functionlistsort=1-SystemClock_Config-RCC-false-HAL-false,2-MX_GPIO_Init-GPIO-false-HAL-true,3-MX_USART1_UART_Init-USART1-false-HAL-true,4-MX_I2C1_Init-I2C1-false-HAL-true,5-MX_I2C2_Init-I2C2-false-HAL-true,6-MX_ADC1_Init-ADC1-false-HAL-true,7-MX_TIM3_Init-TIM3-false-HAL-true
RCC.48MHZClocksFreq_Value=50000000
RCC.AHBFreq_Value=100000000
RCC.APB1CLKDivider=RCC_HCLK_DIV2
RCC.APB1Freq_Value=50000000
RCC.APB1TimFreq_Value=100000000
RCC.APB2Freq_Value=100000000
RCC.APB2TimFreq_Value=100000000
RCC.CortexFreq_Value=100000000
RCC.EthernetFreq_Value=100000000
RCC.FCLKCortexFreq_Value=100000000
RCC.FamilyName=M
RCC.HCLKFreq_Value=100000000
RCC.HSE_VALUE=8000000
RCC.HSI_VALUE=16000000
RCC.I2SClocksFreq_Value=96000000
RCC.IPParameters=48MHZClocksFreq_Value,AHBFreq_Value,APB1CLKDivider,APB1Freq_Value,APB1TimFreq_Value,APB2Freq_Value,APB2TimFreq_Value,CortexFreq_Value,EthernetFreq_Value,FCLKCortexFreq_Value,FamilyName,HCLKFreq_Value,HSE_VALUE,HSI_VALUE,I2SClocksFreq_Value,LSI_VALUE,PLLCLKFreq_Value,PLLM,PLLN,PLLQCLKFreq_Value,RCC_TIM_PRescaler_Selection,RTCFreq_Value,RTCHSEDivFreq_Value,SYSCLKFreq_VALUE,SYSCLKSource,VCOI2SOutputFreq_Value,VCOInputFreq_Value,VCOInputMFreq_Value,VCOOutputFreq_Value,VcooutputI2S
RCC.LSI_VALUE=32000
RCC.PLLCLKFreq_Value=100000000
RCC.PLLM=8
RCC.PLLN=100
RCC.PLLQCLKFreq_Value=50000000
RCC.RCC_TIM_PRescaler_Selection=RCC_TIMPRES_ACTIVATED
RCC.RTCFreq_Value=32000
RCC.RTCHSEDivFreq_Value=4000000
RCC.SYSCLKFreq_VALUE=100000000
RCC.SYSCLKSource=RCC_SYSCLKSOURCE_PLLCLK
RCC.VCOI2SOutputFreq_Value=192000000
RCC.VCOInputFreq_Value=2000000
RCC.VCOInputMFreq_Value=1000000
RCC.VCOOutputFreq_Value=200000000
RCC.VcooutputI2S=96000000
SH.ADCx_IN0.0=ADC1_IN0,IN0
SH.ADCx_IN0.ConfNb=1
SH.ADCx_IN1.0=ADC1_IN1,IN1
SH.ADCx_IN1.ConfNb=1
SH.ADCx_IN4.0=ADC1_IN4,IN4
SH.ADCx_IN4.ConfNb=1
SH.S_TIM3_CH1.0=TIM3_CH1,Input_Capture1_from_TI1
SH.S_TIM3_CH1.ConfNb=1
SH.S_TIM3_CH2.0=TIM3_CH2,Input_Capture2_from_TI2
SH.S_TIM3_CH2.ConfNb=1
TIM3.Channel-Input_Capture1_from_TI1=TIM_CHANNEL_1
TIM3.Channel-Input_Capture2_from_TI2=TIM_CHANNEL_2
TIM3.IPParameters=Channel-Input_Capture1_from_TI1,Channel-Input_Capture2_from_TI2
USART1.IPParameters=VirtualMode
USART1.VirtualMode=VM_ASYNC
USART2.IPParameters=VirtualMode
USART2.VirtualMode=VM_ASYNC
VP_SYS_VS_Systick.Mode=SysTick
VP_SYS_VS_Systick.Signal=SYS_VS_Systick
VP_TIM3_VS_ClockSourceINT.Mode=Internal
VP_TIM3_VS_ClockSourceINT.Signal=TIM3_VS_ClockSourceINT
board=custom
isbadioc=false
