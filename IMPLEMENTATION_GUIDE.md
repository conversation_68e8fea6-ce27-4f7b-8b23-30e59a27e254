# 🛠️ MPU6050项目改进实施指南

## 📋 实施前准备

### **环境准备**
```bash
# 1. 备份当前项目
cp -r F411CEU6_MPU6050_KML F411CEU6_MPU6050_KML_backup

# 2. 创建工作分支
git checkout -b feature/simplify-architecture

# 3. 准备工具
# - STM32CubeIDE
# - Git版本控制
# - 代码分析工具 (可选)
```

### **基线建立**
```bash
# 统计当前代码行数
find Core/Src -name "*.c" | xargs wc -l
find Core/Inc -name "*.h" | xargs wc -l

# 记录当前文件数量
ls -la Core/Src/ | wc -l
ls -la Core/Inc/ | wc -l
```

---

## 🎯 阶段1：代码质量提升 (第1-3天)

### **第1天：代码规范化**

#### **上午 (4小时)：消除魔法数字**

**步骤1.1：识别魔法数字** (1小时)
```bash
# 搜索代码中的数字常量
grep -r "[0-9]\+" Core/Src/*.c | grep -v "0x" | head -20

# 重点关注的文件
# - main.c
# - mpu6050.c
# - error_handler.c
```

**步骤1.2：创建配置文件** (1.5小时)
```c
// 创建 Core/Inc/app_config.h
#ifndef __APP_CONFIG_H
#define __APP_CONFIG_H

/* ========================= 系统配置 ========================= */
#define MAIN_LOOP_PERIOD_MS     200         // 主循环周期
#define DISPLAY_UPDATE_RATE_MS  100         // 显示更新周期
#define I2C_TIMEOUT_MS          50          // I2C超时时间
#define UART_BAUD_RATE          115200      // 串口波特率

/* ========================= 调试配置 ========================= */
#define DEBUG_ENABLE            1           // 调试开关
#define DEBUG_PRINT_SENSOR      1           // 传感器数据打印
#define DEBUG_PRINT_ERRORS      1           // 错误信息打印

/* ========================= 显示配置 ========================= */
#define DISPLAY_FONT_SIZE       Font_7x10   // 字体大小
#define DISPLAY_LINE_HEIGHT     16          // 行高
#define DATA_DECIMAL_PLACES     2           // 小数位数

#endif /* __APP_CONFIG_H */
```

**步骤1.3：更新mpu6050_config.h** (1.5小时)
```c
// 简化现有的 Core/Inc/mpu6050_config.h
#ifndef __MPU6050_CONFIG_H
#define __MPU6050_CONFIG_H

/* ========================= 基础配置 ========================= */
#define MPU6050_I2C_ADDR        0xD0        // I2C地址
#define MPU6050_DEVICE_ID       0x68        // 设备ID

/* ========================= 量程配置 ========================= */
#define MPU6050_ACCEL_RANGE     2           // 加速度量程 ±2g
#define MPU6050_GYRO_RANGE      250         // 陀螺仪量程 ±250°/s
#define MPU6050_SAMPLE_RATE_HZ  100         // 采样率 100Hz

/* ========================= 转换系数 ========================= */
#define MPU6050_ACCEL_LSB_PER_G     16384.0f    // LSB/g
#define MPU6050_GYRO_LSB_PER_DPS    131.0f      // LSB/(°/s)
#define MPU6050_TEMP_SENSITIVITY    340.0f      // 温度灵敏度
#define MPU6050_TEMP_OFFSET         36.53f      // 温度偏移

/* ========================= 数据验证 ========================= */
#define MPU6050_ACCEL_MAX_G     4.0f        // 最大加速度
#define MPU6050_GYRO_MAX_DPS    500.0f      // 最大角速度
#define MPU6050_TEMP_MIN_C      -40.0f      // 最小温度
#define MPU6050_TEMP_MAX_C      85.0f       // 最大温度

#endif /* __MPU6050_CONFIG_H */
```

#### **下午 (4小时)：统一命名规范**

**步骤1.4：函数命名统一** (2小时)
```c
// 统一命名规范
// 原来：MPU6050_Init() 
// 改为：mpu6050_init()

// 原来：MPU6050_Read_All()
// 改为：mpu6050_read_all_data()

// 批量重命名工具
sed -i 's/MPU6050_Init/mpu6050_init/g' Core/Src/*.c Core/Inc/*.h
sed -i 's/MPU6050_Read_All/mpu6050_read_all_data/g' Core/Src/*.c Core/Inc/*.h
sed -i 's/MPU6050_Read_Accel/mpu6050_read_accel/g' Core/Src/*.c Core/Inc/*.h
sed -i 's/MPU6050_Read_Gyro/mpu6050_read_gyro/g' Core/Src/*.c Core/Inc/*.h
sed -i 's/MPU6050_Read_Temp/mpu6050_read_temp/g' Core/Src/*.c Core/Inc/*.h
```

**步骤1.5：变量命名统一** (1小时)
```c
// 统一变量命名
// 原来：MPU6050_t MPU6050;
// 改为：mpu6050_data_t sensor_data;

// 原来：KalmanAngleX, KalmanAngleY
// 改为：kalman_angle_x, kalman_angle_y
```

**步骤1.6：添加注释** (1小时)
```c
/**
 * @brief  初始化MPU6050传感器
 * @param  i2c_handle: I2C句柄指针
 * @retval app_error_t: 错误码 (APP_OK表示成功)
 * @note   配置传感器为±2g加速度量程，±250°/s陀螺仪量程
 */
app_error_t mpu6050_init(I2C_HandleTypeDef *i2c_handle);
```

### **第2天：错误处理完善**

#### **上午 (4小时)：简化错误处理系统**

**步骤2.1：简化错误码定义** (1小时)
```c
// 简化 Core/Inc/error_handler.h
typedef enum {
    APP_OK = 0,                     // 成功
    
    // 系统错误 (1-9)
    APP_ERROR_INIT_FAILED = 1,      // 初始化失败
    APP_ERROR_INVALID_PARAM,        // 参数无效
    APP_ERROR_TIMEOUT,              // 超时
    
    // I2C错误 (10-19)  
    APP_ERROR_I2C_FAILED = 10,      // I2C通信失败
    APP_ERROR_I2C_TIMEOUT,          // I2C超时
    APP_ERROR_I2C_BUSY,             // I2C忙
    
    // 传感器错误 (20-29)
    APP_ERROR_SENSOR_NOT_FOUND = 20,// 传感器未找到
    APP_ERROR_SENSOR_READ_FAILED,   // 传感器读取失败
    APP_ERROR_SENSOR_DATA_INVALID,  // 传感器数据无效
    
    // 显示错误 (30-39)
    APP_ERROR_DISPLAY_FAILED = 30,  // 显示失败
    
    APP_ERROR_MAX                   // 错误码最大值
} app_error_t;
```

**步骤2.2：简化错误处理实现** (2小时)
```c
// 简化 Core/Src/error_handler.c
#include "error_handler.h"
#include "app_config.h"
#include <stdio.h>

static const char* error_messages[] = {
    [APP_OK] = "Success",
    [APP_ERROR_INIT_FAILED] = "Initialization failed",
    [APP_ERROR_INVALID_PARAM] = "Invalid parameter",
    [APP_ERROR_TIMEOUT] = "Operation timeout",
    [APP_ERROR_I2C_FAILED] = "I2C communication failed",
    [APP_ERROR_I2C_TIMEOUT] = "I2C timeout",
    [APP_ERROR_I2C_BUSY] = "I2C bus busy",
    [APP_ERROR_SENSOR_NOT_FOUND] = "Sensor not found",
    [APP_ERROR_SENSOR_READ_FAILED] = "Sensor read failed",
    [APP_ERROR_SENSOR_DATA_INVALID] = "Sensor data invalid",
    [APP_ERROR_DISPLAY_FAILED] = "Display failed"
};

const char* app_error_get_message(app_error_t error) {
    if (error < APP_ERROR_MAX && error_messages[error] != NULL) {
        return error_messages[error];
    }
    return "Unknown error";
}

void app_error_print(app_error_t error, const char* function) {
    #if DEBUG_PRINT_ERRORS
    printf("[ERROR] %s: %s\r\n", function, app_error_get_message(error));
    #endif
}
```

**步骤2.3：错误处理宏定义** (1小时)
```c
// 在 error_handler.h 中添加便捷宏
#define CHECK_ERROR(condition, error_code) \
    do { \
        if (!(condition)) { \
            app_error_print(error_code, __FUNCTION__); \
            return error_code; \
        } \
    } while(0)

#define PRINT_ERROR(error_code) \
    app_error_print(error_code, __FUNCTION__)
```

#### **下午 (4小时)：集成错误处理**

**步骤2.4：更新mpu6050.c** (2小时)
```c
// 更新传感器驱动中的错误处理
app_error_t mpu6050_init(I2C_HandleTypeDef *i2c_handle) {
    uint8_t device_id;
    HAL_StatusTypeDef hal_status;
    
    // 参数检查
    CHECK_ERROR(i2c_handle != NULL, APP_ERROR_INVALID_PARAM);
    
    // 读取设备ID
    hal_status = HAL_I2C_Mem_Read(i2c_handle, MPU6050_I2C_ADDR, 
                                  0x75, 1, &device_id, 1, I2C_TIMEOUT_MS);
    
    if (hal_status != HAL_OK) {
        PRINT_ERROR(APP_ERROR_I2C_FAILED);
        return APP_ERROR_I2C_FAILED;
    }
    
    if (device_id != MPU6050_DEVICE_ID) {
        PRINT_ERROR(APP_ERROR_SENSOR_NOT_FOUND);
        return APP_ERROR_SENSOR_NOT_FOUND;
    }
    
    // 配置传感器...
    return APP_OK;
}
```

**步骤2.5：更新main.c** (2小时)
```c
// 更新主程序中的错误处理
int main(void) {
    app_error_t result;
    
    // 系统初始化
    HAL_Init();
    SystemClock_Config();
    MX_GPIO_Init();
    MX_I2C1_Init();
    MX_I2C2_Init();
    MX_USART1_UART_Init();
    
    // 初始化显示
    ssd1306_Init();
    
    // 初始化传感器
    result = mpu6050_init(&hi2c1);
    if (result != APP_OK) {
        display_error_message(result);
        while(1) {
            HAL_Delay(1000);
        }
    }
    
    // 主循环
    while (1) {
        result = system_update();
        if (result != APP_OK) {
            handle_runtime_error(result);
        }
        HAL_Delay(MAIN_LOOP_PERIOD_MS);
    }
}
```

### **第3天：配置参数化**

#### **上午 (4小时)：替换硬编码数值**

**步骤3.1：更新main.c中的硬编码** (2小时)
```c
// 替换所有硬编码数值
// 原来：HAL_Delay(200);
// 改为：HAL_Delay(MAIN_LOOP_PERIOD_MS);

// 原来：snprintf(buffer, 32, "Pitch:%.2f", angle);
// 改为：snprintf(buffer, 32, "Pitch:%.*f", DATA_DECIMAL_PLACES, angle);
```

**步骤3.2：更新mpu6050.c中的硬编码** (2小时)
```c
// 替换传感器配置中的硬编码
// 原来：config_data = 0x00;
// 改为：config_data = MPU6050_ACCEL_FS_2G;

// 原来：data / 16384.0
// 改为：data / MPU6050_ACCEL_LSB_PER_G
```

#### **下午 (4小时)：配置验证和文档**

**步骤3.3：添加配置验证** (2小时)
```c
// 在 app_config.h 中添加配置验证
#if MAIN_LOOP_PERIOD_MS < 50
#error "Main loop period too small (minimum 50ms)"
#endif

#if MAIN_LOOP_PERIOD_MS > 1000
#error "Main loop period too large (maximum 1000ms)"
#endif

#if DATA_DECIMAL_PLACES > 3
#error "Too many decimal places (maximum 3)"
#endif
```

**步骤3.4：创建配置文档** (2小时)
```markdown
# 创建 Docs/CONFIGURATION.md
## 配置参数说明

### 系统配置
- MAIN_LOOP_PERIOD_MS: 主循环周期 (50-1000ms)
- DISPLAY_UPDATE_RATE_MS: 显示更新周期 (50-500ms)
- I2C_TIMEOUT_MS: I2C超时时间 (10-100ms)

### 传感器配置  
- MPU6050_ACCEL_RANGE: 加速度量程 (2, 4, 8, 16)
- MPU6050_GYRO_RANGE: 陀螺仪量程 (250, 500, 1000, 2000)
- MPU6050_SAMPLE_RATE_HZ: 采样率 (10-1000Hz)
```

---

## 🎯 阶段2：功能优化 (第4-5天)

### **第4天：传感器驱动优化**

#### **上午 (4小时)：I2C通信优化**

**步骤4.1：添加重试机制** (2小时)
```c
// 在 mpu6050.c 中添加重试机制
#define I2C_MAX_RETRY_COUNT 3

static app_error_t mpu6050_i2c_read_with_retry(I2C_HandleTypeDef *i2c_handle,
                                               uint16_t dev_addr, uint16_t mem_addr,
                                               uint8_t *data, uint16_t size) {
    HAL_StatusTypeDef hal_status;
    uint8_t retry_count = 0;
    
    do {
        hal_status = HAL_I2C_Mem_Read(i2c_handle, dev_addr, mem_addr, 
                                      1, data, size, I2C_TIMEOUT_MS);
        if (hal_status == HAL_OK) {
            return APP_OK;
        }
        
        retry_count++;
        HAL_Delay(10); // 重试间隔
        
    } while (retry_count < I2C_MAX_RETRY_COUNT);
    
    return APP_ERROR_I2C_FAILED;
}
```

**步骤4.2：数据验证优化** (2小时)
```c
// 添加数据验证函数
static app_error_t mpu6050_validate_data(const mpu6050_data_t *data) {
    // 检查加速度数据
    if (fabs(data->accel_x) > MPU6050_ACCEL_MAX_G ||
        fabs(data->accel_y) > MPU6050_ACCEL_MAX_G ||
        fabs(data->accel_z) > MPU6050_ACCEL_MAX_G) {
        return APP_ERROR_SENSOR_DATA_INVALID;
    }
    
    // 检查陀螺仪数据
    if (fabs(data->gyro_x) > MPU6050_GYRO_MAX_DPS ||
        fabs(data->gyro_y) > MPU6050_GYRO_MAX_DPS ||
        fabs(data->gyro_z) > MPU6050_GYRO_MAX_DPS) {
        return APP_ERROR_SENSOR_DATA_INVALID;
    }
    
    // 检查温度数据
    if (data->temperature < MPU6050_TEMP_MIN_C ||
        data->temperature > MPU6050_TEMP_MAX_C) {
        return APP_ERROR_SENSOR_DATA_INVALID;
    }
    
    return APP_OK;
}
```

#### **下午 (4小时)：校准功能和统计**

**步骤4.3：简单校准功能** (2小时)
```c
// 添加简单的零点校准
typedef struct {
    float accel_offset_x;
    float accel_offset_y;
    float accel_offset_z;
    float gyro_offset_x;
    float gyro_offset_y;
    float gyro_offset_z;
    bool is_calibrated;
} mpu6050_calibration_t;

static mpu6050_calibration_t calibration_data = {0};

app_error_t mpu6050_calibrate(I2C_HandleTypeDef *i2c_handle) {
    const int CALIBRATION_SAMPLES = 100;
    mpu6050_data_t temp_data;
    float sum_ax = 0, sum_ay = 0, sum_az = 0;
    float sum_gx = 0, sum_gy = 0, sum_gz = 0;
    
    printf("Starting calibration...\r\n");
    
    // 采集校准数据
    for (int i = 0; i < CALIBRATION_SAMPLES; i++) {
        app_error_t result = mpu6050_read_all_data(i2c_handle, &temp_data);
        if (result != APP_OK) {
            return result;
        }
        
        sum_ax += temp_data.accel_x;
        sum_ay += temp_data.accel_y;
        sum_az += temp_data.accel_z;
        sum_gx += temp_data.gyro_x;
        sum_gy += temp_data.gyro_y;
        sum_gz += temp_data.gyro_z;
        
        HAL_Delay(10);
    }
    
    // 计算偏移量
    calibration_data.accel_offset_x = sum_ax / CALIBRATION_SAMPLES;
    calibration_data.accel_offset_y = sum_ay / CALIBRATION_SAMPLES;
    calibration_data.accel_offset_z = (sum_az / CALIBRATION_SAMPLES) - 1.0f; // 重力补偿
    calibration_data.gyro_offset_x = sum_gx / CALIBRATION_SAMPLES;
    calibration_data.gyro_offset_y = sum_gy / CALIBRATION_SAMPLES;
    calibration_data.gyro_offset_z = sum_gz / CALIBRATION_SAMPLES;
    calibration_data.is_calibrated = true;
    
    printf("Calibration completed\r\n");
    return APP_OK;
}
```

**步骤4.4：性能统计** (2小时)
```c
// 添加简单的性能统计
typedef struct {
    uint32_t read_count;
    uint32_t success_count;
    uint32_t error_count;
    uint32_t last_read_time;
    float success_rate;
} mpu6050_stats_t;

static mpu6050_stats_t sensor_stats = {0};

void mpu6050_update_stats(app_error_t result) {
    sensor_stats.read_count++;
    sensor_stats.last_read_time = HAL_GetTick();
    
    if (result == APP_OK) {
        sensor_stats.success_count++;
    } else {
        sensor_stats.error_count++;
    }
    
    sensor_stats.success_rate = (float)sensor_stats.success_count / sensor_stats.read_count * 100.0f;
}

void mpu6050_print_stats(void) {
    printf("Sensor Stats: Reads=%lu, Success=%.1f%%, Errors=%lu\r\n",
           sensor_stats.read_count, sensor_stats.success_rate, sensor_stats.error_count);
}
```

### **第5天：滤波和显示优化**

#### **上午 (4小时)：卡尔曼滤波优化**

**步骤5.1：滤波参数调优** (2小时)
```c
// 创建 Core/Inc/kalman_config.h (简化版)
#ifndef __KALMAN_CONFIG_H
#define __KALMAN_CONFIG_H

/* ========================= 滤波器参数 ========================= */
// 基于实际测试的优化参数
#define KALMAN_Q_ANGLE      0.001f      // 角度过程噪声
#define KALMAN_Q_BIAS       0.003f      // 偏差过程噪声
#define KALMAN_R_MEASURE    0.03f       // 测量噪声

/* ========================= 角度限制 ========================= */
#define KALMAN_ANGLE_MIN    -180.0f     // 最小角度
#define KALMAN_ANGLE_MAX    180.0f      // 最大角度
#define KALMAN_DT_MIN       0.001f      // 最小时间间隔
#define KALMAN_DT_MAX       0.1f        // 最大时间间隔

#endif /* __KALMAN_CONFIG_H */
```

**步骤5.2：滤波器状态管理** (2小时)
```c
// 在 KalmanFilter/Kalman.c 中添加状态管理
typedef struct {
    bool is_initialized;
    bool is_converged;
    uint32_t sample_count;
    float last_angle;
    uint32_t last_update_time;
} kalman_state_t;

static kalman_state_t kalman_x_state = {0};
static kalman_state_t kalman_y_state = {0};

void kalman_reset(Kalman_t* kalman, kalman_state_t* state) {
    kalman->angle = 0.0f;
    kalman->bias = 0.0f;
    kalman->P[0][0] = 0.0f;
    kalman->P[0][1] = 0.0f;
    kalman->P[1][0] = 0.0f;
    kalman->P[1][1] = 0.0f;

    state->is_initialized = false;
    state->is_converged = false;
    state->sample_count = 0;
    state->last_angle = 0.0f;
    state->last_update_time = HAL_GetTick();
}

bool kalman_is_converged(kalman_state_t* state) {
    return state->is_converged && state->sample_count > 50;
}
```

#### **下午 (4小时)：显示系统优化**

**步骤5.3：显示布局优化** (2小时)
```c
// 创建 Core/Inc/display.h
#ifndef __DISPLAY_H
#define __DISPLAY_H

#include "ssd1306.h"
#include "app_config.h"
#include "error_handler.h"

typedef struct {
    float pitch;
    float roll;
    float temperature;
    app_error_t status;
    bool is_calibrated;
    float success_rate;
} display_data_t;

// 显示函数声明
app_error_t display_init(void);
app_error_t display_update_main_screen(const display_data_t* data);
app_error_t display_error_screen(app_error_t error);
app_error_t display_calibration_screen(uint8_t progress);
void display_clear(void);

#endif /* __DISPLAY_H */
```

**步骤5.4：显示功能实现** (2小时)
```c
// 创建 Core/Src/display.c
#include "display.h"
#include <stdio.h>

app_error_t display_init(void) {
    ssd1306_Init();
    ssd1306_Fill(Black);
    ssd1306_UpdateScreen();
    return APP_OK;
}

app_error_t display_update_main_screen(const display_data_t* data) {
    char buffer[32];

    ssd1306_Fill(Black);

    // 第一行：标题
    ssd1306_SetCursor(0, 0);
    ssd1306_WriteString("MPU6050 Monitor", DISPLAY_FONT_SIZE, White);

    // 第二行：俯仰角
    snprintf(buffer, sizeof(buffer), "Pitch:%.*f°",
             DATA_DECIMAL_PLACES, data->pitch);
    ssd1306_SetCursor(0, DISPLAY_LINE_HEIGHT);
    ssd1306_WriteString(buffer, DISPLAY_FONT_SIZE, White);

    // 第三行：横滚角
    snprintf(buffer, sizeof(buffer), "Roll: %.*f°",
             DATA_DECIMAL_PLACES, data->roll);
    ssd1306_SetCursor(0, DISPLAY_LINE_HEIGHT * 2);
    ssd1306_WriteString(buffer, DISPLAY_FONT_SIZE, White);

    // 第四行：温度和状态
    snprintf(buffer, sizeof(buffer), "T:%.*f°C %s",
             DATA_DECIMAL_PLACES, data->temperature,
             data->status == APP_OK ? "OK" : "ERR");
    ssd1306_SetCursor(0, DISPLAY_LINE_HEIGHT * 3);
    ssd1306_WriteString(buffer, DISPLAY_FONT_SIZE, White);

    ssd1306_UpdateScreen();
    return APP_OK;
}

app_error_t display_error_screen(app_error_t error) {
    char buffer[32];

    ssd1306_Fill(Black);

    ssd1306_SetCursor(0, 0);
    ssd1306_WriteString("ERROR DETECTED", DISPLAY_FONT_SIZE, White);

    snprintf(buffer, sizeof(buffer), "Code: %d", error);
    ssd1306_SetCursor(0, DISPLAY_LINE_HEIGHT);
    ssd1306_WriteString(buffer, DISPLAY_FONT_SIZE, White);

    ssd1306_SetCursor(0, DISPLAY_LINE_HEIGHT * 2);
    ssd1306_WriteString(app_error_get_message(error), DISPLAY_FONT_SIZE, White);

    ssd1306_SetCursor(0, DISPLAY_LINE_HEIGHT * 3);
    ssd1306_WriteString("Check connection", DISPLAY_FONT_SIZE, White);

    ssd1306_UpdateScreen();
    return APP_OK;
}
```

---

## 🎯 阶段3：质量保证 (第6-7天)

### **第6天：测试框架建立**

#### **上午 (4小时)：单元测试**

**步骤6.1：创建测试框架** (2小时)
```c
// 创建 Tests/test_framework.h
#ifndef __TEST_FRAMEWORK_H
#define __TEST_FRAMEWORK_H

#include <stdio.h>
#include <stdbool.h>

// 测试结果
typedef enum {
    TEST_PASS,
    TEST_FAIL
} test_result_t;

// 测试统计
typedef struct {
    int total_tests;
    int passed_tests;
    int failed_tests;
} test_stats_t;

// 测试宏
#define TEST_ASSERT(condition, message) \
    do { \
        if (!(condition)) { \
            printf("FAIL: %s - %s\r\n", __FUNCTION__, message); \
            return TEST_FAIL; \
        } \
    } while(0)

#define TEST_ASSERT_EQUAL(expected, actual, message) \
    TEST_ASSERT((expected) == (actual), message)

#define TEST_ASSERT_FLOAT_EQUAL(expected, actual, tolerance, message) \
    TEST_ASSERT(fabs((expected) - (actual)) < (tolerance), message)

// 测试函数声明
void test_run_all(void);
test_result_t test_mpu6050_init(void);
test_result_t test_mpu6050_data_validation(void);
test_result_t test_error_handling(void);
test_result_t test_kalman_filter(void);

#endif /* __TEST_FRAMEWORK_H */
```

**步骤6.2：实现核心测试** (2小时)
```c
// 创建 Tests/test_framework.c
#include "test_framework.h"
#include "mpu6050.h"
#include "error_handler.h"
#include "Kalman.h"

static test_stats_t test_stats = {0};

static void run_test(const char* test_name, test_result_t (*test_func)(void)) {
    printf("Running %s... ", test_name);
    test_stats.total_tests++;

    test_result_t result = test_func();
    if (result == TEST_PASS) {
        printf("PASS\r\n");
        test_stats.passed_tests++;
    } else {
        printf("FAIL\r\n");
        test_stats.failed_tests++;
    }
}

void test_run_all(void) {
    printf("\r\n=== Running Unit Tests ===\r\n");

    run_test("MPU6050 Init", test_mpu6050_init);
    run_test("Data Validation", test_mpu6050_data_validation);
    run_test("Error Handling", test_error_handling);
    run_test("Kalman Filter", test_kalman_filter);

    printf("\r\n=== Test Results ===\r\n");
    printf("Total: %d, Passed: %d, Failed: %d\r\n",
           test_stats.total_tests, test_stats.passed_tests, test_stats.failed_tests);
    printf("Success Rate: %.1f%%\r\n",
           (float)test_stats.passed_tests / test_stats.total_tests * 100.0f);
}

test_result_t test_mpu6050_init(void) {
    // 测试空指针检查
    app_error_t result = mpu6050_init(NULL);
    TEST_ASSERT_EQUAL(APP_ERROR_INVALID_PARAM, result, "Should reject NULL pointer");

    return TEST_PASS;
}

test_result_t test_mpu6050_data_validation(void) {
    mpu6050_data_t test_data;

    // 测试正常数据
    test_data.accel_x = 1.0f;
    test_data.accel_y = 0.5f;
    test_data.accel_z = 0.8f;
    test_data.gyro_x = 10.0f;
    test_data.gyro_y = -5.0f;
    test_data.gyro_z = 2.0f;
    test_data.temperature = 25.0f;

    app_error_t result = mpu6050_validate_data(&test_data);
    TEST_ASSERT_EQUAL(APP_OK, result, "Valid data should pass");

    // 测试超范围数据
    test_data.accel_x = 10.0f; // 超过最大值
    result = mpu6050_validate_data(&test_data);
    TEST_ASSERT_EQUAL(APP_ERROR_SENSOR_DATA_INVALID, result, "Invalid data should fail");

    return TEST_PASS;
}
```

#### **下午 (4小时)：集成测试**

**步骤6.3：硬件在环测试** (2小时)
```c
// 创建 Tests/integration_test.c
#include "test_framework.h"
#include "main.h"

test_result_t test_system_integration(void) {
    app_error_t result;
    mpu6050_data_t sensor_data;
    display_data_t display_data;

    // 测试传感器读取
    result = mpu6050_read_all_data(&hi2c1, &sensor_data);
    TEST_ASSERT_EQUAL(APP_OK, result, "Sensor read should succeed");

    // 测试数据范围
    TEST_ASSERT(fabs(sensor_data.accel_x) < 5.0f, "Accel X in range");
    TEST_ASSERT(fabs(sensor_data.accel_y) < 5.0f, "Accel Y in range");
    TEST_ASSERT(fabs(sensor_data.accel_z) < 5.0f, "Accel Z in range");

    // 测试显示更新
    display_data.pitch = sensor_data.pitch;
    display_data.roll = sensor_data.roll;
    display_data.temperature = sensor_data.temperature;
    display_data.status = APP_OK;

    result = display_update_main_screen(&display_data);
    TEST_ASSERT_EQUAL(APP_OK, result, "Display update should succeed");

    return TEST_PASS;
}
```

**步骤6.4：性能测试** (2小时)
```c
// 创建 Tests/performance_test.c
test_result_t test_system_performance(void) {
    uint32_t start_time, end_time, duration;
    app_error_t result;
    mpu6050_data_t sensor_data;

    // 测试传感器读取性能
    start_time = HAL_GetTick();
    for (int i = 0; i < 100; i++) {
        result = mpu6050_read_all_data(&hi2c1, &sensor_data);
        TEST_ASSERT_EQUAL(APP_OK, result, "Sensor read should succeed");
    }
    end_time = HAL_GetTick();
    duration = end_time - start_time;

    printf("100 sensor reads took %lu ms (avg: %.1f ms)\r\n",
           duration, (float)duration / 100.0f);

    // 性能要求：单次读取应该在20ms内完成
    TEST_ASSERT(duration < 2000, "Performance requirement: <20ms per read");

    return TEST_PASS;
}
```

### **第7天：文档和最终验证**

#### **上午 (4小时)：文档完善**

**步骤7.1：用户手册** (2小时)
```markdown
# 创建 Docs/USER_GUIDE.md
# MPU6050姿态检测系统用户手册

## 快速开始

### 硬件连接
- VCC → 3.3V
- GND → GND
- SCL → PB6 (I2C1_SCL)
- SDA → PB7 (I2C1_SDA)

### 编译和烧录
1. 打开STM32CubeIDE
2. 导入项目
3. 编译 (Ctrl+B)
4. 烧录 (F11)

### 使用说明
1. 上电后系统自动初始化
2. OLED显示当前姿态角度
3. 串口输出详细数据 (115200波特率)

## 配置说明

### 基础配置 (app_config.h)
- MAIN_LOOP_PERIOD_MS: 主循环周期 (默认200ms)
- DEBUG_ENABLE: 调试输出开关 (默认开启)

### 传感器配置 (mpu6050_config.h)
- MPU6050_ACCEL_RANGE: 加速度量程 (默认±2g)
- MPU6050_GYRO_RANGE: 陀螺仪量程 (默认±250°/s)

## 故障排除

### 常见问题
1. 显示屏无显示 → 检查I2C连接
2. 数据跳变 → 进行校准操作
3. 初始化失败 → 检查传感器连接
```

**步骤7.2：开发文档** (2小时)
```markdown
# 创建 Docs/DEVELOPER_GUIDE.md
# 开发者指南

## 代码结构
```
Core/
├── Inc/
│   ├── app_config.h      # 应用配置
│   ├── mpu6050.h         # 传感器接口
│   ├── error_handler.h   # 错误处理
│   └── display.h         # 显示接口
└── Src/
    ├── main.c            # 主程序
    ├── mpu6050.c         # 传感器实现
    ├── error_handler.c   # 错误处理实现
    └── display.c         # 显示实现
```

## API参考

### 传感器接口
```c
app_error_t mpu6050_init(I2C_HandleTypeDef *i2c_handle);
app_error_t mpu6050_read_all_data(I2C_HandleTypeDef *i2c_handle, mpu6050_data_t *data);
app_error_t mpu6050_calibrate(I2C_HandleTypeDef *i2c_handle);
```

### 显示接口
```c
app_error_t display_update_main_screen(const display_data_t* data);
app_error_t display_error_screen(app_error_t error);
```

## 扩展指南

### 添加新传感器
1. 在 app_config.h 中添加配置
2. 创建传感器驱动文件
3. 在 main.c 中集成

### 修改显示内容
1. 修改 display_data_t 结构体
2. 更新 display_update_main_screen() 函数
```

#### **下午 (4小时)：最终验证**

**步骤7.3：完整系统测试** (2小时)
```c
// 创建完整的系统验证程序
void system_validation_test(void) {
    printf("\r\n=== System Validation Test ===\r\n");

    // 1. 初始化测试
    printf("1. Testing initialization...\r\n");
    app_error_t result = mpu6050_init(&hi2c1);
    if (result != APP_OK) {
        printf("FAIL: Initialization failed\r\n");
        return;
    }
    printf("PASS: Initialization successful\r\n");

    // 2. 数据读取测试
    printf("2. Testing data reading...\r\n");
    mpu6050_data_t sensor_data;
    for (int i = 0; i < 10; i++) {
        result = mpu6050_read_all_data(&hi2c1, &sensor_data);
        if (result != APP_OK) {
            printf("FAIL: Data read failed at iteration %d\r\n", i);
            return;
        }
        HAL_Delay(100);
    }
    printf("PASS: Data reading successful\r\n");

    // 3. 显示测试
    printf("3. Testing display...\r\n");
    display_data_t display_data = {
        .pitch = sensor_data.pitch,
        .roll = sensor_data.roll,
        .temperature = sensor_data.temperature,
        .status = APP_OK
    };
    result = display_update_main_screen(&display_data);
    if (result != APP_OK) {
        printf("FAIL: Display update failed\r\n");
        return;
    }
    printf("PASS: Display update successful\r\n");

    // 4. 性能测试
    printf("4. Testing performance...\r\n");
    uint32_t start_time = HAL_GetTick();
    for (int i = 0; i < 50; i++) {
        mpu6050_read_all_data(&hi2c1, &sensor_data);
        display_update_main_screen(&display_data);
        HAL_Delay(MAIN_LOOP_PERIOD_MS);
    }
    uint32_t total_time = HAL_GetTick() - start_time;
    printf("50 cycles completed in %lu ms\r\n", total_time);
    printf("Average cycle time: %.1f ms\r\n", (float)total_time / 50.0f);

    printf("\r\n=== System Validation PASSED ===\r\n");
}
```

**步骤7.4：代码质量检查** (2小时)
```bash
# 代码质量检查清单

# 1. 编译检查
make clean && make all
# 应该无警告无错误

# 2. 静态分析 (如果有工具)
cppcheck --enable=all Core/Src/*.c

# 3. 代码行数统计
find Core/Src -name "*.c" | xargs wc -l
find Core/Inc -name "*.h" | xargs wc -l
# 目标：总行数 < 1500行

# 4. 函数复杂度检查
# 确保单个函数不超过50行

# 5. 内存使用检查
# 编译后检查RAM和Flash使用情况
```

---

## 📊 **验收检查清单**

### **功能验收**
- [ ] MPU6050初始化成功
- [ ] 数据读取稳定无跳变
- [ ] OLED显示正常
- [ ] 错误处理正确响应
- [ ] 校准功能工作正常

### **性能验收**
- [ ] 主循环周期稳定在200ms±10%
- [ ] 传感器读取时间<20ms
- [ ] 内存使用: RAM<50%, Flash<70%
- [ ] 错误恢复时间<1秒

### **代码质量验收**
- [ ] 总代码行数<1500行
- [ ] 单个函数<50行
- [ ] 无编译警告
- [ ] 测试覆盖率>80%
- [ ] 文档完整

### **可维护性验收**
- [ ] 新手可在2小时内理解代码
- [ ] 配置修改无需重新编译
- [ ] 添加新功能影响<3个文件
- [ ] 问题定位时间<30分钟

---

## 🎉 **项目交付**

### **交付物清单**
1. **源代码**: 简化后的完整源码
2. **文档**: 用户手册、开发指南、API文档
3. **测试**: 单元测试、集成测试、性能测试
4. **配置**: 详细的配置说明和示例

### **后续维护**
1. **定期测试**: 每月运行完整测试套件
2. **性能监控**: 跟踪关键性能指标
3. **文档更新**: 根据使用反馈更新文档
4. **社区支持**: 建立问题反馈和解决机制

---

**实施指南完成时间**: 2025-01-27
**预计实施周期**: 7个工作日
**质量保证**: 每日检查点和最终验收
**成功标准**: 所有验收项目100%通过 ✅
```
