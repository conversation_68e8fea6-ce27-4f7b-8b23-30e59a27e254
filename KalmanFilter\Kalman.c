// Minimal <PERSON>lman filter implementation for angle estimation
#include "Kalman.h"
#include <string.h>

void Kalman_Init(Kalman_t* kalman) {
    if (!kalman) return;
    kalman->Q_angle = 0.001f;
    kalman->Q_bias = 0.003f;
    kalman->R_measure = 0.03f;
    kalman->angle = 0.0f;
    kalman->bias = 0.0f;
    // 初始化协方差矩阵为非零值
    kalman->P[0][0] = 1.0f;
    kalman->P[0][1] = 0.0f;
    kalman->P[1][0] = 0.0f;
    kalman->P[1][1] = 1.0f;
}

float Kalman_getAngle(Kalman_t* kalman, float newAngle, float newRate, float dt) {
    // Predict
    float rate = newRate - kalman->bias;
    kalman->angle += dt * rate;

    kalman->P[0][0] += dt * (dt*kalman->P[1][1] - kalman->P[0][1] - kalman->P[1][0] + kalman->Q_angle);
    kalman->P[0][1] -= dt * kalman->P[1][1];
    kalman->P[1][0] -= dt * kalman->P[1][1];
    kalman->P[1][1] += kalman->Q_bias * dt;

    // Update
    float S = kalman->P[0][0] + kalman->R_measure;
    float K[2];
    K[0] = kalman->P[0][0] / S;
    K[1] = kalman->P[1][0] / S;
    float y = newAngle - kalman->angle;
    kalman->angle += K[0] * y;
    kalman->bias += K[1] * y;
    float P00_temp = kalman->P[0][0];
    float P01_temp = kalman->P[0][1];
    kalman->P[0][0] -= K[0] * P00_temp;
    kalman->P[0][1] -= K[0] * P01_temp;
    kalman->P[1][0] -= K[1] * P00_temp;
    kalman->P[1][1] -= K[1] * P01_temp;
    return kalman->angle;
}
