#ifndef __KALMAN_H
#define __KALMAN_H

#include <stdint.h>
#include "kalman_config.h"

#ifdef __cplusplus
extern "C" {
#endif

typedef struct {
    float Q_angle;
    float Q_bias;
    float R_measure;
    float angle;
    float bias;
    float rate;
    float P[2][2];
} <PERSON><PERSON>_t;

void Kalman_Init(Kalman_t* kalman);
void Kalman_setQangle(<PERSON><PERSON>_t* kalman, float Q_angle);
void Ka<PERSON>_setQbias(<PERSON><PERSON>_t* kalman, float Q_bias);
void Kalman_setRmeasure(<PERSON><PERSON>_t* kalman, float R_measure);
float Kalman_getAngle(<PERSON><PERSON>_t* kalman, float newAngle, float newRate, float dt);

#ifdef __cplusplus
}
#endif

#endif // __KALMAN_H
