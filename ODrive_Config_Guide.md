# ODrive 3.6 配置指南

## 🔧 硬件连接

### STM32F411CEU6 ↔ ODrive 3.6
```
STM32F411CEU6    ←→    ODrive 3.6
PA9 (UART1_TX)   ←→    GPIO1 (RX)
PA10 (UART1_RX)  ←→    GPIO2 (TX)
GND              ←→    GND
5V               ←→    5V (可选，用于为STM32供电)
```

### 电机连接
```
ODrive 3.6       ←→    电机
M0 (A,B,C)       ←→    左电机 (3相无刷电机)
M1 (A,B,C)       ←→    右电机 (3相无刷电机)
```

### 编码器连接
```
ODrive 3.6       ←→    编码器
Encoder0 (A,B,Z) ←→    左电机编码器
Encoder1 (A,B,Z) ←→    右电机编码器
```

## ⚙️ ODrive配置步骤

### 1. 连接ODrive到电脑
使用USB连接ODrive到电脑，打开ODrive Tool：
```bash
pip install odrive
odrivetool
```

### 2. 基本配置
```python
# 设置UART通信
odrv0.config.uart_baudrate = 115200
odrv0.config.enable_uart = True

# 设置GPIO1和GPIO2为UART功能
odrv0.config.gpio1_mode = GPIO_MODE_UART_A
odrv0.config.gpio2_mode = GPIO_MODE_UART_A

# 保存配置
odrv0.save_configuration()
odrv0.reboot()
```

### 3. 电机配置 (以常见的2208电机为例)
```python
# 轴0 (左电机) 配置
odrv0.axis0.motor.config.pole_pairs = 7
odrv0.axis0.motor.config.resistance_calib_max_voltage = 2.0
odrv0.axis0.motor.config.requested_current_range = 25.0
odrv0.axis0.motor.config.current_control_bandwidth = 100.0
odrv0.axis0.motor.config.torque_constant = 8.27 / 150.0

# 轴1 (右电机) 配置 - 相同设置
odrv0.axis1.motor.config.pole_pairs = 7
odrv0.axis1.motor.config.resistance_calib_max_voltage = 2.0
odrv0.axis1.motor.config.requested_current_range = 25.0
odrv0.axis1.motor.config.current_control_bandwidth = 100.0
odrv0.axis1.motor.config.torque_constant = 8.27 / 150.0
```

### 4. 编码器配置 (增量式编码器)
```python
# 轴0编码器配置
odrv0.axis0.encoder.config.mode = ENCODER_MODE_INCREMENTAL
odrv0.axis0.encoder.config.cpr = 2000  # 根据实际编码器调整
odrv0.axis0.encoder.config.bandwidth = 3000.0

# 轴1编码器配置
odrv0.axis1.encoder.config.mode = ENCODER_MODE_INCREMENTAL
odrv0.axis1.encoder.config.cpr = 2000
odrv0.axis1.encoder.config.bandwidth = 3000.0
```

### 5. 控制器配置
```python
# 轴0控制器配置
odrv0.axis0.controller.config.control_mode = CONTROL_MODE_VELOCITY_CONTROL
odrv0.axis0.controller.config.input_mode = INPUT_MODE_PASSTHROUGH
odrv0.axis0.controller.config.vel_limit = 10.0  # 最大速度 10 turns/s
odrv0.axis0.controller.config.vel_gain = 0.16
odrv0.axis0.controller.config.vel_integrator_gain = 0.32

# 轴1控制器配置 - 相同设置
odrv0.axis1.controller.config.control_mode = CONTROL_MODE_VELOCITY_CONTROL
odrv0.axis1.controller.config.input_mode = INPUT_MODE_PASSTHROUGH
odrv0.axis1.controller.config.vel_limit = 10.0
odrv0.axis1.controller.config.vel_gain = 0.16
odrv0.axis1.controller.config.vel_integrator_gain = 0.32
```

### 6. 校准步骤
```python
# 保存配置
odrv0.save_configuration()
odrv0.reboot()

# 重新连接后进行校准
# 电机校准
odrv0.axis0.requested_state = AXIS_STATE_MOTOR_CALIBRATION
odrv0.axis1.requested_state = AXIS_STATE_MOTOR_CALIBRATION

# 等待校准完成
# 检查错误: odrv0.axis0.error, odrv0.axis1.error

# 编码器校准
odrv0.axis0.requested_state = AXIS_STATE_ENCODER_OFFSET_CALIBRATION
odrv0.axis1.requested_state = AXIS_STATE_ENCODER_OFFSET_CALIBRATION

# 等待校准完成
# 检查错误: odrv0.axis0.encoder.error, odrv0.axis1.encoder.error

# 设置校准完成标志
odrv0.axis0.motor.config.pre_calibrated = True
odrv0.axis1.motor.config.pre_calibrated = True
odrv0.axis0.encoder.config.pre_calibrated = True
odrv0.axis1.encoder.config.pre_calibrated = True

# 保存配置
odrv0.save_configuration()
```

### 7. 测试运行
```python
# 启动闭环控制
odrv0.axis0.requested_state = AXIS_STATE_CLOSED_LOOP_CONTROL
odrv0.axis1.requested_state = AXIS_STATE_CLOSED_LOOP_CONTROL

# 测试速度控制
odrv0.axis0.controller.input_vel = 1.0  # 1 turn/s
odrv0.axis1.controller.input_vel = 1.0

# 停止
odrv0.axis0.controller.input_vel = 0.0
odrv0.axis1.controller.input_vel = 0.0

# 回到空闲状态
odrv0.axis0.requested_state = AXIS_STATE_IDLE
odrv0.axis1.requested_state = AXIS_STATE_IDLE
```

## 🚀 平衡车专用配置

### 平衡车PID参数建议
```python
# 速度环PID (平衡车需要快速响应)
odrv0.axis0.controller.config.vel_gain = 0.2
odrv0.axis0.controller.config.vel_integrator_gain = 0.4
odrv0.axis1.controller.config.vel_gain = 0.2
odrv0.axis1.controller.config.vel_integrator_gain = 0.4

# 电流限制 (保护电机)
odrv0.axis0.motor.config.current_lim = 15.0  # 15A限制
odrv0.axis1.motor.config.current_lim = 15.0

# 速度限制 (安全考虑)
odrv0.axis0.controller.config.vel_limit = 8.0  # 8 turns/s
odrv0.axis1.controller.config.vel_limit = 8.0
```

### 启动序列配置
```python
# 设置启动序列，上电自动进入闭环控制
odrv0.axis0.config.startup_motor_calibration = False  # 已校准，跳过
odrv0.axis0.config.startup_encoder_index_search = False
odrv0.axis0.config.startup_encoder_offset_calibration = False
odrv0.axis0.config.startup_closed_loop_control = True  # 自动进入闭环

odrv0.axis1.config.startup_motor_calibration = False
odrv0.axis1.config.startup_encoder_index_search = False
odrv0.axis1.config.startup_encoder_offset_calibration = False
odrv0.axis1.config.startup_closed_loop_control = True

# 保存配置
odrv0.save_configuration()
```

## 🔍 故障排除

### 常见错误及解决方法

1. **ERROR_MOTOR_NOT_CALIBRATED**
   - 解决：重新进行电机校准

2. **ERROR_ENCODER_NOT_CALIBRATED**
   - 解决：重新进行编码器校准

3. **ERROR_CURRENT_LIMIT_VIOLATION**
   - 解决：降低电流限制或检查电机负载

4. **ERROR_VELOCITY_LIMIT_VIOLATION**
   - 解决：降低速度限制

5. **通信超时**
   - 检查UART连接
   - 检查波特率设置
   - 检查GPIO配置

### 调试命令
```python
# 检查系统状态
odrv0.vbus_voltage  # 电源电压
odrv0.axis0.error   # 轴0错误
odrv0.axis1.error   # 轴1错误
odrv0.axis0.motor.error  # 电机错误
odrv0.axis0.encoder.error  # 编码器错误

# 清除错误
odrv0.clear_errors()

# 实时监控
odrv0.axis0.encoder.vel_estimate  # 当前速度
odrv0.axis0.encoder.pos_estimate  # 当前位置
odrv0.axis0.motor.current_control.Iq_measured  # 当前电流
```

## 📝 注意事项

1. **安全第一**：调试时确保电机能自由转动，避免卡死
2. **电源要求**：确保电源能提供足够的电流 (建议24V 10A以上)
3. **散热**：ODrive和电机需要良好的散热
4. **接线**：确保所有连接牢固，特别是编码器信号线
5. **备份配置**：校准完成后及时保存配置

## 🎯 性能优化建议

1. **控制频率**：ODrive内部控制频率为8kHz，STM32发送命令频率建议200Hz
2. **通信优化**：使用批量命令减少通信延迟
3. **参数调优**：根据实际机械特性调整PID参数
4. **错误处理**：实现完善的错误检测和恢复机制
