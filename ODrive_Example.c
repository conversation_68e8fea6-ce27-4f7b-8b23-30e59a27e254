/**
 ******************************************************************************
 * @file           : ODrive_Example.c
 * @brief          : ODrive 3.6平衡车控制示例
 * @version        : v1.0
 * @date           : 2025-01-27
 ******************************************************************************
 * @description    :
 * 完整的ODrive平衡车控制示例，展示如何：
 * 1. 初始化ODrive系统
 * 2. 实现平衡车控制逻辑
 * 3. 错误处理和恢复
 * 4. 性能监控
 ******************************************************************************
 */

#include "main.h"
#include "odrive_driver.h"
#include "mpu6050.h"
#include "balance_controller.h"

// ODrive平衡车控制示例
void odrive_balance_car_example(void) {
    // 1. 初始化ODrive
    odrive_config_t config = {
        .huart = &huart1,
        .timeout_ms = 100,
        .max_velocity = 8.0f,      // 8 turns/s 最大速度
        .max_current = 15.0f,      // 15A 最大电流
        .control_mode = ODRIVE_CONTROL_VELOCITY
    };
    
    if (ODrive_Init(&config) != HAL_OK) {
        printf("ODrive initialization failed!\r\n");
        return;
    }
    
    // 2. 自检
    if (!ODrive_SelfTest()) {
        printf("ODrive self-test failed!\r\n");
        return;
    }
    
    printf("ODrive initialized successfully!\r\n");
    
    // 3. 启动电机
    ODrive_StartAxis(0);  // 左电机
    ODrive_StartAxis(1);  // 右电机
    HAL_Delay(1000);      // 等待启动完成
    
    // 4. 主控制循环
    uint32_t last_time = HAL_GetTick();
    uint32_t status_check_time = 0;
    
    while (1) {
        uint32_t current_time = HAL_GetTick();
        
        // 5ms控制周期 (200Hz)
        if (current_time - last_time >= 5) {
            last_time = current_time;
            
            // 读取传感器数据
            mpu6050_data_t sensor_data;
            if (MPU6050_ReadData(&sensor_data) == HAL_OK) {
                
                // 平衡控制算法 (简化版)
                float pitch_error = 0.0f - sensor_data.pitch;  // 目标角度为0度
                float pitch_rate = sensor_data.pitch_rate;
                
                // 简单PD控制
                float kp = 50.0f;   // 比例系数
                float kd = 5.0f;    // 微分系数
                
                float control_output = kp * pitch_error + kd * pitch_rate;
                
                // 限制输出范围
                if (control_output > 1000) control_output = 1000;
                if (control_output < -1000) control_output = -1000;
                
                // 设置电机速度
                ODrive_SetMotorSpeed((int16_t)control_output, (int16_t)control_output);
                
                // 安全检查：如果倾斜角度过大，停止电机
                if (fabs(sensor_data.pitch) > 45.0f) {
                    ODrive_EmergencyStop();
                    printf("Emergency stop: pitch angle too large!\r\n");
                    break;
                }
            }
        }
        
        // 每100ms检查一次ODrive状态
        if (current_time - status_check_time >= 100) {
            status_check_time = current_time;
            
            odrive_motor_status_t status0, status1;
            if (ODrive_GetStatus(0, &status0) == HAL_OK && 
                ODrive_GetStatus(1, &status1) == HAL_OK) {
                
                // 检查错误状态
                if (status0.error != 0 || status1.error != 0) {
                    printf("ODrive error detected: Axis0=%lu, Axis1=%lu\r\n", 
                           status0.error, status1.error);
                    
                    // 清除错误并重新启动
                    ODrive_ClearErrors(0);
                    ODrive_ClearErrors(1);
                    ODrive_StartAxis(0);
                    ODrive_StartAxis(1);
                }
                
                // 打印状态信息 (每秒一次)
                static uint32_t print_time = 0;
                if (current_time - print_time >= 1000) {
                    print_time = current_time;
                    printf("Motor Status - L: %.2f turns/s, R: %.2f turns/s\r\n",
                           status0.velocity, status1.velocity);
                }
            }
        }
        
        // 检查通信状态
        if (!ODrive_CheckCommunication()) {
            printf("ODrive communication lost!\r\n");
            ODrive_EmergencyStop();
            break;
        }
    }
    
    // 停止电机
    ODrive_EmergencyStop();
    printf("Balance control stopped.\r\n");
}

// ODrive高级控制示例
void odrive_advanced_control_example(void) {
    // 位置控制示例
    printf("Position control example...\r\n");
    
    // 设置为位置控制模式
    ODrive_SetControlMode(0, ODRIVE_CONTROL_POSITION);
    ODrive_SetControlMode(1, ODRIVE_CONTROL_POSITION);
    
    // 启动电机
    ODrive_StartAxis(0);
    ODrive_StartAxis(1);
    HAL_Delay(1000);
    
    // 移动到指定位置
    ODrive_SetPosition(0, 5.0f, 2.0f);  // 移动到5圈位置，速度限制2 turns/s
    ODrive_SetPosition(1, 5.0f, 2.0f);
    
    HAL_Delay(3000);  // 等待移动完成
    
    // 返回原点
    ODrive_SetPosition(0, 0.0f, 2.0f);
    ODrive_SetPosition(1, 0.0f, 2.0f);
    
    HAL_Delay(3000);
    
    // 电流控制示例
    printf("Current control example...\r\n");
    
    // 设置为电流控制模式
    ODrive_SetControlMode(0, ODRIVE_CONTROL_CURRENT);
    ODrive_SetControlMode(1, ODRIVE_CONTROL_CURRENT);
    
    // 施加恒定电流
    ODrive_SetCurrent(0, 2.0f);  // 2A电流
    ODrive_SetCurrent(1, 2.0f);
    
    HAL_Delay(2000);
    
    // 停止
    ODrive_SetCurrent(0, 0.0f);
    ODrive_SetCurrent(1, 0.0f);
    
    ODrive_StopAxis(0);
    ODrive_StopAxis(1);
    
    printf("Advanced control examples completed.\r\n");
}

// ODrive系统监控示例
void odrive_system_monitor_example(void) {
    float vbus_voltage, temperature;
    
    // 获取系统信息
    if (ODrive_GetSystemInfo(&vbus_voltage, &temperature) == HAL_OK) {
        printf("System Info:\r\n");
        printf("  Bus Voltage: %.2f V\r\n", vbus_voltage);
        printf("  Temperature: %.2f °C\r\n", temperature);
    }
    
    // 监控电机状态
    odrive_motor_status_t status[2];
    
    for (int i = 0; i < 2; i++) {
        if (ODrive_GetStatus(i, &status[i]) == HAL_OK) {
            printf("Motor %d Status:\r\n", i);
            printf("  Velocity: %.3f turns/s\r\n", status[i].velocity);
            printf("  Position: %.3f turns\r\n", status[i].position);
            printf("  Current: %.3f A\r\n", status[i].current);
            printf("  State: %d\r\n", status[i].state);
            printf("  Error: %lu\r\n", status[i].error);
        }
    }
    
    // 检查连接状态
    if (ODrive_IsConnected()) {
        printf("ODrive connection: OK\r\n");
    } else {
        printf("ODrive connection: FAILED\r\n");
    }
}

// ODrive错误处理示例
void odrive_error_handling_example(void) {
    // 模拟错误处理流程
    
    // 1. 检查并清除错误
    ODrive_ClearErrors(0);
    ODrive_ClearErrors(1);
    
    // 2. 重新初始化
    odrive_config_t config = {
        .huart = &huart1,
        .timeout_ms = 100,
        .max_velocity = 8.0f,
        .max_current = 15.0f,
        .control_mode = ODRIVE_CONTROL_VELOCITY
    };
    
    if (ODrive_Init(&config) != HAL_OK) {
        printf("ODrive re-initialization failed!\r\n");
        return;
    }
    
    // 3. 重新启动电机
    if (ODrive_StartAxis(0) == HAL_OK && ODrive_StartAxis(1) == HAL_OK) {
        printf("ODrive recovery successful!\r\n");
    } else {
        printf("ODrive recovery failed!\r\n");
    }
}

// ODrive性能测试示例
void odrive_performance_test_example(void) {
    uint32_t start_time, end_time;
    
    // 测试通信延迟
    start_time = HAL_GetTick();
    for (int i = 0; i < 100; i++) {
        ODrive_SetVelocity(0, 1.0f);
    }
    end_time = HAL_GetTick();
    
    printf("Communication test:\r\n");
    printf("  100 commands in %lu ms\r\n", end_time - start_time);
    printf("  Average: %.2f ms per command\r\n", (end_time - start_time) / 100.0f);
    
    // 测试状态读取速度
    start_time = HAL_GetTick();
    odrive_motor_status_t status;
    for (int i = 0; i < 50; i++) {
        ODrive_GetStatus(0, &status);
    }
    end_time = HAL_GetTick();
    
    printf("Status read test:\r\n");
    printf("  50 reads in %lu ms\r\n", end_time - start_time);
    printf("  Average: %.2f ms per read\r\n", (end_time - start_time) / 50.0f);
}
