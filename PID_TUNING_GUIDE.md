# 🎛️ 平衡车PID参数调试完整指南

## 📋 调试前准备

### 1. 硬件检查清单
- [ ] MPU6050传感器安装牢固，轴向正确
- [ ] 电机驱动电路连接正确
- [ ] 电源电压稳定(建议7.4V锂电池)
- [ ] 车轮安装平衡，无明显偏心
- [ ] 重心位置合理(略高于轮轴)

### 2. 软件准备
- [ ] 传感器校准完成
- [ ] 串口调试正常(115200波特率)
- [ ] OLED显示正常
- [ ] 电机方向测试正确

### 3. 安全措施
- [ ] 设置角度保护(±45°自动停机)
- [ ] 准备紧急停止按钮
- [ ] 在安全的开阔场地调试
- [ ] 准备软垫防止摔坏

## 🎯 三环PID调试步骤

### 第一步：角度环调试 (最重要)

#### 1.1 初始设置
```c
// 在balance_controller.c中修改参数
#define DEFAULT_ANGLE_KP        0.0f    // 从0开始
#define DEFAULT_ANGLE_KI        0.0f    // 保持为0
#define DEFAULT_ANGLE_KD        0.0f    // 从0开始

// 禁用其他环
#define DEFAULT_SPEED_KP        0.0f
#define DEFAULT_TURN_KP         0.0f
```

#### 1.2 调试Kp参数
1. **启动系统**，手扶平衡车保持垂直
2. **逐步增加Kp**：10 → 20 → 30 → 40 → 50...
3. **观察现象**：
   - Kp太小：车辆反应迟钝，容易倾倒
   - Kp合适：车辆能基本保持平衡，有轻微振荡
   - Kp太大：车辆剧烈振荡，无法稳定

**目标**：找到能让车辆基本平衡但有轻微振荡的Kp值

#### 1.3 调试Kd参数
1. **固定Kp**为上一步找到的值
2. **逐步增加Kd**：0.5 → 1.0 → 1.5 → 2.0...
3. **观察现象**：
   - Kd太小：振荡明显
   - Kd合适：振荡减小，响应平稳
   - Kd太大：响应变慢，可能出现高频振荡

**目标**：消除或大幅减少振荡，保持快速响应

#### 1.4 参考参数范围
```c
// 典型参数范围 (根据车辆重量和尺寸调整)
角度环Kp: 50-150   // 轻车用小值，重车用大值
角度环Ki: 0        // 通常不需要
角度环Kd: 1-5      // 抑制振荡
```

### 第二步：速度环调试

#### 2.1 启用速度环
```c
#define DEFAULT_SPEED_KP        0.5f    // 从小值开始
#define DEFAULT_SPEED_KI        0.0f    // 先不用积分
```

#### 2.2 测试前进后退
1. **设置目标速度**：`Balance_SetTarget(&controller, 0, 10, 0)`
2. **观察车辆**是否能向前移动并停在原位附近
3. **调整Kp**：
   - 太小：车辆移动缓慢，不能回到原位
   - 太大：车辆移动过快，超调严重

#### 2.3 添加积分项
1. **逐步增加Ki**：0.01 → 0.05 → 0.1...
2. **观察**车辆是否能准确回到原位
3. **注意**积分饱和问题

#### 2.4 参考参数范围
```c
速度环Kp: 0.3-1.5
速度环Ki: 0.01-0.2
速度环Kd: 0       // 通常不需要
```

### 第三步：转向环调试

#### 3.1 启用转向环
```c
#define DEFAULT_TURN_KP         1.0f    // 从小值开始
```

#### 3.2 测试转向
1. **设置转向指令**：`Balance_SetTarget(&controller, 0, 0, 30)`
2. **观察车辆**是否能平稳转向
3. **调整Kp**：
   - 太小：转向缓慢
   - 太大：转向过快，可能失控

#### 3.3 参考参数范围
```c
转向环Kp: 1.0-5.0
转向环Ki: 0       // 通常不需要
转向环Kd: 0       // 通常不需要
```

## 🔧 实时参数调试工具

### 1. 串口调试命令
在main.c中添加串口命令解析：

```c
void Parse_Debug_Command(char* cmd) {
    float kp, ki, kd;
    
    if (sscanf(cmd, "ap %f", &kp) == 1) {
        // 设置角度环Kp
        PID_SetParams(&balance_controller.angle_pid, kp, 0, 
                     balance_controller.angle_pid.Kd);
        printf("角度环Kp设置为: %.1f\r\n", kp);
    }
    else if (sscanf(cmd, "ad %f", &kd) == 1) {
        // 设置角度环Kd
        PID_SetParams(&balance_controller.angle_pid, 
                     balance_controller.angle_pid.Kp, 0, kd);
        printf("角度环Kd设置为: %.1f\r\n", kd);
    }
    else if (sscanf(cmd, "sp %f", &kp) == 1) {
        // 设置速度环Kp
        PID_SetParams(&balance_controller.speed_pid, kp, 
                     balance_controller.speed_pid.Ki, 0);
        printf("速度环Kp设置为: %.1f\r\n", kp);
    }
    else if (sscanf(cmd, "si %f", &ki) == 1) {
        // 设置速度环Ki
        PID_SetParams(&balance_controller.speed_pid, 
                     balance_controller.speed_pid.Kp, ki, 0);
        printf("速度环Ki设置为: %.3f\r\n", ki);
    }
    else if (strcmp(cmd, "help") == 0) {
        printf("调试命令:\r\n");
        printf("ap <value> - 设置角度环Kp\r\n");
        printf("ad <value> - 设置角度环Kd\r\n");
        printf("sp <value> - 设置速度环Kp\r\n");
        printf("si <value> - 设置速度环Ki\r\n");
        printf("start - 启动平衡控制\r\n");
        printf("stop - 停止平衡控制\r\n");
    }
    else if (strcmp(cmd, "start") == 0) {
        Balance_Enable(&balance_controller, 1);
        system_enabled = 1;
        printf("平衡控制已启动\r\n");
    }
    else if (strcmp(cmd, "stop") == 0) {
        Balance_Enable(&balance_controller, 0);
        system_enabled = 0;
        printf("平衡控制已停止\r\n");
    }
}
```

### 2. OLED实时显示
```c
void Display_PID_Params(void) {
    char buffer[32];
    
    ssd1306_SetCursor(0, 0);
    snprintf(buffer, sizeof(buffer), "Angle P:%.0f D:%.1f", 
             balance_controller.angle_pid.Kp,
             balance_controller.angle_pid.Kd);
    ssd1306_WriteString(buffer, Font_7x10, White);
    
    ssd1306_SetCursor(0, 10);
    snprintf(buffer, sizeof(buffer), "Speed P:%.1f I:%.2f", 
             balance_controller.speed_pid.Kp,
             balance_controller.speed_pid.Ki);
    ssd1306_WriteString(buffer, Font_7x10, White);
}
```

## 📊 调试数据记录表

### 角度环调试记录
| Kp值 | Kd值 | 现象描述 | 稳定性(1-5) | 响应速度(1-5) | 备注 |
|------|------|----------|-------------|---------------|------|
| 30   | 0    | 反应慢   | 2           | 2             |      |
| 50   | 0    | 轻微振荡 | 3           | 3             |      |
| 80   | 1    | 较稳定   | 4           | 4             | ✓    |
| 100  | 1    | 振荡加剧 | 2           | 4             |      |

### 速度环调试记录
| Kp值 | Ki值 | 现象描述 | 位置精度 | 超调程度 | 备注 |
|------|------|----------|----------|----------|------|
| 0.5  | 0    | 移动慢   | 差       | 无       |      |
| 0.8  | 0.05 | 较好     | 好       | 轻微     | ✓    |
| 1.2  | 0.1  | 超调     | 好       | 明显     |      |

## ⚠️ 常见问题及解决方案

### 1. 车辆无法站立
**可能原因**：
- 重心过高或过低
- 角度环Kp太小
- 传感器安装方向错误
- 电机方向设置错误

**解决方案**：
- 调整重心位置
- 增加角度环Kp
- 检查传感器轴向
- 测试电机正反转

### 2. 车辆剧烈振荡
**可能原因**：
- 角度环Kp太大
- 角度环Kd太小
- 控制频率太低
- 机械结构松动

**解决方案**：
- 减小角度环Kp
- 增加角度环Kd
- 提高控制频率到100Hz以上
- 检查机械连接

### 3. 车辆倾向一边
**可能原因**：
- 车轮直径不一致
- 电机功率不匹配
- 重心偏移
- 传感器零点偏移

**解决方案**：
- 更换相同规格车轮
- 软件补偿电机差异
- 调整重心位置
- 重新校准传感器

### 4. 车辆不能回到原位
**可能原因**：
- 速度环参数不当
- 缺少积分项
- 速度反馈不准确

**解决方案**：
- 调整速度环Kp
- 增加适当的Ki
- 改进速度估算算法

## 🎯 最终参数示例

### 典型小型平衡车参数
```c
// 车重: 1-2kg, 轮径: 6-8cm
角度环Kp: 80.0
角度环Ki: 0.0
角度环Kd: 3.0

速度环Kp: 0.8
速度环Ki: 0.1
速度环Kd: 0.0

转向环Kp: 2.5
转向环Ki: 0.0
转向环Kd: 0.0
```

### 典型中型平衡车参数
```c
// 车重: 3-5kg, 轮径: 10-15cm
角度环Kp: 120.0
角度环Ki: 0.0
角度环Kd: 4.0

速度环Kp: 1.2
速度环Ki: 0.15
速度环Kd: 0.0

转向环Kp: 3.5
转向环Ki: 0.0
转向环Kd: 0.0
```

## 📝 调试技巧总结

1. **循序渐进**：一次只调一个参数，观察效果
2. **安全第一**：始终保持角度保护和紧急停止
3. **记录数据**：详细记录每次调试的参数和效果
4. **耐心细致**：PID调试需要时间，不要急于求成
5. **理解原理**：了解每个参数的作用机制
6. **实际测试**：在实际使用环境中验证参数

通过系统的调试，您的平衡车将能够实现稳定的自平衡控制！