# STM32 F411CEU6 + MPU6050 + 卡尔曼滤波姿态检测系统

## 📋 项目概述

本项目是基于STM32 F411CEU6微控制器的六轴姿态检测系统，集成了MPU6050传感器和卡尔曼滤波算法，能够实时计算并显示设备的俯仰角（Pitch）和横滚角（Roll）。

### ✨ 主要特性

- 🎯 **高精度姿态检测**：使用MPU6050六轴传感器获取加速度和陀螺仪数据
- 🔧 **卡尔曼滤波算法**：有效融合加速度计和陀螺仪数据，提高角度计算精度
- 📺 **实时显示**：通过SSD1306 OLED屏幕实时显示姿态角度
- 📡 **串口输出**：支持通过UART输出详细的传感器数据
- ⚡ **高性能**：100MHz主频，200ms更新周期，满足实时性要求

### 🛠️ 硬件配置

| 组件 | 型号 | 接口 | 功能 |
|------|------|------|------|
| 主控制器 | STM32F411CEU6 | - | 系统核心处理器 |
| 姿态传感器 | MPU6050 | I2C1 | 六轴惯性测量单元 |
| 显示屏 | SSD1306 OLED | I2C2 | 128x64像素显示屏 |
| 调试接口 | UART1 | 115200bps | 数据输出和调试 |

### 📊 技术规格

- **处理器频率**：100MHz (HSI + PLL)
- **I2C速率**：400kHz (MPU6050), 100kHz (SSD1306)
- **采样频率**：1kHz (MPU6050内部采样)
- **更新频率**：5Hz (主循环200ms周期)
- **角度精度**：±0.1° (卡尔曼滤波后)
- **响应时间**：<50ms

## 🚀 快速开始

### 环境要求

- **开发环境**：STM32CubeIDE 1.8.0+
- **编译器**：ARM GCC
- **调试器**：ST-Link V2/V3
- **目标芯片**：STM32F411CEU6

### 硬件连接

```
STM32F411CEU6 引脚连接：

MPU6050 (I2C1):
├── VCC  → 3.3V
├── GND  → GND  
├── SCL  → PB6 (I2C1_SCL)
└── SDA  → PB7 (I2C1_SDA)

SSD1306 (I2C2):
├── VCC  → 3.3V
├── GND  → GND
├── SCL  → PB10 (I2C2_SCL)
└── SDA  → PB3 (I2C2_SDA)

UART1 (调试输出):
├── TX   → PA9 (USART1_TX)
└── RX   → PA10 (USART1_RX)
```

### 编译和烧录

1. **克隆项目**
```bash
git clone <repository-url>
cd F411CEU6_MPU6050_KML
```

2. **导入STM32CubeIDE**
   - File → Import → Existing Projects into Workspace
   - 选择项目根目录

3. **编译项目**
   - Project → Build Project
   - 或使用快捷键 Ctrl+B

4. **烧录程序**
   - Run → Debug As → STM32 MCU C/C++ Application
   - 或使用快捷键 F11

### 使用说明

1. **系统启动**：上电后系统自动初始化，OLED显示屏显示启动信息
2. **传感器校准**：保持设备静止3-5秒，系统自动完成初始校准
3. **姿态显示**：
   - OLED屏幕显示：Pitch（俯仰角）和Roll（横滚角）
   - 串口输出：完整的传感器数据和滤波结果
4. **数据格式**：
   ```
   Ax:0.12 Ay:-0.05 Az:0.98 | Gx:1.23 Gy:-0.45 Gz:0.67 | T:25.4 | KX:2.3 KY:-1.8
   ```

## 📁 项目结构

```
F411CEU6_MPU6050_KML/
├── Core/                          # 核心应用代码
│   ├── Inc/                       # 头文件
│   │   ├── main.h                 # 主程序头文件
│   │   ├── mpu6050.h              # MPU6050驱动接口
│   │   └── stm32f4xx_hal_conf.h   # HAL库配置
│   └── Src/                       # 源文件
│       ├── main.c                 # 主程序实现
│       ├── mpu6050.c              # MPU6050驱动实现
│       └── stm32f4xx_hal_msp.c    # HAL MSP配置
├── Drivers/                       # 外设驱动
│   ├── STM32F4xx_HAL_Driver/      # STM32 HAL库
│   ├── CMSIS/                     # CMSIS库
│   └── ssd1306/                   # SSD1306显示驱动
│       ├── ssd1306.h/.c           # 显示驱动核心
│       ├── ssd1306_fonts.h/.c     # 字体库
│       └── ssd1306_conf.h         # 显示配置
├── KalmanFilter/                  # 卡尔曼滤波算法
│   ├── Kalman.h                   # 滤波器接口
│   └── Kalman.c                   # 滤波器实现
├── Debug/                         # 调试输出文件
├── F411CEU6_MPU6050_KML.ioc      # STM32CubeMX配置
├── README.md                      # 项目说明文档
└── ProjectGuide.md               # 开发指南
```

## 🔧 核心模块说明

### MPU6050驱动模块

**文件**：`Core/Src/mpu6050.c`, `Core/Inc/mpu6050.h`

**主要功能**：
- 传感器初始化和配置
- 加速度计数据读取（±2g量程）
- 陀螺仪数据读取（±250°/s量程）
- 温度数据读取
- 数据格式转换和校准

**关键函数**：
```c
uint8_t MPU6050_Init(I2C_HandleTypeDef *I2Cx);           // 传感器初始化
void MPU6050_Read_All(I2C_HandleTypeDef *I2Cx, MPU6050_t *DataStruct);  // 读取所有数据
```

### 卡尔曼滤波模块

**文件**：`KalmanFilter/Kalman.c`, `KalmanFilter/Kalman.h`

**算法特点**：
- 一维卡尔曼滤波器实现
- 融合加速度计和陀螺仪数据
- 自适应噪声参数调整
- 实时角度估计和偏差校正

**滤波参数**：
```c
Q_angle = 0.001f;    // 角度过程噪声方差
Q_bias = 0.003f;     // 偏差过程噪声方差  
R_measure = 0.03f;   // 测量噪声方差
```

### 显示管理模块

**文件**：`Drivers/ssd1306/`

**显示功能**：
- 128x64像素OLED显示
- 多种字体支持（7x10, 11x18等）
- 实时角度数值显示
- 图形绘制功能（可扩展）

## ⚙️ 配置参数

### 系统配置

| 参数 | 数值 | 说明 |
|------|------|------|
| 系统时钟 | 100MHz | HSI + PLL配置 |
| 主循环周期 | 200ms | 数据更新和显示刷新 |
| I2C1时钟 | 400kHz | MPU6050通信速率 |
| I2C2时钟 | 100kHz | SSD1306通信速率 |
| UART波特率 | 115200 | 调试数据输出 |

### MPU6050配置

| 参数 | 设置值 | 寄存器 |
|------|--------|--------|
| 采样率 | 1kHz | SMPLRT_DIV = 0x07 |
| 加速度量程 | ±2g | ACCEL_CONFIG = 0x00 |
| 陀螺仪量程 | ±250°/s | GYRO_CONFIG = 0x00 |
| 数字低通滤波 | 关闭 | CONFIG = 0x00 |

## 🐛 故障排查

### 常见问题

1. **MPU6050初始化失败**
   - 检查I2C连接线路
   - 确认设备地址（0x68）
   - 检查上拉电阻（4.7kΩ）

2. **显示屏无显示**
   - 检查SSD1306 I2C地址（0x3C）
   - 确认电源供电（3.3V）
   - 检查I2C2配置

3. **角度数据异常**
   - 重新校准传感器
   - 检查卡尔曼滤波参数
   - 确认传感器安装方向

4. **串口无输出**
   - 检查UART配置（115200, 8N1）
   - 确认printf重定向实现
   - 检查连接线路

### 调试方法

1. **使用串口监控**：
   ```bash
   # 串口助手设置
   波特率：115200
   数据位：8
   停止位：1
   校验位：无
   ```

2. **LED指示**：
   - 可添加LED指示系统状态
   - 初始化成功/失败指示
   - 数据更新指示

3. **断点调试**：
   - 在关键函数设置断点
   - 监控变量数值变化
   - 检查函数返回值
## 🔬 技术细节

### 卡尔曼滤波算法原理

卡尔曼滤波器通过以下步骤融合传感器数据：

1. **预测步骤**：
   ```c
   // 基于陀螺仪数据预测角度
   angle_pred = angle_prev + (gyro_rate - bias) * dt;
   ```

2. **更新步骤**：
   ```c
   // 使用加速度计数据校正预测值
   angle_accel = atan2(accel_y, accel_z) * RAD_TO_DEG;
   angle_filtered = kalman_filter(angle_pred, angle_accel);
   ```

3. **噪声模型**：
   - **Q_angle**：角度过程噪声，反映陀螺仪积分误差
   - **Q_bias**：偏差过程噪声，反映陀螺仪零点漂移
   - **R_measure**：测量噪声，反映加速度计测量精度

### 坐标系定义

```
设备坐标系（右手坐标系）：
     Y
     ↑
     |
     |
     └────→ X
    /
   /
  ↙
 Z

角度定义：
- Roll (横滚角)：绕X轴旋转，范围 ±180°
- Pitch (俯仰角)：绕Y轴旋转，范围 ±90°
- Yaw (偏航角)：绕Z轴旋转（本项目未实现）
```

### 性能优化

1. **I2C通信优化**：
   - 使用连续读取模式减少I2C事务
   - 14字节连续读取获取所有传感器数据
   - 400kHz高速I2C提高数据传输效率

2. **计算优化**：
   - 使用查找表优化三角函数计算
   - 减少浮点运算，提高实时性
   - 合理的滤波参数平衡精度和响应速度

## 📈 扩展功能

### 可扩展特性

1. **数据记录**：
   - 添加SD卡模块记录姿态数据
   - 实现数据回放和分析功能

2. **无线通信**：
   - 集成WiFi/蓝牙模块
   - 实现远程数据监控

3. **多传感器融合**：
   - 添加磁力计实现三轴姿态检测
   - 集成气压计提供高度信息

4. **用户界面**：
   - 添加按键控制
   - 实现参数在线调整
   - 多页面显示功能

### 应用场景

- 🚁 **无人机姿态控制**：提供实时姿态反馈
- 🎮 **体感控制器**：游戏和VR应用
- 📱 **手机稳定器**：云台控制系统
- 🏗️ **工程测量**：倾斜角度监测
- 🚗 **车辆监控**：车身姿态检测

## 📚 参考资料

### 技术文档

- [STM32F411CEU6数据手册](https://www.st.com/resource/en/datasheet/stm32f411ce.pdf)
- [MPU6050寄存器映射](https://invensense.tdk.com/wp-content/uploads/2015/02/MPU-6000-Register-Map1.pdf)
- [SSD1306控制器手册](https://cdn-shop.adafruit.com/datasheets/SSD1306.pdf)
- [卡尔曼滤波理论](https://www.kalmanfilter.net/)

### 开源库

- [STM32 HAL库](https://github.com/STMicroelectronics/STM32CubeF4)
- [SSD1306驱动库](https://github.com/afiskon/stm32-ssd1306)
- [卡尔曼滤波器](https://github.com/TKJElectronics/KalmanFilter)

## 🤝 贡献指南

### 开发规范

请参考 `ProjectGuide.md` 中的开发规范：

- **代码风格**：遵循C语言编码规范
- **命名规范**：使用有意义的变量和函数名
- **注释标准**：关键逻辑必须有详细注释
- **测试要求**：新功能需要相应的测试用例

### 提交流程

1. Fork本项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建Pull Request

## 📄 许可证

本项目采用多种开源许可证：

- **主项目代码**：MIT License
- **卡尔曼滤波器**：GPL v2 License (来自TKJ Electronics)
- **SSD1306驱动**：MIT License
- **STM32 HAL库**：BSD 3-Clause License

详细信息请查看各模块的LICENSE文件。

## 📞 联系方式

- **项目维护者**：[您的姓名]
- **邮箱**：[<EMAIL>]
- **问题反馈**：[GitHub Issues](https://github.com/your-username/F411CEU6_MPU6050_KML/issues)

## 🔄 版本历史

### v1.0.0 (当前版本)
- ✅ 基础MPU6050驱动实现
- ✅ 卡尔曼滤波算法集成
- ✅ SSD1306显示功能
- ✅ UART调试输出
- ✅ 实时姿态角度计算

### 计划中的版本

#### v1.1.0
- 🔄 代码重构和优化
- 🔄 错误处理机制完善
- 🔄 配置参数化
- 🔄 性能优化

#### v1.2.0
- 🔄 多传感器融合
- 🔄 数据记录功能
- 🔄 用户界面改进
- 🔄 无线通信支持

---

**⭐ 如果这个项目对您有帮助，请给我们一个Star！**
typedef struct {
    float q0;      // w
    float q1;      // x
    float q2;      // y
    float q3;      // z
} MPU6050_Quaternion_t;
​``` 2.2 运动检测结构体
```

typedef struct {
    uint8_t is_moving;          // 是否在运
    动
    uint8_t motion_type;         // 运动类
    型（静止、走路、跑步等）
    float motion_intensity;      // 运动强
    度
    uint32_t motion_duration;    // 运动持
    续时间（ms）
} MPU6050_Motion_t;

```
### 3. 使用说明 3.1 初始化配置
```

// 配置结构体初始化
MPU6050_Config_t mpu_config = {
    .max_init_retries = 3,
    .timeout_ms = 1000,
    .accel_threshold = 16.0f,  // ±16g
    .gyro_threshold = 2000.0f   // ±2000°/
    s
};

// 初始化带重试机制
uint8_t retry_count = 0;
uint8_t init_status;

do {
    init_status = MPU6050_Init(&hi2c1);
    retry_count++;
    if (init_status != MPU6050_OK) {
        HAL_Delay(100);
    }
} while (init_status != MPU6050_OK && 
retry_count < mpu_config.
max_init_retries);

``` 3.2 数据读取示例

```

// 在定时器中断中读取数据
if (mpu_data_ready) {
    mpu_data_ready = 0;
    MPU6050_Read_All(&hi2c1, &MPU6050);
    

    // 输出姿态数据
    printf("Euler: Roll=%.2f Pitch=%.2f 
    Yaw=%.2f | ", 
           MPU6050.euler.roll,
           MPU6050.euler.pitch,
           MPU6050.euler.yaw);
           
    printf("Quat: w=%.2f x=%.2f y=%.2f 
    z=%.2f | ", 
           MPU6050.quaternion.q0,
           MPU6050.quaternion.q1,
           MPU6050.quaternion.q2,
           MPU6050.quaternion.q3);
           
    // 输出运动状态
    if (MPU6050.motion.is_moving) {
        printf("Motion: Type=%d 
        Intensity=%.2f Duration=%lu\r\n",
               MPU6050.motion.motion_type,
               MPU6050.motion.
               motion_intensity,
               MPU6050.motion.
               motion_duration);
    } else {
        printf("Motion: Static\r\n");
    }

这是一个基于STM32 HAL库的MPU6050应用项目，集成了卡尔曼滤波算法来提高姿态角度测量的精度。让我为您详细介绍项目的功能和结构：

### 项目功能

1. 基本功能：

   - 通过I2C接口读取MPU6050传感器的加速度和陀螺仪数据
   - 计算温度值
   - 使用卡尔曼滤波算法计算出更精确的姿态角度（横滚角和俯仰角）
2. 数据处理：

   - 原始数据获取：加速度（X、Y、Z轴）和陀螺仪（X、Y、Z轴）的原始值
   - 数据转换：将原始数据转换为实际的物理量
   - 姿态解算：通过卡尔曼滤波算法融合加速度计和陀螺仪数据，得到更准确的姿态角度

### 项目结构

1. 核心组件：

   - mpu6050.h/c ：MPU6050传感器的驱动程序
   - Kalman.h/c ：卡尔曼滤波器的实现
2. 数据结构：

   - MPU6050_t ：存储传感器数据的结构体，包含：
     - 原始加速度和陀螺仪数据
     - 转换后的实际值
     - 温度值
     - 经过卡尔曼滤波后的角度值
3. 卡尔曼滤波器：

   - 提供了可调节的参数（Q_angle、Q_bias、R_measure）用于优化滤波效果
   - 实现了预测和校正两个步骤的滤波算法

### 使用方法

1. 硬件配置：

   - 使用STM32CubeMX配置I2C接口
   - 将MPU6050模块连接到相应的I2C1引脚

2. 软件初始化：

   ```
   MPU6050_t MPU6050;  // 创建MPU6050结构体
   实例
   while (MPU6050_Init(&hi2c1) == 1);  // 
   初始化MPU6050
   ```

3. 数据读取：

   ```
   while (1) {
       MPU6050_Read_All(&hi2c1, &
       MPU6050);  // 读取所有数据
       HAL_Delay(100);  // 延时100ms
   }
   ```

   这个项目的特点是结合了硬件驱动和高级算法处理，通过卡尔曼滤波器提高了姿态测量的精度，适合用于需要准确姿态测量的应用场景，如平衡车、飞行器等。

请审核此项目是否有不当的地方，提出优化建议


经过审核，项目存在以下几个需要优化的地方：

### 1. 代码安全性和稳定性

1. 错误处理不足：

   - 这种循环方式可能导致系统死锁
   - 建议添加超时机制和错误处理
   - 可以改进为：设置最大重试次数，超过后返回错误状态
2. 数据有效性验证：

   - 缺少对MPU6050读取数据的有效性检查
   - 建议添加数据范围检查和异常值过滤

### 2. 性能优化

1. 采样时间：

   - 固定的延时可能不适合所有应用场景
   - 建议将采样率设置为可配置参数
   - 考虑使用定时器中断来实现更精确的采样
2. 数据处理效率：

   - 卡尔曼滤波的计算在主循环中执行，可能影响系统响应性
   - 建议考虑使用DMA进行数据传输
   - 可以使用RTOS来优化任务调度

### 3. 功能完善

1. 配置灵活性：

   - 卡尔曼滤波器参数（Q_angle、Q_bias、R_measure）是固定的
   - 建议提供动态调整参数的接口
   - 可以添加参数自适应机制
2. 数据输出：

   - 当前只通过串口输出数据
   - 建议添加数据存储功能
   - 可以考虑添加其他通信接口（如SPI、USB等）

### 4. 代码结构

1. 模块化：

   - 建议将卡尔曼滤波器代码完全独立为一个模块
   - 添加更多注释说明滤波器的原理和参数含义
2. 配置管理：

   - 建议将配置参数集中管理
   - 可以添加配置文件支持

### 5. 调试功能

1. 调试接口：

   - 建议添加调试模式
   - 提供数据可视化接口
   - 添加自检功能
2. 错误日志：

   - 添加错误日志记录功能
   - 提供系统状态监控

### 6. 文档完善

1. 代码文档：

   - 需要更详细的API文档
   - 添加使用示例
   - 完善参数配置说明
2. 测试文档：

   - 添加测试用例
   - 提供性能测试报告
   - 记录已知问题和解决方案
     这些优化建议可以根据实际应用场景的需求选择性实施，以提高系统的可靠性、性能和可维护性。