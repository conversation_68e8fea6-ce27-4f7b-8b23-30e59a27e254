# STM32F411 + MPU6050 两轮平衡车完整解决方案

## 🎯 项目概述

这是一个基于STM32F411CEU6微控制器和MPU6050陀螺仪的两轮平衡车完整解决方案，实现了从基础平衡控制到高级智能功能的全套系统。

### ✨ 核心特性

- **🔄 双控制系统**: 电子陀螺仪平衡控制 + 十字摇杆运动控制
- **🎛️ 三环PID控制**: 角度环、速度环、转向环精确控制
- **🔧 ODrive 3.6集成**: 高精度电机控制板支持
- **🤖 自动调优**: 智能PID参数优化
- **🛤️ 路径跟踪**: 自主导航功能
- **🧠 自适应控制**: 环境自适应算法
- **📡 遥控功能**: 无线控制支持
- **📊 实时监控**: 完整的状态显示和数据记录

## 🏗️ 系统架构

### 硬件配置
```
STM32F411CEU6 (主控制器)
├── MPU6050 (陀螺仪/加速度计) - I2C1
│   ├── SCL → PB6
│   └── SDA → PB7
├── ODrive 3.6 (电机控制板) - UART2 ⭐ 主要控制
│   ├── GPIO1(RX) ← PA2 (UART2_TX)
│   └── GPIO2(TX) → PA3 (UART2_RX)
├── SSD1306 OLED (显示屏) - I2C2
│   ├── SCL → PB10
│   └── SDA → PB3
├── 蓝牙模块 (手机APP通信) - UART1
│   ├── TX → PA10 (UART1_RX)
│   └── RX ← PA9 (UART1_TX)
├── 摇杆模块 (手动控制)
│   ├── X轴 → PA0 (ADC1_CH0)
│   ├── Y轴 → PA1 (ADC1_CH1)
│   └── 按键 → PA4 (GPIO_Input)
├── 超声波传感器 (自动跟随)
│   ├── Trig ← PA5 (GPIO_Output)
│   └── Echo → PA6 (GPIO_Input)
└── 调试接口
    ├── SWDIO → PA13
    └── SWCLK → PA14
```

### 软件模块
```
平衡车系统 (简化架构)
├── 主控制系统
│   └── balance_car_main (简化主控制器) ⭐ 核心
├── 核心控制模块
│   ├── balance_controller (平衡控制器)
│   ├── motion_controller (运动控制器)
│   └── joystick_driver (摇杆驱动)
├── 智能功能模块
│   ├── auto_follow (自动跟随) ⭐ 新增
│   └── mobile_app (手机APP接口) ⭐ 新增
├── 硬件驱动层
│   ├── mpu6050 (陀螺仪驱动)
│   ├── odrive_driver (ODrive驱动)
│   └── ssd1306 (显示驱动)
└── 支持模块
    ├── error_handler (错误处理)
    └── app_config (配置管理)
```

## 🚀 快速开始

### 1. 硬件连接
按照上述硬件配置图连接所有组件。

### 2. 编译和烧录
```bash
# 使用STM32CubeIDE或Keil MDK编译项目
# 烧录到STM32F411CEU6开发板
```

### 3. 基础使用 (简化版)
```c
#include "balance_car_main.h"

int main(void) {
    // 系统初始化
    HAL_Init();
    SystemClock_Config();

    // 创建主控制系统
    balance_car_main_t car;
    balance_car_config_t config;

    // 获取默认配置
    BalanceCarMain_GetDefaultConfig(&config);

    // 初始化平衡车系统
    BalanceCarMain_Init(&car);
    BalanceCarMain_SetConfig(&car, &config);

    // 启动平衡车
    BalanceCarMain_Start(&car);

    // 设置为手动控制模式
    BalanceCarMain_SetMode(&car, BALANCE_CAR_MODE_MANUAL);

    // 主循环
    while (1) {
        BalanceCarMain_Update(&car);
        HAL_Delay(5); // 200Hz更新频率
    }
}
```

## 📋 功能模块详解

### 🎯 基础平衡控制
- **三环PID控制算法**: 角度环(平衡) + 速度环(前后) + 转向环(左右)
- **MPU6050数据融合**: 陀螺仪和加速度计数据融合
- **实时平衡维持**: 200Hz高频控制循环

### 🕹️ 摇杆运动控制
- **双轴摇杆输入**: X轴控制转向，Y轴控制前后运动
- **平滑滤波处理**: 消除摇杆抖动和噪声
- **死区处理**: 避免微小摇杆移动造成的误动作

### ⚙️ 自动参数调优
- **智能PID优化**: 自动寻找最佳PID参数
- **多种调优模式**: 手动、自动平衡、自动运动、安全测试
- **性能评估**: 稳定性、响应性、综合评分

### 🛤️ 路径跟踪功能
- **多种路径类型**: 直线、方形、圆形、8字形路径
- **前瞻控制算法**: 基于前瞻距离的路径跟踪
- **位置估计**: 基于里程计的位置估算

### 🧠 自适应控制
- **环境参数估计**: 地面摩擦、负载、电池电压
- **动态参数调整**: 根据环境变化自动调整控制参数
- **学习模式**: 基于历史性能数据的参数优化

### 📡 遥控功能
- **命令队列管理**: 支持多命令缓存和优先级处理
- **超时保护**: 通信中断自动停止
- **多种控制命令**: 前进、后退、转向、紧急停止等

## 🎮 操作指南

### 按键控制
- **1/2**: 切换演示模式
- **3**: 暂停/恢复演示
- **4**: 停止演示
- **5**: 紧急停止
- **6**: 切换显示信息
- **7**: 开始自动调优
- **8**: 系统重置

### 演示模式
1. **基础平衡演示**: 纯平衡控制，测试稳定性
2. **摇杆控制演示**: 手动摇杆控制演示
3. **自动调优演示**: PID参数自动优化
4. **路径跟踪演示**: 自主路径跟踪
5. **自适应控制演示**: 环境自适应控制
6. **遥控演示**: 无线遥控功能
7. **综合演示**: 所有功能综合展示

## 📊 性能参数

### 控制性能
- **平衡精度**: ±2°以内
- **响应时间**: <1秒稳定时间
- **控制频率**: 200Hz
- **最大倾斜角**: ±15°运动控制

### 运动性能
- **最大速度**: 可调节，建议20cm/s
- **转向速度**: 可调节，建议5°/s
- **路径精度**: ±10cm位置容差
- **续航时间**: 取决于电池容量

## 🔧 参数配置

### PID参数推荐值
```c
// 角度环PID
angle_kp = 80.0f;
angle_ki = 0.0f;
angle_kd = 3.0f;

// 速度环PID
speed_kp = 0.8f;
speed_ki = 0.1f;
speed_kd = 0.0f;

// 转向环PID
turn_kp = 2.5f;
turn_ki = 0.0f;
turn_kd = 0.0f;
```

### 运动参数推荐值
```c
max_tilt_angle = 15.0f;      // 最大倾斜角度
max_turn_rate = 5.0f;        // 最大转向速率
acceleration_limit = 10.0f;  // 加速度限制
response_speed = 2.0f;       // 响应速度
```

## 🛠️ 开发指南

### 添加新功能
1. 在相应的头文件中声明新函数
2. 在源文件中实现功能
3. 在系统集成模块中注册新功能
4. 在演示应用中添加测试代码

### 调试技巧
- 使用串口输出调试信息
- 启用数据记录功能
- 使用示波器观察控制信号
- 分阶段测试各个模块

### 性能优化
- 调整控制频率以平衡性能和功耗
- 优化PID参数以获得最佳控制效果
- 使用自适应控制应对环境变化
- 定期进行参数调优
## 📈 项目特色

### 🎯 技术亮点
1. **模块化设计**: 清晰的模块分离，易于维护和扩展
2. **完整的控制算法**: 从基础PID到高级自适应控制
3. **智能调优**: 自动参数优化，无需手动调试
4. **多功能集成**: 平衡、运动、导航、遥控一体化
5. **实用的演示系统**: 完整的演示和测试框架

### 🔬 创新点
- **双控制融合**: 平衡控制与运动控制的无缝融合
- **自适应算法**: 环境感知和参数自动调整
- **ODrive集成**: 高精度电机控制板的完美适配
- **路径跟踪**: 基于前瞻控制的自主导航

## 📞 技术支持

如有问题或建议，请通过以下方式联系：
- 项目仓库: [GitHub链接]
- 技术交流群: [QQ群号]
- 邮箱: [联系邮箱]

## 📄 许可证

本项目采用MIT许可证，详见LICENSE文件。

---

**🎉 感谢使用STM32F411 + MPU6050两轮平衡车解决方案！**

*这个项目凝聚了多年的平衡车开发经验，希望能为您的学习和开发提供帮助。*