# STM32F411CEU6 平衡车项目 CubeMX配置指南

## 📋 项目概述

本指南详细说明如何使用STM32CubeMX配置STM32F411CEU6微控制器，用于两轮平衡车项目。

## 🎯 硬件连接总览

```
STM32F411CEU6 引脚分配：

传感器模块：
├── MPU6050 (陀螺仪)    - I2C1: PB6(SCL), PB7(SDA)
├── 超声波传感器        - GPIO: PA5(Trig), PA6(Echo)

显示模块：
└── SSD1306 OLED       - I2C2: PB10(SCL), PB9(SDA)

通信模块：
├── ODrive 3.6         - UART2: PA2(TX), PA3(RX)
└── 蓝牙模块           - UART1: PA9(TX), PA10(RX)

输入模块：
└── 摇杆模块           - ADC: PA0(X轴), PA1(Y轴), PA4(按键)

电源和调试：
├── 电源               - VCC, GND
└── ST-Link调试        - PA13(SWDIO), PA14(SWCLK)
```

## ⚙️ CubeMX详细配置步骤

### 1. 创建新项目

1. 打开STM32CubeMX
2. 选择 "New Project"
3. 搜索并选择 "STM32F411CEU6"
4. 点击 "Start Project"

### 2. 系统时钟配置

```
RCC配置 (修正为100MHz)：
├── HSE: Crystal/Ceramic Resonator (8MHz外部晶振)
├── PLL Source: HSE
├── PLLM: 8
├── PLLN: 400
├── PLLP: 4
└── System Clock: 100MHz
```

**配置步骤：**
1. Clock Configuration 标签页
2. 设置 HSE 为 8MHz
3. 启用 PLL，配置倍频参数：PLLM=8, PLLN=400, PLLP=4
4. 系统时钟设置为 100MHz

### 3. GPIO配置

#### 3.1 I2C引脚配置
```
I2C1 (MPU6050):
├── PB6: I2C1_SCL
└── PB7: I2C1_SDA

I2C2 (OLED) - 修正版本:
├── PB10: I2C2_SCL
└── PB9: I2C2_SDA (避免与SWO冲突)
```

#### 3.2 UART引脚配置
```
UART1 (蓝牙):
├── PA9: UART1_TX
└── PA10: UART1_RX

UART2 (ODrive):
├── PA2: UART2_TX
└── PA3: UART2_RX
```

#### 3.3 ADC引脚配置
```
ADC1 (摇杆):
├── PA0: ADC1_IN0 (X轴)
└── PA1: ADC1_IN1 (Y轴)
```

#### 3.4 GPIO输入输出
```
数字IO:
├── PA4: GPIO_Input (摇杆按键)
├── PA5: GPIO_Output (超声波Trig)
├── PA6: GPIO_Input (超声波Echo)
├── PA13: SYS_JTMS-SWDIO (调试)
└── PA14: SYS_JTCK-SWCLK (调试)
```

### 4. 外设详细配置

#### 4.1 I2C配置
```
I2C1 配置 (MPU6050):
├── Mode: I2C
├── Speed: Standard Mode (100kHz)
├── Clock No Stretch: Disable
├── General Call: Disable
└── 7-bit Address: Enable

I2C2 配置 (OLED):
├── Mode: I2C
├── Speed: Fast Mode (400kHz)
├── Clock No Stretch: Disable
├── General Call: Disable
└── 7-bit Address: Enable
```

#### 4.2 UART配置
```
UART1 配置 (蓝牙通信):
├── Mode: Asynchronous
├── Baud Rate: 115200
├── Word Length: 8 Bits
├── Parity: None
├── Stop Bits: 1
├── Data Direction: Receive and Transmit
└── Over Sampling: 16 Samples

UART2 配置 (ODrive通信):
├── Mode: Asynchronous
├── Baud Rate: 115200
├── Word Length: 8 Bits
├── Parity: None
├── Stop Bits: 1
├── Data Direction: Receive and Transmit
└── Over Sampling: 16 Samples
```

#### 4.3 ADC配置
```
ADC1 配置 (摇杆输入):
├── Mode: Independent mode
├── Clock Prescaler: PCLK2 divided by 2
├── Resolution: 12 bits
├── Data Alignment: Right alignment
├── Scan Conversion Mode: Enable
├── Continuous Conversion Mode: Enable
├── DMA Continuous Requests: Enable
└── Number of Conversions: 2

通道配置:
├── Channel 0 (PA0): Rank 1, Sampling Time 3 Cycles
└── Channel 1 (PA1): Rank 2, Sampling Time 3 Cycles
```

#### 4.4 定时器配置
```
TIM2 配置 (系统定时):
├── Mode: Internal Clock
├── Prescaler: 8399 (10kHz)
├── Counter Period: 9999 (1Hz)
├── Auto-reload preload: Enable
└── Trigger Event Selection: Reset

TIM3 配置 (超声波测距):
├── Mode: Internal Clock
├── Prescaler: 83 (1MHz)
├── Counter Period: 65535
└── Auto-reload preload: Enable
```

### 5. 中断和DMA配置

#### 5.1 中断优先级
```
中断优先级设置:
├── UART1 global interrupt: Priority 1
├── UART2 global interrupt: Priority 1
├── I2C1 event interrupt: Priority 2
├── I2C2 event interrupt: Priority 2
├── ADC global interrupt: Priority 3
├── TIM2 global interrupt: Priority 4
└── EXTI Line6 interrupt: Priority 5 (超声波Echo)
```

#### 5.2 DMA配置
```
DMA1 配置:
├── Stream 0: ADC1 → Memory (循环模式)
├── Stream 1: UART2_RX → Memory (循环模式)
├── Stream 6: UART2_TX ← Memory (正常模式)
└── Stream 7: UART1_TX ← Memory (正常模式)
```

### 6. 项目设置

#### 6.1 项目管理器设置
```
Project Settings:
├── Project Name: F411CEU6_MPU6050_BalanceCar
├── Project Location: 选择合适的目录
├── Toolchain/IDE: MDK-ARM V5
├── Firmware Package: Latest
├── Stack Size: 0x400
└── Heap Size: 0x200
```

#### 6.2 代码生成设置
```
Code Generator:
├── STM32Cube Firmware Library Package: Add necessary library files only
├── Generated files: 
│   ├── Generate peripheral initialization as a pair of '.c/.h' files per peripheral
│   ├── Keep User Code when re-generating
│   └── Delete previously generated files when not re-generated
└── HAL Settings:
    ├── Set all free pins as analog (to optimize power consumption)
    └── Enable Full Assert
```

## 🔧 验证配置

### 配置检查清单
- [ ] 系统时钟：100MHz
- [ ] I2C1：PB6/PB7，100kHz
- [ ] I2C2：PB10/PB3，400kHz  
- [ ] UART1：PA9/PA10，115200
- [ ] UART2：PA2/PA3，115200
- [ ] ADC1：PA0/PA1，12位分辨率
- [ ] GPIO：PA4输入，PA5输出，PA6输入
- [ ] 中断：所有外设中断已启用
- [ ] DMA：ADC和UART的DMA已配置

### 生成代码
1. 点击 "GENERATE CODE"
2. 选择 "Open Project" 
3. 在Keil MDK中打开项目
4. 编译验证无错误

## 📝 注意事项

### 重要提醒
1. **引脚冲突检查**：确保没有引脚功能冲突
2. **时钟配置**：验证所有外设时钟已正确启用
3. **中断优先级**：关键功能使用较高优先级
4. **DMA配置**：确保DMA通道没有冲突
5. **电源管理**：未使用的引脚设为模拟输入以降低功耗

### 常见问题
1. **I2C通信失败**：检查上拉电阻（4.7kΩ）
2. **UART数据丢失**：启用DMA和中断
3. **ADC读数不稳定**：增加采样时间
4. **系统复位**：检查看门狗配置

## 🚀 下一步

配置完成后，可以开始：
1. 添加MPU6050驱动代码
2. 集成ODrive通信协议
3. 实现平衡控制算法
4. 测试各个功能模块

---

**✅ 按照此配置指南，您将获得一个完整、可靠的STM32F411CEU6平衡车硬件平台！**
