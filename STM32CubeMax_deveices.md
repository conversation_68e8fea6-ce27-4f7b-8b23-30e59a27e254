好的，我已经阅读了《平衡车控制方案.md》文档。该文档详细描述了一个基于STM32F411和MPU6050，采用三环PID（角度、速度、转向）的平衡车控制系统。

结合文档内容和您的项目文件，我为您提炼出以下STM32CubeMX图形化配置方案。此方案旨在**精确匹配代码逻辑、节省MCU资源并降低配置错误风险**。

### STM32CubeMX优化配置建议

#### 1. **系统核心 (System Core)**

*   **`SYS`**:
    *   **Debug**: 设置为 `Serial Wire` (SWD)。这是进行调试和程序烧录的基础，必须开启。
    *   **Timebase Source**: 保持默认的 `SysTick`。控制代码中的`HAL_Delay()`和100Hz主循环都依赖于此。
*   **`Cortex_M4`**:
    *   **FPU**: **务必使能 (Enable)**。PID和卡尔曼滤波代码中包含大量浮点运算 (`float`)，开启硬件FPU可以极大提升计算效率，降低CPU负载。
*   **`RCC`**:
    *   **High Speed Clock (HSE)**: 推荐设置为 `Crystal/Ceramic Resonator`。使用外部晶振比内部RC振荡器更稳定，能为系统提供更精确的时钟，这对PID控制的定时精度至关重要。

#### 2. **定时器 (Timers)**

*   **`TIM2`** (用于电机PWM):
    *   **Channel 1 / Channel 2**: 分别设置为 `PWM Generation CH1` 和 `PWM Generation CH2`，用于控制左右电机。
    *   **参数配置 (Parameter Settings)**:
        *   **`Prescaler`**: 根据您的系统时钟进行计算，以产生一个中间时钟频率。
        *   **`Counter Period (Auto-Reload Register)`**: 设置为 `999`。
        *   **目标**: 将`Prescaler`和`Counter Period`组合配置，使最终PWM频率为 **1kHz**，分辨率为1000级，与`motor_driver.c`中的`pwm_frequency = 1000`和`pwm_resolution = 1000`的设定完全匹配。
        *   **示例**: 假设APB1定时器时钟为50MHz，则 `Prescaler` 设置为 `49`。 `(50,000,000 / (49+1)) / 1000 = 1kHz`。

#### 3. **通信接口 (Connectivity)**

*   **`I2C1`** (用于MPU6050):
    *   **Mode**: 设置为 `I2C`。
    *   **配置**: 使用标准模式 (`Standard Mode`, 100kHz) 即可满足MPU6050的数据更新率要求。
    *   **DMA配置**: **建议不使用DMA**。理由：MPU6050每次读取的数据量很小，使用轮询或中断方式足够高效，且能极大简化代码逻辑，避免复杂的DMA配置和中断处理，降低出错风险。
*   **`I2C2`** (用于OLED显示，若使用):
    *   **Mode**: 设置为 `I2C`，配置同I2C1。
*   **`USART1`** (用于调试信息输出):
    *   **Mode**: 设置为 `Asynchronous` (异步模式)。
    *   **参数配置**: `Baud Rate` 设置为 `115200`，8位数据，无校验，1位停止位。这是最常用的串行调试配置。

#### 4. **GPIO引脚配置**

*   以下引脚需要手动配置为 `GPIO_Output`:
    *   `PA2`, `PA3`: 左电机方向控制。
    *   `PA4`, `PA5`: 右电机方向控制。
    *   `PA6`: 电机驱动板使能。
*   **注意**: 其他如I2C、USART、TIM的引脚，在启用相应外设后，CubeMX会自动完成配置，无需手动设置。

#### 5. **时钟配置 (Clock Configuration)**

*   将 **HCLK** 的频率设置为 **100 MHz**。这是STM32F411CEU6的最高工作频率，确保PID控制算法有最充足的算力。CubeMX会自动解算并配置PLL（锁相环）和各总线分频器。

### 方案总结

| 外设        | 配置模式                  | 关键参数/说明                                                              | 目的                                       |
| :---------- | :------------------------ | :------------------------------------------------------------------------- | :----------------------------------------- |
| **SYS**     | `Serial Wire`             | 用于调试和烧录。                                                           | 基础功能                                   |
| **FPU**     | `Enabled`                 | **必须开启**，用于硬件浮点运算。                                           | 大幅提升PID和卡尔曼滤波性能。              |
| **TIM2**    | `PWM Generation CH1/CH2`  | 频率1kHz，分辨率1000。                                                     | 精确匹配代码中的电机驱动逻辑。             |
| **I2C1**    | `I2C`                     | 标准模式(100kHz)，**不使用DMA**。                                          | 简化设计，降低MPU6050驱动的复杂度。        |
| **USART1**  | `Asynchronous`            | 115200波特率。                                                             | 用于`printf`调试输出。                     |
| **GPIOs**   | `GPIO_Output`             | PA2, PA3, PA4, PA5, PA6。                                                  | 匹配代码中的电机方向和使能控制。           |
| **Clock**   | `HCLK = 100 MHz`          | 设置为芯片最高频率。                                                       | 保证控制算法的实时性和响应速度。           |

采纳以上配置，您可以在STM32CubeMX中生成一个既高效又与现有代码高度兼容的底层初始化框架，从而专注于核心控制算法的开发与调试。