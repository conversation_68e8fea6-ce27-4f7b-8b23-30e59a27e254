/**
 ******************************************************************************
 * @file           : balance_main_example.c
 * @brief          : 平衡车主程序集成示例
 * @version        : v1.0
 * @date           : 2025-01-27
 ******************************************************************************
 * @description    :
 * 这是一个完整的平衡车主程序示例，展示如何集成：
 * - MPU6050传感器读取
 * - 三环PID平衡控制
 * - 电机驱动控制
 * - OLED显示和串口调试
 * - 用户交互(按钮控制)
 * 
 * 使用方法：
 * 1. 将此文件内容复制到main.c中
 * 2. 在STM32CubeMX中配置TIM2用于PWM输出
 * 3. 配置相应的GPIO用于电机控制
 * 4. 编译下载到平衡车
 ******************************************************************************
 */

#include "main.h"
#include "mpu6050.h"
#include "ssd1306.h"
#include "balance_controller.h"
#include "motor_driver.h"
#include "app_config.h"
#include <stdio.h>
#include <string.h>

/* 硬件句柄 */
I2C_HandleTypeDef hi2c1, hi2c2;
UART_HandleTypeDef huart1;
TIM_HandleTypeDef htim2;  // 电机PWM定时器

/* 平衡车系统变量 */
balance_controller_t balance_controller;
motor_config_t motor_config;
mpu6050_data_t sensor_data;

/* 控制变量 */
uint8_t system_enabled = 0;
uint32_t last_control_time = 0;
uint32_t last_display_time = 0;

/* 函数声明 */
void SystemClock_Config(void);
static void MX_GPIO_Init(void);
static void MX_I2C1_Init(void);
static void MX_I2C2_Init(void);
static void MX_USART1_UART_Init(void);
static void MX_TIM2_Init(void);

void Balance_System_Init(void);
void Balance_Control_Loop(void);
void Display_Balance_Info(void);
void Handle_User_Input(void);
void Safety_Check(void);

/* printf重定向 */
int __io_putchar(int ch) {
    HAL_UART_Transmit(&huart1, (uint8_t *)&ch, 1, HAL_MAX_DELAY);
    return ch;
}

/**
 * @brief 主函数
 */
int main(void) {
    /* 系统初始化 */
    HAL_Init();
    SystemClock_Config();
    
    /* 外设初始化 */
    MX_GPIO_Init();
    MX_I2C1_Init();
    MX_I2C2_Init();
    MX_USART1_UART_Init();
    MX_TIM2_Init();
    
    /* 平衡车系统初始化 */
    Balance_System_Init();
    
    printf("\r\n=== 平衡车控制系统启动 ===\r\n");
    printf("版本: v1.0\r\n");
    printf("硬件: STM32F411CEU6 + MPU6050\r\n");
    printf("控制: 三环PID平衡算法\r\n");
    printf("按用户按钮启动平衡控制\r\n\r\n");
    
    /* 主循环 */
    while (1) {
        Handle_User_Input();    // 处理用户输入
        Balance_Control_Loop(); // 平衡控制循环
        Display_Balance_Info(); // 显示信息
        Safety_Check();         // 安全检查
        
        HAL_Delay(1); // 1ms基础延时
    }
}

/**
 * @brief 平衡车系统初始化
 */
void Balance_System_Init(void) {
    /* 初始化OLED显示 */
    ssd1306_Init();
    ssd1306_Fill(Black);
    ssd1306_SetCursor(0, 0);
    ssd1306_WriteString("Balance Car", Font_11x18, White);
    ssd1306_SetCursor(0, 20);
    ssd1306_WriteString("Initializing...", Font_7x10, White);
    ssd1306_UpdateScreen();
    
    /* 初始化MPU6050 */
    app_error_t mpu_result = mpu6050_init(&hi2c1);
    if (mpu_result != APP_OK) {
        printf("MPU6050初始化失败: %d\r\n", mpu_result);
        Error_Handler();
    }
    
    /* MPU6050自检 */
    mpu_result = mpu6050_self_test(&hi2c1);
    if (mpu_result != APP_OK) {
        printf("MPU6050自检失败: %d\r\n", mpu_result);
    }
    
    /* 配置电机驱动 */
    motor_config.htim = &htim2;
    motor_config.left_pwm_channel = TIM_CHANNEL_1;
    motor_config.right_pwm_channel = TIM_CHANNEL_2;
    
    // 电机方向控制GPIO (根据实际硬件连接修改)
    motor_config.left_dir1_port = GPIOA;
    motor_config.left_dir1_pin = GPIO_PIN_2;
    motor_config.left_dir2_port = GPIOA;
    motor_config.left_dir2_pin = GPIO_PIN_3;
    motor_config.right_dir1_port = GPIOA;
    motor_config.right_dir1_pin = GPIO_PIN_4;
    motor_config.right_dir2_port = GPIOA;
    motor_config.right_dir2_pin = GPIO_PIN_5;
    motor_config.enable_port = GPIOA;
    motor_config.enable_pin = GPIO_PIN_6;
    
    motor_config.driver_type = MOTOR_DRIVER_L298N;
    motor_config.pwm_frequency = 1000; // 1kHz PWM
    motor_config.pwm_resolution = 1000; // 1000级分辨率
    
    /* 初始化电机驱动 */
    HAL_StatusTypeDef motor_result = Motor_Init(&motor_config);
    if (motor_result != HAL_OK) {
        printf("电机驱动初始化失败\r\n");
        Error_Handler();
    }
    
    /* 电机自检 */
    printf("执行电机自检...\r\n");
    Motor_SelfTest();
    printf("电机自检完成\r\n");
    
    /* 设置电机接口 */
    motor_interface_t motor_interface;
    motor_interface.set_motor_speed = Motor_SetSpeed;
    motor_interface.enable_motors = Motor_Enable;
    motor_interface.get_motor_speed = Motor_GetSpeed;
    
    /* 初始化平衡控制器 */
    Balance_Init(&balance_controller, motor_interface);
    
    /* MPU6050校准 */
    printf("开始MPU6050校准，请保持设备静止...\r\n");
    ssd1306_Fill(Black);
    ssd1306_SetCursor(0, 0);
    ssd1306_WriteString("Calibrating", Font_11x18, White);
    ssd1306_SetCursor(0, 20);
    ssd1306_WriteString("Keep still...", Font_7x10, White);
    ssd1306_UpdateScreen();
    
    mpu_result = mpu6050_calibrate(&hi2c1);
    if (mpu_result == APP_OK) {
        printf("MPU6050校准完成\r\n");
        mpu6050_save_calibration();
    } else {
        printf("MPU6050校准失败，使用默认参数\r\n");
    }
    
    /* 初始化完成 */
    ssd1306_Fill(Black);
    ssd1306_SetCursor(0, 0);
    ssd1306_WriteString("Ready!", Font_11x18, White);
    ssd1306_SetCursor(0, 20);
    ssd1306_WriteString("Press button", Font_7x10, White);
    ssd1306_SetCursor(0, 30);
    ssd1306_WriteString("to start", Font_7x10, White);
    ssd1306_UpdateScreen();
    
    printf("平衡车系统初始化完成\r\n");
}

/**
 * @brief 平衡控制主循环
 */
void Balance_Control_Loop(void) {
    uint32_t current_time = HAL_GetTick();
    
    // 控制频率：100Hz (10ms)
    if (current_time - last_control_time >= 10) {
        float dt = (current_time - last_control_time) / 1000.0f;
        last_control_time = current_time;
        
        // 读取传感器数据
        app_error_t result = mpu6050_read_data(&hi2c1, &sensor_data);
        
        if (result == APP_OK) {
            // 执行平衡控制
            if (system_enabled) {
                Balance_Update(&balance_controller, &sensor_data, dt);
            }
            
            // 调试输出 (降低频率到10Hz)
            static uint32_t debug_counter = 0;
            if (++debug_counter >= 10) {
                debug_counter = 0;
                
                #if DEBUG_ENABLE
                int16_t left_motor, right_motor;
                float angle_out, speed_out;
                Balance_GetDebugInfo(&balance_controller, &left_motor, &right_motor, &angle_out, &speed_out);
                
                printf("Pitch:%+6.1f° Rate:%+6.1f°/s Motors:L=%d R=%d State:%d\r\n",
                       sensor_data.pitch, sensor_data.pitch_rate,
                       left_motor, right_motor, Balance_GetState(&balance_controller));
                #endif
            }
        } else {
            printf("传感器读取错误: %d\r\n", result);
            // 传感器错误时停止电机
            if (system_enabled) {
                Balance_Enable(&balance_controller, 0);
                system_enabled = 0;
            }
        }
    }
}

/**
 * @brief 显示平衡信息
 */
void Display_Balance_Info(void) {
    uint32_t current_time = HAL_GetTick();
    
    // 显示频率：5Hz (200ms)
    if (current_time - last_display_time >= 200) {
        last_display_time = current_time;
        
        ssd1306_Fill(Black);
        
        // 显示标题
        ssd1306_SetCursor(0, 0);
        if (system_enabled) {
            balance_state_t state = Balance_GetState(&balance_controller);
            switch (state) {
                case BALANCE_STATE_STANDBY:
                    ssd1306_WriteString("STANDBY", Font_11x18, White);
                    break;
                case BALANCE_STATE_BALANCING:
                    ssd1306_WriteString("BALANCING", Font_11x18, White);
                    break;
                case BALANCE_STATE_FALLEN:
                    ssd1306_WriteString("FALLEN", Font_11x18, White);
                    break;
                default:
                    ssd1306_WriteString("UNKNOWN", Font_11x18, White);
                    break;
            }
        } else {
            ssd1306_WriteString("DISABLED", Font_11x18, White);
        }
        
        // 显示角度信息
        char buffer[32];
        ssd1306_SetCursor(0, 20);
        snprintf(buffer, sizeof(buffer), "Pitch:%+6.1f", sensor_data.pitch);
        ssd1306_WriteString(buffer, Font_7x10, White);
        
        ssd1306_SetCursor(0, 30);
        snprintf(buffer, sizeof(buffer), "Rate: %+6.1f", sensor_data.pitch_rate);
        ssd1306_WriteString(buffer, Font_7x10, White);
        
        // 显示电机输出
        if (system_enabled) {
            int16_t left_motor, right_motor;
            Balance_GetDebugInfo(&balance_controller, &left_motor, &right_motor, NULL, NULL);
            
            ssd1306_SetCursor(0, 40);
            snprintf(buffer, sizeof(buffer), "L:%d R:%d", left_motor, right_motor);
            ssd1306_WriteString(buffer, Font_7x10, White);
        }
        
        // 显示温度
        ssd1306_SetCursor(0, 50);
        snprintf(buffer, sizeof(buffer), "Temp:%.1fC", sensor_data.temperature);
        ssd1306_WriteString(buffer, Font_7x10, White);
        
        ssd1306_UpdateScreen();
    }
}

/**
 * @brief 处理用户输入
 */
void Handle_User_Input(void) {
    // 检查用户按钮 (假设连接到PA0)
    static uint8_t button_pressed = 0;
    static uint32_t button_press_time = 0;
    
    if (HAL_GPIO_ReadPin(GPIOA, GPIO_PIN_0) == GPIO_PIN_SET) {
        if (!button_pressed) {
            button_pressed = 1;
            button_press_time = HAL_GetTick();
        } else {
            // 长按2秒切换系统状态
            if (HAL_GetTick() - button_press_time > 2000) {
                system_enabled = !system_enabled;
                
                if (system_enabled) {
                    printf("启动平衡控制\r\n");
                    Balance_Enable(&balance_controller, 1);
                } else {
                    printf("停止平衡控制\r\n");
                    Balance_Enable(&balance_controller, 0);
                }
                
                button_pressed = 0; // 重置按钮状态
            }
        }
    } else {
        button_pressed = 0;
    }
}

/**
 * @brief 安全检查
 */
void Safety_Check(void) {
    static uint32_t last_safety_check = 0;
    uint32_t current_time = HAL_GetTick();
    
    // 每100ms执行一次安全检查
    if (current_time - last_safety_check >= 100) {
        last_safety_check = current_time;
        
        // 检查传感器数据有效性
        if (fabs(sensor_data.pitch) > 60.0f || fabs(sensor_data.roll) > 60.0f) {
            if (system_enabled) {
                printf("安全保护：角度过大，停止控制\r\n");
                Balance_Enable(&balance_controller, 0);
                system_enabled = 0;
            }
        }
        
        // 检查温度
        if (sensor_data.temperature > 70.0f || sensor_data.temperature < -10.0f) {
            printf("警告：温度异常 %.1f°C\r\n", sensor_data.temperature);
        }
    }
}

/* STM32CubeMX生成的初始化函数 */
static void MX_TIM2_Init(void) {
    TIM_ClockConfigTypeDef sClockSourceConfig = {0};
    TIM_MasterConfigTypeDef sMasterConfig = {0};
    TIM_OC_InitTypeDef sConfigOC = {0};
    
    htim2.Instance = TIM2;
    htim2.Init.Prescaler = 99;  // 100MHz/100 = 1MHz
    htim2.Init.CounterMode = TIM_COUNTERMODE_UP;
    htim2.Init.Period = 999;    // 1MHz/1000 = 1kHz PWM
    htim2.Init.ClockDivision = TIM_CLOCKDIVISION_DIV1;
    htim2.Init.AutoReloadPreload = TIM_AUTORELOAD_PRELOAD_DISABLE;
    
    if (HAL_TIM_Base_Init(&htim2) != HAL_OK) {
        Error_Handler();
    }
    
    sClockSourceConfig.ClockSource = TIM_CLOCKSOURCE_INTERNAL;
    if (HAL_TIM_ConfigClockSource(&htim2, &sClockSourceConfig) != HAL_OK) {
        Error_Handler();
    }
    
    if (HAL_TIM_PWM_Init(&htim2) != HAL_OK) {
        Error_Handler();
    }
    
    sMasterConfig.MasterOutputTrigger = TIM_TRGO_RESET;
    sMasterConfig.MasterSlaveMode = TIM_MASTERSLAVEMODE_DISABLE;
    if (HAL_TIMEx_MasterConfigSynchronization(&htim2, &sMasterConfig) != HAL_OK) {
        Error_Handler();
    }
    
    sConfigOC.OCMode = TIM_OCMODE_PWM1;
    sConfigOC.Pulse = 0;
    sConfigOC.OCPolarity = TIM_OCPOLARITY_HIGH;
    sConfigOC.OCFastMode = TIM_OCFAST_DISABLE;
    
    if (HAL_TIM_PWM_ConfigChannel(&htim2, &sConfigOC, TIM_CHANNEL_1) != HAL_OK) {
        Error_Handler();
    }
    
    if (HAL_TIM_PWM_ConfigChannel(&htim2, &sConfigOC, TIM_CHANNEL_2) != HAL_OK) {
        Error_Handler();
    }
    
    HAL_TIM_MspPostInit(&htim2);
}

/* 其他初始化函数保持不变... */