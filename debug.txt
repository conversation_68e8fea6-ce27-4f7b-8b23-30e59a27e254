22:34:23 **** Build of configuration Debug for project F411CEU6_MPU6050_KML ****
make -j8 all 
arm-none-eabi-gcc "../KalmanFilter/Kalman.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"KalmanFilter/Kalman.d" -MT"KalmanFilter/Kalman.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "KalmanFilter/Kalman.o"
arm-none-eabi-gcc "../Drivers/ssd1306/ssd1306.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Drivers/ssd1306/ssd1306.d" -MT"Drivers/ssd1306/ssd1306.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Drivers/ssd1306/ssd1306.o"
arm-none-eabi-gcc "../Drivers/ssd1306/ssd1306_fonts.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Drivers/ssd1306/ssd1306_fonts.d" -MT"Drivers/ssd1306/ssd1306_fonts.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Drivers/ssd1306/ssd1306_fonts.o"
arm-none-eabi-gcc "../Drivers/ssd1306/ssd1306_tests.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Drivers/ssd1306/ssd1306_tests.d" -MT"Drivers/ssd1306/ssd1306_tests.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Drivers/ssd1306/ssd1306_tests.o"
arm-none-eabi-gcc "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.d" -MT"Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o"
arm-none-eabi-gcc "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.d" -MT"Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o"
arm-none-eabi-gcc "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.d" -MT"Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o"
arm-none-eabi-gcc "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.d" -MT"Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o"
arm-none-eabi-gcc "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.d" -MT"Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o"
arm-none-eabi-gcc "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.d" -MT"Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o"
arm-none-eabi-gcc "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.d" -MT"Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o"
arm-none-eabi-gcc "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.d" -MT"Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o"
arm-none-eabi-gcc "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.d" -MT"Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o"
arm-none-eabi-gcc "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.d" -MT"Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o"
arm-none-eabi-gcc "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.d" -MT"Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o"
arm-none-eabi-gcc "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c.d" -MT"Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c.o"
arm-none-eabi-gcc "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c_ex.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c_ex.d" -MT"Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c_ex.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c_ex.o"
arm-none-eabi-gcc "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.d" -MT"Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o"
arm-none-eabi-gcc "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.d" -MT"Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o"
arm-none-eabi-gcc "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.d" -MT"Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o"
arm-none-eabi-gcc "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.d" -MT"Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o"
arm-none-eabi-gcc "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.d" -MT"Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o"
arm-none-eabi-gcc "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.d" -MT"Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o"
arm-none-eabi-gcc "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.d" -MT"Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.o"
arm-none-eabi-gcc "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_ll_adc.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_ll_adc.d" -MT"Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_ll_adc.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_ll_adc.o"
arm-none-eabi-gcc -mcpu=cortex-m4 -g3 -DDEBUG -c -x assembler-with-cpp -MMD -MP -MF"Core/Startup/startup_stm32f411ceux.d" -MT"Core/Startup/startup_stm32f411ceux.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Core/Startup/startup_stm32f411ceux.o" "../Core/Startup/startup_stm32f411ceux.s"
arm-none-eabi-gcc "../Core/Src/auto_follow.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Core/Src/auto_follow.d" -MT"Core/Src/auto_follow.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Core/Src/auto_follow.o"
arm-none-eabi-gcc "../Core/Src/balance_car_advanced.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Core/Src/balance_car_advanced.d" -MT"Core/Src/balance_car_advanced.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Core/Src/balance_car_advanced.o"
arm-none-eabi-gcc "../Core/Src/balance_car_demo.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Core/Src/balance_car_demo.d" -MT"Core/Src/balance_car_demo.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Core/Src/balance_car_demo.o"
arm-none-eabi-gcc "../Core/Src/balance_car_main.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Core/Src/balance_car_main.d" -MT"Core/Src/balance_car_main.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Core/Src/balance_car_main.o"
arm-none-eabi-gcc "../Core/Src/balance_car_system.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Core/Src/balance_car_system.d" -MT"Core/Src/balance_car_system.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Core/Src/balance_car_system.o"
arm-none-eabi-gcc "../Core/Src/balance_car_tuning.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Core/Src/balance_car_tuning.d" -MT"Core/Src/balance_car_tuning.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Core/Src/balance_car_tuning.o"
../Core/Src/balance_car_main.c: In function 'BalanceCarMain_InitHardware':
../Core/Src/balance_car_main.c:378:21: warning: unused variable 'odrive_cfg' [-Wunused-variable]
  378 |     odrive_config_t odrive_cfg = {
      |                     ^~~~~~~~~~
arm-none-eabi-gcc "../Core/Src/balance_controller.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Core/Src/balance_controller.d" -MT"Core/Src/balance_controller.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Core/Src/balance_controller.o"
arm-none-eabi-gcc "../Core/Src/error_handler.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Core/Src/error_handler.d" -MT"Core/Src/error_handler.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Core/Src/error_handler.o"
arm-none-eabi-gcc "../Core/Src/joystick_driver.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Core/Src/joystick_driver.d" -MT"Core/Src/joystick_driver.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Core/Src/joystick_driver.o"
arm-none-eabi-gcc "../Core/Src/main.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Core/Src/main.d" -MT"Core/Src/main.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Core/Src/main.o"
arm-none-eabi-gcc "../Core/Src/mobile_app.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Core/Src/mobile_app.d" -MT"Core/Src/mobile_app.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Core/Src/mobile_app.o"
arm-none-eabi-gcc "../Core/Src/motion_controller.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Core/Src/motion_controller.d" -MT"Core/Src/motion_controller.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Core/Src/motion_controller.o"
arm-none-eabi-gcc "../Core/Src/motor_driver.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Core/Src/motor_driver.d" -MT"Core/Src/motor_driver.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Core/Src/motor_driver.o"
arm-none-eabi-gcc "../Core/Src/mpu6050.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Core/Src/mpu6050.d" -MT"Core/Src/mpu6050.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Core/Src/mpu6050.o"
../Core/Src/joystick_driver.c: In function 'Joystick_SelfTest':
../Core/Src/joystick_driver.c:244:13: warning: unused variable 'initial_button' [-Wunused-variable]
  244 |     uint8_t initial_button = test_data.button_pressed;
      |             ^~~~~~~~~~~~~~
arm-none-eabi-gcc "../Core/Src/odrive_driver.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Core/Src/odrive_driver.d" -MT"Core/Src/odrive_driver.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Core/Src/odrive_driver.o"
../Core/Src/main.c:121:13: warning: 'system_health_monitor' defined but not used [-Wunused-function]
  121 | static void system_health_monitor(void)
      |             ^~~~~~~~~~~~~~~~~~~~~
../Core/Src/mobile_app.c:54:16: error: conflicting types for 'MobileApp_CalculateChecksum'; have 'uint8_t(const uint8_t *, uint8_t)' {aka 'unsigned char(const unsigned char *, unsigned char)'}
   54 | static uint8_t MobileApp_CalculateChecksum(const uint8_t *data, uint8_t length);
      |                ^~~~~~~~~~~~~~~~~~~~~~~~~~~
In file included from ../Core/Src/mobile_app.c:13:
../Core/Inc/mobile_app.h:210:9: note: previous declaration of 'MobileApp_CalculateChecksum' with type 'uint8_t(uint8_t *, uint16_t)' {aka 'unsigned char(unsigned char *, short unsigned int)'}
  210 | uint8_t MobileApp_CalculateChecksum(uint8_t *data, uint16_t length);
      |         ^~~~~~~~~~~~~~~~~~~~~~~~~~~
../Core/Src/mobile_app.c:62:48: error: unknown type name 'mobile_app_config_t'; did you mean 'mobile_app_cmd_t'?
   62 | mobile_app_result_t MobileApp_GetDefaultConfig(mobile_app_config_t *config)
      |                                                ^~~~~~~~~~~~~~~~~~~
      |                                                mobile_app_cmd_t
../Core/Src/mobile_app.c:86:61: error: unknown type name 'mobile_app_config_t'
   86 | mobile_app_result_t MobileApp_Init(mobile_app_t *app, const mobile_app_config_t *config)
      |                                                             ^~~~~~~~~~~~~~~~~~~
../Core/Src/mobile_app.c:86:21: error: conflicting types for 'MobileApp_Init'; have 'mobile_app_result_t(mobile_app_t *, const int *)'
   86 | mobile_app_result_t MobileApp_Init(mobile_app_t *app, const mobile_app_config_t *config)
      |                     ^~~~~~~~~~~~~~
../Core/Inc/mobile_app.h:184:21: note: previous declaration of 'MobileApp_Init' with type 'mobile_app_result_t(mobile_app_t *, UART_HandleTypeDef *)' {aka 'mobile_app_result_t(mobile_app_t *, struct __UART_HandleTypeDef *)'}
  184 | mobile_app_result_t MobileApp_Init(mobile_app_t *app, UART_HandleTypeDef *uart);
      |                     ^~~~~~~~~~~~~~
../Core/Src/mobile_app.c: In function 'MobileApp_Init':
../Core/Src/mobile_app.c:96:16: error: 'mobile_app_t' has no member named 'config'
   96 |     memcpy(&app->config, config, sizeof(mobile_app_config_t));
      |                ^~
../Core/Src/motion_controller.c: In function 'Motion_GetStatistics':
../Core/Src/motion_controller.c:425:5: warning: implicit declaration of function 'snprintf' [-Wimplicit-function-declaration]
  425 |     snprintf(info_str, max_len,
      |     ^~~~~~~~
../Core/Src/motion_controller.c:21:1: note: include '<stdio.h>' or provide a declaration of 'snprintf'
   20 | #include <string.h>
  +++ |+#include <stdio.h>
   21 | 
../Core/Src/motion_controller.c:425:5: warning: incompatible implicit declaration of built-in function 'snprintf' [-Wbuiltin-declaration-mismatch]
  425 |     snprintf(info_str, max_len,
      |     ^~~~~~~~
../Core/Src/motion_controller.c:425:5: note: include '<stdio.h>' or provide a declaration of 'snprintf'
../Core/Src/motion_controller.c: In function 'apply_deadzone_and_scale':
../Core/Src/motion_controller.c:454:9: warning: implicit declaration of function 'abs' [-Wimplicit-function-declaration]
  454 |     if (abs(input) < deadzone) {
      |         ^~~
../Core/Src/motion_controller.c:21:1: note: include '<stdlib.h>' or provide a declaration of 'abs'
   20 | #include <string.h>
  +++ |+#include <stdlib.h>
   21 | 
../Core/Src/mobile_app.c:96:41: error: 'mobile_app_config_t' undeclared (first use in this function); did you mean 'mobile_app_cmd_t'?
   96 |     memcpy(&app->config, config, sizeof(mobile_app_config_t));
      |                                         ^~~~~~~~~~~~~~~~~~~
      |                                         mobile_app_cmd_t
../Core/Src/mobile_app.c:96:41: note: each undeclared identifier is reported only once for each function it appears in
../Core/Src/mobile_app.c:99:10: error: 'mobile_app_t' has no member named 'state'; did you mean 'rx_state'?
   99 |     app->state = MOBILE_APP_STATE_DISCONNECTED;
      |          ^~~~~
      |          rx_state
arm-none-eabi-gcc "../Core/Src/pid_controller.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Core/Src/pid_controller.d" -MT"Core/Src/pid_controller.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Core/Src/pid_controller.o"
../Core/Src/mobile_app.c:99:18: error: 'MOBILE_APP_STATE_DISCONNECTED' undeclared (first use in this function)
   99 |     app->state = MOBILE_APP_STATE_DISCONNECTED;
      |                  ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~
../Core/Src/mobile_app.c:100:10: error: 'mobile_app_t' has no member named 'connected'; did you mean 'is_connected'?
  100 |     app->connected = false;
      |          ^~~~~~~~~
      |          is_connected
../Core/Src/mobile_app.c:101:10: error: 'mobile_app_t' has no member named 'last_heartbeat_time'; did you mean 'last_stream_time'?
  101 |     app->last_heartbeat_time = 0;
      |          ^~~~~~~~~~~~~~~~~~~
      |          last_stream_time
../Core/Src/mobile_app.c:102:10: error: 'mobile_app_t' has no member named 'last_command_time'; did you mean 'last_ping_time'?
  102 |     app->last_command_time = 0;
      |          ^~~~~~~~~~~~~~~~~
      |          last_ping_time
../Core/Src/mobile_app.c: In function 'MobileApp_Start':
../Core/Src/mobile_app.c:123:14: error: 'mobile_app_t' has no member named 'state'; did you mean 'rx_state'?
  123 |     if (app->state != MOBILE_APP_STATE_DISCONNECTED) {
      |              ^~~~~
      |              rx_state
../Core/Src/motor_driver.c: In function 'Motor_SetSpeed':
../Core/Src/motor_driver.c:81:22: warning: implicit declaration of function 'abs' [-Wimplicit-function-declaration]
   81 |     set_motor_pwm(0, abs(left_speed));
      |                      ^~~
../Core/Src/motor_driver.c:24:1: note: include '<stdlib.h>' or provide a declaration of 'abs'
   23 | #include "motor_driver.h"
  +++ |+#include <stdlib.h>
   24 | 
../Core/Src/mobile_app.c:123:23: error: 'MOBILE_APP_STATE_DISCONNECTED' undeclared (first use in this function)
  123 |     if (app->state != MOBILE_APP_STATE_DISCONNECTED) {
      |                       ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~
../Core/Src/mobile_app.c:124:16: error: 'MOBILE_APP_ERROR_INVALID_STATE' undeclared (first use in this function); did you mean 'MOBILE_APP_ERROR_INVALID_PARAMETER'?
  124 |         return MOBILE_APP_ERROR_INVALID_STATE;
      |                ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
      |                MOBILE_APP_ERROR_INVALID_PARAMETER
../Core/Src/mobile_app.c:130:10: error: 'mobile_app_t' has no member named 'state'; did you mean 'rx_state'?
  130 |     app->state = MOBILE_APP_STATE_WAITING_CONNECTION;
      |          ^~~~~
      |          rx_state
../Core/Src/mobile_app.c:130:18: error: 'MOBILE_APP_STATE_WAITING_CONNECTION' undeclared (first use in this function)
  130 |     app->state = MOBILE_APP_STATE_WAITING_CONNECTION;
      |                  ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
../Core/Src/mobile_app.c:131:10: error: 'mobile_app_t' has no member named 'last_heartbeat_time'; did you mean 'last_stream_time'?
  131 |     app->last_heartbeat_time = HAL_GetTick();
      |          ^~~~~~~~~~~~~~~~~~~
      |          last_stream_time
../Core/Src/mobile_app.c: In function 'MobileApp_Stop':
../Core/Src/mobile_app.c:149:10: error: 'mobile_app_t' has no member named 'state'; did you mean 'rx_state'?
  149 |     app->state = MOBILE_APP_STATE_DISCONNECTED;
      |          ^~~~~
      |          rx_state
arm-none-eabi-gcc "../Core/Src/stm32f4xx_hal_msp.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Core/Src/stm32f4xx_hal_msp.d" -MT"Core/Src/stm32f4xx_hal_msp.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Core/Src/stm32f4xx_hal_msp.o"
../Core/Src/mobile_app.c:149:18: error: 'MOBILE_APP_STATE_DISCONNECTED' undeclared (first use in this function)
  149 |     app->state = MOBILE_APP_STATE_DISCONNECTED;
      |                  ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~
../Core/Src/mobile_app.c:150:10: error: 'mobile_app_t' has no member named 'connected'; did you mean 'is_connected'?
  150 |     app->connected = false;
      |          ^~~~~~~~~
      |          is_connected
../Core/Src/mobile_app.c:151:8: error: 'mobile_app_t' has no member named 'control_output'
  151 |     app->control_output.forward_speed = 0.0f;
      |        ^~
../Core/Src/mobile_app.c:152:8: error: 'mobile_app_t' has no member named 'control_output'
  152 |     app->control_output.turn_speed = 0.0f;
      |        ^~
../Core/Src/mobile_app.c: In function 'MobileApp_Update':
../Core/Src/mobile_app.c:167:14: error: 'mobile_app_t' has no member named 'state'; did you mean 'rx_state'?
  167 |     if (app->state == MOBILE_APP_STATE_DISCONNECTED) {
      |              ^~~~~
      |              rx_state
../Core/Src/mobile_app.c:167:23: error: 'MOBILE_APP_STATE_DISCONNECTED' undeclared (first use in this function)
  167 |     if (app->state == MOBILE_APP_STATE_DISCONNECTED) {
      |                       ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~
../Core/Src/mobile_app.c:174:14: error: 'mobile_app_t' has no member named 'connected'; did you mean 'is_connected'?
  174 |     if (app->connected &&
      |              ^~~~~~~~~
      |              is_connected
../Core/Src/mobile_app.c:175:30: error: 'mobile_app_t' has no member named 'last_heartbeat_time'; did you mean 'last_stream_time'?
  175 |         (current_time - app->last_heartbeat_time >= app->config.connection_timeout_ms)) {
      |                              ^~~~~~~~~~~~~~~~~~~
      |                              last_stream_time
../Core/Src/mobile_app.c:175:56: error: 'mobile_app_t' has no member named 'config'
  175 |         (current_time - app->last_heartbeat_time >= app->config.connection_timeout_ms)) {
      |                                                        ^~
../Core/Src/mobile_app.c:176:14: error: 'mobile_app_t' has no member named 'connected'; did you mean 'is_connected'?
  176 |         app->connected = false;
      |              ^~~~~~~~~
      |              is_connected
../Core/Src/mobile_app.c:177:14: error: 'mobile_app_t' has no member named 'state'; did you mean 'rx_state'?
  177 |         app->state = MOBILE_APP_STATE_WAITING_CONNECTION;
      |              ^~~~~
      |              rx_state
arm-none-eabi-gcc "../Core/Src/stm32f4xx_it.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Core/Src/stm32f4xx_it.d" -MT"Core/Src/stm32f4xx_it.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Core/Src/stm32f4xx_it.o"
../Core/Src/mobile_app.c:177:22: error: 'MOBILE_APP_STATE_WAITING_CONNECTION' undeclared (first use in this function)
  177 |         app->state = MOBILE_APP_STATE_WAITING_CONNECTION;
      |                      ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
../Core/Src/mobile_app.c:178:12: error: 'mobile_app_t' has no member named 'control_output'
  178 |         app->control_output.forward_speed = 0.0f;
      |            ^~
../Core/Src/mobile_app.c:179:12: error: 'mobile_app_t' has no member named 'control_output'
  179 |         app->control_output.turn_speed = 0.0f;
      |            ^~
../Core/Src/mobile_app.c:191:14: error: 'mobile_app_t' has no member named 'connected'; did you mean 'is_connected'?
  191 |     if (app->connected &&
      |              ^~~~~~~~~
      |              is_connected
../Core/Src/mobile_app.c:192:30: error: 'mobile_app_t' has no member named 'last_heartbeat_time'; did you mean 'last_stream_time'?
  192 |         (current_time - app->last_heartbeat_time >= app->config.heartbeat_interval_ms)) {
      |                              ^~~~~~~~~~~~~~~~~~~
      |                              last_stream_time
../Core/Src/mobile_app.c:192:56: error: 'mobile_app_t' has no member named 'config'
  192 |         (current_time - app->last_heartbeat_time >= app->config.heartbeat_interval_ms)) {
      |                                                        ^~
../Core/Src/mobile_app.c:194:14: error: 'mobile_app_t' has no member named 'last_heartbeat_time'; did you mean 'last_stream_time'?
  194 |         app->last_heartbeat_time = current_time;
      |              ^~~~~~~~~~~~~~~~~~~
      |              last_stream_time
arm-none-eabi-gcc "../Core/Src/syscalls.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Core/Src/syscalls.d" -MT"Core/Src/syscalls.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Core/Src/syscalls.o"
../Core/Src/mobile_app.c: At top level:
../Core/Src/mobile_app.c:203:67: error: unknown type name 'mobile_app_control_t'; did you mean 'mobile_app_cmd_t'?
  203 | mobile_app_result_t MobileApp_GetControlOutput(mobile_app_t *app, mobile_app_control_t *control)
      |                                                                   ^~~~~~~~~~~~~~~~~~~~
      |                                                                   mobile_app_cmd_t
../Core/Src/mobile_app.c:223:60: error: unknown type name 'mobile_app_status_t'; did you mean 'mobile_app_frame_t'?
  223 | mobile_app_result_t MobileApp_GetStatus(mobile_app_t *app, mobile_app_status_t *status)
      |                                                            ^~~~~~~~~~~~~~~~~~~
      |                                                            mobile_app_frame_t
../Core/Src/mobile_app.c:242:21: error: conflicting types for 'MobileApp_SendStatus'; have 'mobile_app_result_t(mobile_app_t *, const balance_car_status_t *)'
  242 | mobile_app_result_t MobileApp_SendStatus(mobile_app_t *app, const balance_car_status_t *car_status)
      |                     ^~~~~~~~~~~~~~~~~~~~
../Core/Inc/mobile_app.h:190:21: note: previous declaration of 'MobileApp_SendStatus' with type 'mobile_app_result_t(mobile_app_t *, app_system_status_t *)'
  190 | mobile_app_result_t MobileApp_SendStatus(mobile_app_t *app, app_system_status_t *status);
      |                     ^~~~~~~~~~~~~~~~~~~~
../Core/Src/mobile_app.c: In function 'MobileApp_SendStatus':
../Core/Src/mobile_app.c:248:15: error: 'mobile_app_t' has no member named 'connected'; did you mean 'is_connected'?
  248 |     if (!app->connected) {
      |               ^~~~~~~~~
      |               is_connected
../Core/Src/mobile_app.c:249:16: error: 'MOBILE_APP_ERROR_NOT_CONNECTED' undeclared (first use in this function); did you mean 'MOBILE_APP_ERROR_NULL_POINTER'?
  249 |         return MOBILE_APP_ERROR_NOT_CONNECTED;
      |                ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
      |                MOBILE_APP_ERROR_NULL_POINTER
../Core/Src/mobile_app.c:258:41: error: 'balance_car_status_t' has no member named 'pitch_angle'
  258 |     int16_t pitch = (int16_t)(car_status->pitch_angle * 100);
      |                                         ^~
../Core/Src/mobile_app.c:259:40: error: 'balance_car_status_t' has no member named 'roll_angle'
  259 |     int16_t roll = (int16_t)(car_status->roll_angle * 100);
      |                                        ^~
../Core/Src/mobile_app.c:271:33: error: 'balance_car_status_t' has no member named 'uptime_ms'
  271 |     uint32_t uptime = car_status->uptime_ms;
      |                                 ^~
../Core/Src/mobile_app.c: In function 'MobileApp_SendFrame':
../Core/Src/mobile_app.c:315:70: error: 'mobile_app_t' has no member named 'config'
  315 |                                                  4 + payload_len, app->config.timeout_ms);
      |                                                                      ^~
../Core/Src/mobile_app.c: In function 'MobileApp_HandleControlCommand':
../Core/Src/mobile_app.c:346:8: error: 'mobile_app_t' has no member named 'control_output'
  346 |     app->control_output.forward_speed = (float)forward_raw / 32767.0f * app->config.max_forward_speed;
      |        ^~
../Core/Src/mobile_app.c:346:76: error: 'mobile_app_t' has no member named 'config'
  346 |     app->control_output.forward_speed = (float)forward_raw / 32767.0f * app->config.max_forward_speed;
      |                                                                            ^~
../Core/Src/mobile_app.c:347:8: error: 'mobile_app_t' has no member named 'control_output'
  347 |     app->control_output.turn_speed = (float)turn_raw / 32767.0f * app->config.max_turn_speed;
      |        ^~
../Core/Src/mobile_app.c:347:70: error: 'mobile_app_t' has no member named 'config'
  347 |     app->control_output.turn_speed = (float)turn_raw / 32767.0f * app->config.max_turn_speed;
      |                                                                      ^~
../Core/Src/mobile_app.c:350:9: warning: implicit declaration of function 'fabs' [-Wimplicit-function-declaration]
  350 |     if (fabs(app->control_output.forward_speed) < app->config.control_deadzone) {
      |         ^~~~
../Core/Src/mobile_app.c:17:1: note: include '<math.h>' or provide a declaration of 'fabs'
   16 | #include <string.h>
  +++ |+#include <math.h>
   17 | 
../Core/Src/mobile_app.c:350:9: warning: incompatible implicit declaration of built-in function 'fabs' [-Wbuiltin-declaration-mismatch]
  350 |     if (fabs(app->control_output.forward_speed) < app->config.control_deadzone) {
      |         ^~~~
../Core/Src/mobile_app.c:350:9: note: include '<math.h>' or provide a declaration of 'fabs'
../Core/Src/mobile_app.c:350:17: error: 'mobile_app_t' has no member named 'control_output'
  350 |     if (fabs(app->control_output.forward_speed) < app->config.control_deadzone) {
      |                 ^~
../Core/Src/mobile_app.c:350:54: error: 'mobile_app_t' has no member named 'config'
  350 |     if (fabs(app->control_output.forward_speed) < app->config.control_deadzone) {
      |                                                      ^~
../Core/Src/mobile_app.c:351:12: error: 'mobile_app_t' has no member named 'control_output'
  351 |         app->control_output.forward_speed = 0.0f;
      |            ^~
../Core/Src/mobile_app.c:353:9: warning: incompatible implicit declaration of built-in function 'fabs' [-Wbuiltin-declaration-mismatch]
  353 |     if (fabs(app->control_output.turn_speed) < app->config.control_deadzone) {
      |         ^~~~
../Core/Src/mobile_app.c:353:9: note: include '<math.h>' or provide a declaration of 'fabs'
../Core/Src/mobile_app.c:353:17: error: 'mobile_app_t' has no member named 'control_output'
  353 |     if (fabs(app->control_output.turn_speed) < app->config.control_deadzone) {
      |                 ^~
../Core/Src/mobile_app.c:353:51: error: 'mobile_app_t' has no member named 'config'
  353 |     if (fabs(app->control_output.turn_speed) < app->config.control_deadzone) {
      |                                                   ^~
../Core/Src/mobile_app.c:354:12: error: 'mobile_app_t' has no member named 'control_output'
  354 |         app->control_output.turn_speed = 0.0f;
      |            ^~
../Core/Src/mobile_app.c:357:8: error: 'mobile_app_t' has no member named 'control_output'
  357 |     app->control_output.valid = true;
      |        ^~
../Core/Src/mobile_app.c:358:10: error: 'mobile_app_t' has no member named 'last_command_time'; did you mean 'last_ping_time'?
  358 |     app->last_command_time = HAL_GetTick();
      |          ^~~~~~~~~~~~~~~~~
      |          last_ping_time
../Core/Src/mobile_app.c: At top level:
../Core/Src/mobile_app.c:52:28: warning: 'MobileApp_ProcessFrame' declared 'static' but never defined [-Wunused-function]
   52 | static mobile_app_result_t MobileApp_ProcessFrame(mobile_app_t *app, const mobile_app_frame_t *frame);
      |                            ^~~~~~~~~~~~~~~~~~~~~~
../Core/Src/mobile_app.c:56:28: warning: 'MobileApp_HandleStatusRequest' declared 'static' but never defined [-Wunused-function]
   56 | static mobile_app_result_t MobileApp_HandleStatusRequest(mobile_app_t *app);
      |                            ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~
../Core/Src/mobile_app.c:57:28: warning: 'MobileApp_HandleConfigCommand' declared 'static' but never defined [-Wunused-function]
   57 | static mobile_app_result_t MobileApp_HandleConfigCommand(mobile_app_t *app, const uint8_t *payload, uint8_t length);
      |                            ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~
../Core/Src/mobile_app.c:335:28: warning: 'MobileApp_HandleControlCommand' defined but not used [-Wunused-function]
  335 | static mobile_app_result_t MobileApp_HandleControlCommand(mobile_app_t *app, const uint8_t *payload, uint8_t length)
      |                            ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
arm-none-eabi-gcc "../Core/Src/sysmem.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Core/Src/sysmem.d" -MT"Core/Src/sysmem.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Core/Src/sysmem.o"
make: *** [Core/Src/subdir.mk:79: Core/Src/mobile_app.o] Error 1
make: *** Waiting for unfinished jobs....
"make -j8 all" terminated with exit code 2. Build might be incomplete.

22:34:27 Build Failed. 59 errors, 14 warnings. (took 3s.212ms)

