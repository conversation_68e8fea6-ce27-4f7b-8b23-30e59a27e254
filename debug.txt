22:25:40 **** Incremental Build of configuration Debug for project F411CEU6_MPU6050_KML ****
make -j8 all 
arm-none-eabi-gcc "../Core/Src/auto_follow.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Core/Src/auto_follow.d" -MT"Core/Src/auto_follow.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Core/Src/auto_follow.o"
arm-none-eabi-gcc "../Core/Src/balance_car_advanced.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Core/Src/balance_car_advanced.d" -MT"Core/Src/balance_car_advanced.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Core/Src/balance_car_advanced.o"
arm-none-eabi-gcc "../Core/Src/balance_car_demo.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Core/Src/balance_car_demo.d" -MT"Core/Src/balance_car_demo.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Core/Src/balance_car_demo.o"
arm-none-eabi-gcc "../Core/Src/balance_car_main.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Core/Src/balance_car_main.d" -MT"Core/Src/balance_car_main.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Core/Src/balance_car_main.o"
arm-none-eabi-gcc "../Core/Src/balance_car_system.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Core/Src/balance_car_system.d" -MT"Core/Src/balance_car_system.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Core/Src/balance_car_system.o"
arm-none-eabi-gcc "../Core/Src/balance_car_tuning.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Core/Src/balance_car_tuning.d" -MT"Core/Src/balance_car_tuning.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Core/Src/balance_car_tuning.o"
arm-none-eabi-gcc "../Core/Src/balance_controller.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Core/Src/balance_controller.d" -MT"Core/Src/balance_controller.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Core/Src/balance_controller.o"
arm-none-eabi-gcc "../Core/Src/main.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Core/Src/main.d" -MT"Core/Src/main.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Core/Src/main.o"
../Core/Src/balance_car_advanced.c: In function 'update_path_tracking':
../Core/Src/balance_car_advanced.c:392:10: error: 'joystick_data_t' has no member named 'x'
  392 |         .x = motion_cmd.forward_speed * 100,  // 转换为摇杆范围
      |          ^
../Core/Src/balance_car_advanced.c:392:24: error: 'motion_command_t' has no member named 'forward_speed'
  392 |         .x = motion_cmd.forward_speed * 100,  // 转换为摇杆范围
      |                        ^
../Core/Src/balance_car_advanced.c:393:10: error: 'joystick_data_t' has no member named 'y'
  393 |         .y = motion_cmd.turn_speed * 100,
      |          ^
../Core/Src/balance_car_advanced.c:393:24: error: 'motion_command_t' has no member named 'turn_speed'
  393 |         .y = motion_cmd.turn_speed * 100,
      |                        ^
../Core/Src/balance_car_advanced.c:395:38: error: 'motion_command_t' has no member named 'forward_speed'
  395 |         .raw_x = (int16_t)(motion_cmd.forward_speed * 2048),
      |                                      ^
../Core/Src/balance_car_advanced.c:396:38: error: 'motion_command_t' has no member named 'turn_speed'
  396 |         .raw_y = (int16_t)(motion_cmd.turn_speed * 2048)
      |                                      ^
../Core/Src/balance_car_advanced.c: In function 'execute_remote_command':
../Core/Src/balance_car_advanced.c:596:18: error: 'joystick_data_t' has no member named 'x'
  596 |                 .x = motion_cmd.forward_speed * 100,
      |                  ^
../Core/Src/balance_car_advanced.c:596:32: error: 'motion_command_t' has no member named 'forward_speed'
  596 |                 .x = motion_cmd.forward_speed * 100,
      |                                ^
../Core/Src/balance_car_advanced.c:597:18: error: 'joystick_data_t' has no member named 'y'
  597 |                 .y = motion_cmd.turn_speed * 100,
      |                  ^
../Core/Src/balance_car_advanced.c:597:32: error: 'motion_command_t' has no member named 'turn_speed'
  597 |                 .y = motion_cmd.turn_speed * 100,
      |                                ^
../Core/Src/balance_car_advanced.c:599:46: error: 'motion_command_t' has no member named 'forward_speed'
  599 |                 .raw_x = (int16_t)(motion_cmd.forward_speed * 2048),
      |                                              ^
../Core/Src/balance_car_advanced.c:600:46: error: 'motion_command_t' has no member named 'turn_speed'
  600 |                 .raw_y = (int16_t)(motion_cmd.turn_speed * 2048)
      |                                              ^
../Core/Src/balance_car_advanced.c:609:27: error: 'joystick_data_t' has no member named 'x'
  609 |             joystick_equiv.x = -cmd->parameter1 * 100;
      |                           ^
../Core/Src/balance_car_advanced.c:618:27: error: 'joystick_data_t' has no member named 'y'
  618 |             joystick_equiv.y = -cmd->parameter1 * 100;
      |                           ^
../Core/Src/balance_car_advanced.c:627:27: error: 'joystick_data_t' has no member named 'y'
  627 |             joystick_equiv.y = cmd->parameter1 * 100;
      |                           ^
../Core/Src/balance_car_advanced.c:584:22: warning: variable 'motion_cmd' set but not used [-Wunused-but-set-variable]
  584 |     motion_command_t motion_cmd = {0};
      |                      ^~~~~~~~~~
make: *** [Core/Src/subdir.mk:79: Core/Src/balance_car_advanced.o] Error 1
make: *** Waiting for unfinished jobs....
../Core/Src/balance_car_demo.c: In function 'BalanceCarDemo_Init':
../Core/Src/balance_car_demo.c:57:46: warning: passing argument 2 of 'BalanceCarSystem_Init' from incompatible pointer type [-Wincompatible-pointer-types]
   57 |     if (BalanceCarSystem_Init(&demo->system, &system_config) != HAL_OK) {
      |                                              ^~~~~~~~~~~~~~
      |                                              |
      |                                              demo_config_t *
In file included from ../Core/Inc/balance_car_demo.h:22,
                 from ../Core/Src/balance_car_demo.c:10:
../Core/Inc/balance_car_system.h:162:88: note: expected 'system_config_t *' but argument is of type 'demo_config_t *'
  162 | HAL_StatusTypeDef BalanceCarSystem_Init(balance_car_system_t *system, system_config_t *config);
      |                                                                       ~~~~~~~~~~~~~~~~~^~~~~~
../Core/Src/balance_car_main.c: In function 'BalanceCarMain_InitHardware':
../Core/Src/balance_car_main.c:378:21: warning: unused variable 'odrive_cfg' [-Wunused-variable]
  378 |     odrive_config_t odrive_cfg = {
      |                     ^~~~~~~~~~
../Core/Src/main.c:83:1: error: unknown type name 'motor_config_t'; did you mean 'motion_config_t'?
   83 | motor_config_t motor_config;
      | ^~~~~~~~~~~~~~
      | motion_config_t
../Core/Src/main.c: In function 'init_traditional_motor_system':
../Core/Src/main.c:283:17: error: request for member 'htim' in something not a structure or union
  283 |     motor_config.htim = &htim2;
      |                 ^
../Core/Src/main.c:284:17: error: request for member 'left_pwm_channel' in something not a structure or union
  284 |     motor_config.left_pwm_channel = TIM_CHANNEL_1;   // PA0 - TIM2_CH1
      |                 ^
../Core/Src/main.c:285:17: error: request for member 'right_pwm_channel' in something not a structure or union
  285 |     motor_config.right_pwm_channel = TIM_CHANNEL_2;  // PA1 - TIM2_CH2
      |                 ^
../Core/Src/main.c:288:17: error: request for member 'left_dir1_port' in something not a structure or union
  288 |     motor_config.left_dir1_port = GPIOA;
      |                 ^
../Core/Src/main.c:289:17: error: request for member 'left_dir1_pin' in something not a structure or union
  289 |     motor_config.left_dir1_pin = GPIO_PIN_2;
      |                 ^
../Core/Src/main.c:290:17: error: request for member 'left_dir2_port' in something not a structure or union
  290 |     motor_config.left_dir2_port = GPIOA;
      |                 ^
../Core/Src/main.c:291:17: error: request for member 'left_dir2_pin' in something not a structure or union
  291 |     motor_config.left_dir2_pin = GPIO_PIN_3;
      |                 ^
../Core/Src/main.c:294:17: error: request for member 'right_dir1_port' in something not a structure or union
  294 |     motor_config.right_dir1_port = GPIOA;
      |                 ^
../Core/Src/main.c:295:17: error: request for member 'right_dir1_pin' in something not a structure or union
  295 |     motor_config.right_dir1_pin = GPIO_PIN_4;
      |                 ^
../Core/Src/main.c:296:17: error: request for member 'right_dir2_port' in something not a structure or union
  296 |     motor_config.right_dir2_port = GPIOA;
      |                 ^
../Core/Src/main.c:297:17: error: request for member 'right_dir2_pin' in something not a structure or union
  297 |     motor_config.right_dir2_pin = GPIO_PIN_5;
      |                 ^
../Core/Src/main.c:300:17: error: request for member 'enable_port' in something not a structure or union
  300 |     motor_config.enable_port = GPIOA;
      |                 ^
../Core/Src/main.c:301:17: error: request for member 'enable_pin' in something not a structure or union
  301 |     motor_config.enable_pin = GPIO_PIN_6;
      |                 ^
../Core/Src/main.c:303:17: error: request for member 'driver_type' in something not a structure or union
  303 |     motor_config.driver_type = MOTOR_DRIVER_L298N;
      |                 ^
../Core/Src/balance_car_tuning.c: In function 'BalanceCarTuner_SetMotionParams':
../Core/Src/balance_car_tuning.c:203:5: warning: implicit declaration of function 'Motion_SetMaxTiltAngle' [-Wimplicit-function-declaration]
  203 |     Motion_SetMaxTiltAngle(&tuner->system->motion_controller, motion_params->max_tilt_angle);
      |     ^~~~~~~~~~~~~~~~~~~~~~
../Core/Src/main.c:303:32: error: 'MOTOR_DRIVER_L298N' undeclared (first use in this function)
  303 |     motor_config.driver_type = MOTOR_DRIVER_L298N;
      |                                ^~~~~~~~~~~~~~~~~~
../Core/Src/main.c:303:32: note: each undeclared identifier is reported only once for each function it appears in
../Core/Src/main.c:304:17: error: request for member 'pwm_frequency' in something not a structure or union
  304 |     motor_config.pwm_frequency = 1000;  // 1kHz PWM
      |                 ^
../Core/Src/main.c:305:17: error: request for member 'pwm_resolution' in something not a structure or union
  305 |     motor_config.pwm_resolution = 1000; // 0-1000 PWM范围
      |                 ^
../Core/Src/main.c:308:9: warning: implicit declaration of function 'Motor_Init'; did you mean 'Motion_Init'? [-Wimplicit-function-declaration]
  308 |     if (Motor_Init(&motor_config) != HAL_OK) {
      |         ^~~~~~~~~~
      |         Motion_Init
../Core/Src/main.c: At top level:
../Core/Src/main.c:121:13: warning: 'system_health_monitor' defined but not used [-Wunused-function]
  121 | static void system_health_monitor(void)
      |             ^~~~~~~~~~~~~~~~~~~~~
../Core/Src/balance_car_tuning.c:204:5: warning: implicit declaration of function 'Motion_SetMaxTurnRate'; did you mean 'Motion_CalculateTurnRate'? [-Wimplicit-function-declaration]
  204 |     Motion_SetMaxTurnRate(&tuner->system->motion_controller, motion_params->max_turn_rate);
      |     ^~~~~~~~~~~~~~~~~~~~~
      |     Motion_CalculateTurnRate
../Core/Src/balance_car_tuning.c:205:5: warning: implicit declaration of function 'Motion_SetAccelerationLimit' [-Wimplicit-function-declaration]
  205 |     Motion_SetAccelerationLimit(&tuner->system->motion_controller, motion_params->acceleration_limit);
      |     ^~~~~~~~~~~~~~~~~~~~~~~~~~~
make: *** [Core/Src/subdir.mk:79: Core/Src/main.o] Error 1
../Core/Src/balance_car_tuning.c:206:5: warning: implicit declaration of function 'Motion_SetResponseSpeed'; did you mean 'Motion_SetCruiseSpeed'? [-Wimplicit-function-declaration]
  206 |     Motion_SetResponseSpeed(&tuner->system->motion_controller, motion_params->response_speed);
      |     ^~~~~~~~~~~~~~~~~~~~~~~
      |     Motion_SetCruiseSpeed
"make -j8 all" terminated with exit code 2. Build might be incomplete.

22:25:41 Build Failed. 36 errors, 9 warnings. (took 764ms)

