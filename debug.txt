21:56:35 **** Build of configuration Debug for project F411CEU6_MPU6050_KML ****
make -j8 all 
arm-none-eabi-gcc "../KalmanFilter/Kalman.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"KalmanFilter/Kalman.d" -MT"KalmanFilter/Kalman.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "KalmanFilter/Kalman.o"
arm-none-eabi-gcc "../Drivers/ssd1306/ssd1306.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Drivers/ssd1306/ssd1306.d" -MT"Drivers/ssd1306/ssd1306.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Drivers/ssd1306/ssd1306.o"
arm-none-eabi-gcc "../Drivers/ssd1306/ssd1306_fonts.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Drivers/ssd1306/ssd1306_fonts.d" -MT"Drivers/ssd1306/ssd1306_fonts.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Drivers/ssd1306/ssd1306_fonts.o"
arm-none-eabi-gcc "../Drivers/ssd1306/ssd1306_tests.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Drivers/ssd1306/ssd1306_tests.d" -MT"Drivers/ssd1306/ssd1306_tests.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Drivers/ssd1306/ssd1306_tests.o"
arm-none-eabi-gcc "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.d" -MT"Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.o"
arm-none-eabi-gcc "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.d" -MT"Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.o"
arm-none-eabi-gcc "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.d" -MT"Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.o"
arm-none-eabi-gcc "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.d" -MT"Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.o"
arm-none-eabi-gcc "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.d" -MT"Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.o"
arm-none-eabi-gcc "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.d" -MT"Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.o"
arm-none-eabi-gcc "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.d" -MT"Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.o"
arm-none-eabi-gcc "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.d" -MT"Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.o"
arm-none-eabi-gcc "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.d" -MT"Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.o"
arm-none-eabi-gcc "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.d" -MT"Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.o"
arm-none-eabi-gcc "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.d" -MT"Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.o"
arm-none-eabi-gcc "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c.d" -MT"Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c.o"
arm-none-eabi-gcc "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c_ex.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c_ex.d" -MT"Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c_ex.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c_ex.o"
arm-none-eabi-gcc "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.d" -MT"Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.o"
arm-none-eabi-gcc "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.d" -MT"Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.o"
arm-none-eabi-gcc "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.d" -MT"Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.o"
arm-none-eabi-gcc "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.d" -MT"Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.o"
arm-none-eabi-gcc "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.d" -MT"Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.o"
arm-none-eabi-gcc "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.d" -MT"Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.o"
arm-none-eabi-gcc "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.d" -MT"Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.o"
arm-none-eabi-gcc "../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_ll_adc.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_ll_adc.d" -MT"Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_ll_adc.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_ll_adc.o"
arm-none-eabi-gcc -mcpu=cortex-m4 -g3 -DDEBUG -c -x assembler-with-cpp -MMD -MP -MF"Core/Startup/startup_stm32f411ceux.d" -MT"Core/Startup/startup_stm32f411ceux.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Core/Startup/startup_stm32f411ceux.o" "../Core/Startup/startup_stm32f411ceux.s"
arm-none-eabi-gcc "../Core/Src/auto_follow.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Core/Src/auto_follow.d" -MT"Core/Src/auto_follow.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Core/Src/auto_follow.o"
arm-none-eabi-gcc "../Core/Src/balance_car_advanced.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Core/Src/balance_car_advanced.d" -MT"Core/Src/balance_car_advanced.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Core/Src/balance_car_advanced.o"
arm-none-eabi-gcc "../Core/Src/balance_car_demo.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Core/Src/balance_car_demo.d" -MT"Core/Src/balance_car_demo.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Core/Src/balance_car_demo.o"
arm-none-eabi-gcc "../Core/Src/balance_car_main.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Core/Src/balance_car_main.d" -MT"Core/Src/balance_car_main.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Core/Src/balance_car_main.o"
arm-none-eabi-gcc "../Core/Src/balance_car_system.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Core/Src/balance_car_system.d" -MT"Core/Src/balance_car_system.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Core/Src/balance_car_system.o"
In file included from ../Core/Src/auto_follow.c:12:
../Core/Inc/auto_follow.h:76:5: error: unknown type name 'pid_config_t'
   76 |     pid_config_t distance_pid;   // 距离PID控制器
      |     ^~~~~~~~~~~~
../Core/Inc/auto_follow.h:182:1: error: unknown type name 'follow_state_t'; did you mean 'follow_mode_t'?
  182 | follow_state_t AutoFollow_GetState(auto_follow_t *follow);
      | ^~~~~~~~~~~~~~
      | follow_mode_t
arm-none-eabi-gcc "../Core/Src/balance_car_tuning.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Core/Src/balance_car_tuning.d" -MT"Core/Src/balance_car_tuning.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Core/Src/balance_car_tuning.o"
In file included from ../Core/Src/auto_follow.c:13:
../Core/Inc/balance_car_main.h:134:5: error: unknown type name 'joystick_driver_t'
  134 |     joystick_driver_t joystick;
      |     ^~~~~~~~~~~~~~~~~
../Core/Src/auto_follow.c:46:22: error: conflicting types for 'AutoFollow_GetDefaultConfig'; have 'auto_follow_result_t(follow_config_t *)'
   46 | auto_follow_result_t AutoFollow_GetDefaultConfig(follow_config_t *config)
      |                      ^~~~~~~~~~~~~~~~~~~~~~~~~~~
../Core/Inc/auto_follow.h:166:6: note: previous declaration of 'AutoFollow_GetDefaultConfig' with type 'void(follow_config_t *)'
  166 | void AutoFollow_GetDefaultConfig(follow_config_t *config);
      |      ^~~~~~~~~~~~~~~~~~~~~~~~~~~
../Core/Src/auto_follow.c: In function 'AutoFollow_GetDefaultConfig':
../Core/Src/auto_follow.c:56:11: error: 'follow_config_t' has no member named 'min_follow_distance'
   56 |     config->min_follow_distance = 0.2f;    // 最小跟随距离 20cm
      |           ^~
../Core/Src/auto_follow.c:57:13: error: 'follow_config_t' has no member named 'max_follow_distance'; did you mean 'max_follow_speed'?
   57 |     config->max_follow_distance = 2.0f;    // 最大跟随距离 2m
      |             ^~~~~~~~~~~~~~~~~~~
      |             max_follow_speed
../Core/Src/auto_follow.c:60:11: error: 'follow_config_t' has no member named 'ultrasonic_timeout_us'
   60 |     config->ultrasonic_timeout_us = 30000; // 30ms超时
      |           ^~
../Core/Src/auto_follow.c:62:11: error: 'follow_config_t' has no member named 'filter_coefficient'
   62 |     config->filter_coefficient = 0.3f;     // 滤波系数
      |           ^~
../Core/Src/auto_follow.c:65:25: error: request for member 'kp' in something not a structure or union
   65 |     config->distance_pid.kp = 2.0f;
      |                         ^
../Core/Src/auto_follow.c:66:25: error: request for member 'ki' in something not a structure or union
   66 |     config->distance_pid.ki = 0.1f;
      |                         ^
../Core/Src/auto_follow.c:67:25: error: request for member 'kd' in something not a structure or union
   67 |     config->distance_pid.kd = 0.5f;
      |                         ^
../Core/Src/auto_follow.c:68:25: error: request for member 'output_limit' in something not a structure or union
   68 |     config->distance_pid.output_limit = 1.0f;
      |                         ^
../Core/Src/auto_follow.c:71:13: error: 'follow_config_t' has no member named 'lost_target_timeout_ms'; did you mean 'target_timeout_ms'?
   71 |     config->lost_target_timeout_ms = 3000; // 3秒失去目标超时
      |             ^~~~~~~~~~~~~~~~~~~~~~
      |             target_timeout_ms
../Core/Src/auto_follow.c:72:11: error: 'follow_config_t' has no member named 'emergency_stop_distance'
   72 |     config->emergency_stop_distance = 0.15f; // 15cm紧急停止距离
      |           ^~
../Core/Src/auto_follow.c: In function 'AutoFollow_Init':
../Core/Src/auto_follow.c:95:13: error: 'auto_follow_t' has no member named 'last_valid_distance'; did you mean 'total_distance'?
   95 |     follow->last_valid_distance = 0.0f;
      |             ^~~~~~~~~~~~~~~~~~~
      |             total_distance
../Core/Src/auto_follow.c: In function 'AutoFollow_Start':
../Core/Src/auto_follow.c:127:11: error: 'auto_follow_t' has no member named 'last_measurement_time'
  127 |     follow->last_measurement_time = HAL_GetTick();
      |           ^~
../Core/Src/auto_follow.c:128:13: error: 'auto_follow_t' has no member named 'target_lost_time'; did you mean 'target_lost_count'?
  128 |     follow->target_lost_time = 0;
      |             ^~~~~~~~~~~~~~~~
      |             target_lost_count
../Core/Src/auto_follow.c: In function 'AutoFollow_Update':
../Core/Src/auto_follow.c:172:30: error: 'auto_follow_t' has no member named 'last_measurement_time'
  172 |     if (current_time - follow->last_measurement_time >= (1000 / follow->config.measurement_frequency)) {
      |                              ^~
../Core/Src/auto_follow.c:173:15: error: 'auto_follow_t' has no member named 'last_measurement_time'
  173 |         follow->last_measurement_time = current_time;
      |               ^~
../Core/Src/auto_follow.c:183:45: error: 'auto_follow_t' has no member named 'target_lost_time'; did you mean 'target_lost_count'?
  183 |     if (!follow->target_detected && follow->target_lost_time > 0) {
      |                                             ^~~~~~~~~~~~~~~~
      |                                             target_lost_count
../Core/Src/auto_follow.c:184:36: error: 'auto_follow_t' has no member named 'target_lost_time'; did you mean 'target_lost_count'?
  184 |         if (current_time - follow->target_lost_time >= follow->config.lost_target_timeout_ms) {
      |                                    ^~~~~~~~~~~~~~~~
      |                                    target_lost_count
../Core/Src/auto_follow.c:184:71: error: 'follow_config_t' has no member named 'lost_target_timeout_ms'; did you mean 'target_timeout_ms'?
  184 |         if (current_time - follow->target_lost_time >= follow->config.lost_target_timeout_ms) {
      |                                                                       ^~~~~~~~~~~~~~~~~~~~~~
      |                                                                       target_timeout_ms
../Core/Src/auto_follow.c: In function 'AutoFollow_GetMotionOutput':
../Core/Src/auto_follow.c:204:15: error: incompatible types when assigning to type 'auto_follow_motion_t' from type 'motion_output_t'
  204 |     *motion = follow->motion_output;
      |               ^~~~~~
../Core/Src/auto_follow.c: In function 'AutoFollow_GetStatus':
../Core/Src/auto_follow.c:219:13: error: 'auto_follow_status_t' has no member named 'current_distance'; did you mean 'target_distance'?
  219 |     status->current_distance = follow->current_distance;
      |             ^~~~~~~~~~~~~~~~
      |             target_distance
../Core/Src/auto_follow.c:219:38: error: 'auto_follow_t' has no member named 'current_distance'
  219 |     status->current_distance = follow->current_distance;
      |                                      ^~
../Core/Src/auto_follow.c: In function 'AutoFollow_ProcessUltrasonicData':
../Core/Src/auto_follow.c:291:15: error: 'auto_follow_t' has no member named 'measurement_valid'
  291 |         follow->measurement_valid = false;
      |               ^~
../Core/Src/auto_follow.c:295:11: error: 'auto_follow_t' has no member named 'current_distance'
  295 |     follow->current_distance = raw_distance;
      |           ^~
../Core/Src/auto_follow.c:296:11: error: 'auto_follow_t' has no member named 'measurement_valid'
  296 |     follow->measurement_valid = true;
      |           ^~
../Core/Src/auto_follow.c:302:87: error: 'follow_config_t' has no member named 'filter_coefficient'
  302 |         follow->filtered_distance = follow->filtered_distance * (1.0f - follow->config.filter_coefficient) +
      |                                                                                       ^
../Core/Src/auto_follow.c:303:65: error: 'follow_config_t' has no member named 'filter_coefficient'
  303 |                                    raw_distance * follow->config.filter_coefficient;
      |                                                                 ^
../Core/Src/auto_follow.c:307:52: error: 'follow_config_t' has no member named 'min_follow_distance'
  307 |     if (follow->filtered_distance >= follow->config.min_follow_distance &&
      |                                                    ^
../Core/Src/auto_follow.c:308:53: error: 'follow_config_t' has no member named 'max_follow_distance'; did you mean 'max_follow_speed'?
  308 |         follow->filtered_distance <= follow->config.max_follow_distance) {
      |                                                     ^~~~~~~~~~~~~~~~~~~
      |                                                     max_follow_speed
../Core/Src/auto_follow.c:313:21: error: 'auto_follow_t' has no member named 'target_lost_time'; did you mean 'target_lost_count'?
  313 |             follow->target_lost_time = 0;
      |                     ^~~~~~~~~~~~~~~~
      |                     target_lost_count
../Core/Src/auto_follow.c:316:17: error: 'auto_follow_t' has no member named 'last_valid_distance'; did you mean 'total_distance'?
  316 |         follow->last_valid_distance = follow->filtered_distance;
      |                 ^~~~~~~~~~~~~~~~~~~
      |                 total_distance
../Core/Src/auto_follow.c:320:21: error: 'auto_follow_t' has no member named 'target_lost_time'; did you mean 'target_lost_count'?
  320 |             follow->target_lost_time = HAL_GetTick();
      |                     ^~~~~~~~~~~~~~~~
      |                     target_lost_count
../Core/Src/auto_follow.c: In function 'AutoFollow_CalculateMotion':
../Core/Src/auto_follow.c:341:52: error: 'follow_config_t' has no member named 'emergency_stop_distance'
  341 |     if (follow->filtered_distance <= follow->config.emergency_stop_distance) {
      |                                                    ^
../Core/Src/auto_follow.c: In function 'AutoFollow_UpdatePIDController':
../Core/Src/auto_follow.c:377:68: error: request for member 'output_limit' in something not a structure or union
  377 |     if (follow->distance_pid_integral > follow->config.distance_pid.output_limit) {
      |                                                                    ^
../Core/Src/auto_follow.c:378:68: error: request for member 'output_limit' in something not a structure or union
  378 |         follow->distance_pid_integral = follow->config.distance_pid.output_limit;
      |                                                                    ^
../Core/Src/auto_follow.c:379:76: error: request for member 'output_limit' in something not a structure or union
  379 |     } else if (follow->distance_pid_integral < -follow->config.distance_pid.output_limit) {
      |                                                                            ^
../Core/Src/auto_follow.c:380:69: error: request for member 'output_limit' in something not a structure or union
  380 |         follow->distance_pid_integral = -follow->config.distance_pid.output_limit;
      |                                                                     ^
../Core/Src/auto_follow.c:385:36: error: request for member 'kp' in something not a structure or union
  385 |         follow->config.distance_pid.kp * error +
      |                                    ^
../Core/Src/auto_follow.c:386:36: error: request for member 'ki' in something not a structure or union
  386 |         follow->config.distance_pid.ki * follow->distance_pid_integral +
      |                                    ^
../Core/Src/auto_follow.c:387:36: error: request for member 'kd' in something not a structure or union
  387 |         follow->config.distance_pid.kd * derivative;
      |                                    ^
../Core/Src/balance_car_demo.c: In function 'BalanceCarDemo_Init':
../Core/Src/balance_car_demo.c:54:5: error: unknown type name 'balance_car_config_t'; did you mean 'balance_car_tuner_t'?
   54 |     balance_car_config_t system_config;
      |     ^~~~~~~~~~~~~~~~~~~~
      |     balance_car_tuner_t
make: *** [Core/Src/subdir.mk:79: Core/Src/auto_follow.o] Error 1
make: *** Waiting for unfinished jobs....
../Core/Src/balance_car_demo.c:55:5: warning: implicit declaration of function 'BalanceCarSystem_GetDefaultConfig'; did you mean 'BalanceCarDemo_GetDefaultConfig'? [-Wimplicit-function-declaration]
   55 |     BalanceCarSystem_GetDefaultConfig(&system_config);
      |     ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
      |     BalanceCarDemo_GetDefaultConfig
../Core/Src/balance_car_demo.c:57:46: warning: passing argument 2 of 'BalanceCarSystem_Init' from incompatible pointer type [-Wincompatible-pointer-types]
   57 |     if (BalanceCarSystem_Init(&demo->system, &system_config) != HAL_OK) {
      |                                              ^~~~~~~~~~~~~~
      |                                              |
      |                                              int *
In file included from ../Core/Inc/balance_car_demo.h:22,
                 from ../Core/Src/balance_car_demo.c:10:
../Core/Inc/balance_car_system.h:160:88: note: expected 'system_config_t *' but argument is of type 'int *'
  160 | HAL_StatusTypeDef BalanceCarSystem_Init(balance_car_system_t *system, system_config_t *config);
      |                                                                       ~~~~~~~~~~~~~~~~~^~~~~~
../Core/Src/balance_car_demo.c: In function 'BalanceCarDemo_GenerateReport':
../Core/Src/balance_car_demo.c:394:29: error: 'system_status_t' has no member named 'system_mode'
  394 |         (demo->system.status.system_mode == SYSTEM_MODE_BALANCE_ONLY) ? "平衡中" : "其他",
      |                             ^
In file included from ../Core/Inc/balance_car_main.h:30,
                 from ../Core/Src/balance_car_main.c:26:
../Core/Inc/auto_follow.h:76:5: error: unknown type name 'pid_config_t'
   76 |     pid_config_t distance_pid;   // 距离PID控制器
      |     ^~~~~~~~~~~~
../Core/Src/balance_car_demo.c: In function 'display_sensor_data':
../Core/Src/balance_car_demo.c:652:31: error: 'system_status_t' has no member named 'temperature'
  652 |            demo->system.status.temperature);
      |                               ^
../Core/Src/balance_car_advanced.c: In function 'update_path_tracking':
../Core/Src/balance_car_advanced.c:389:57: warning: passing argument 2 of 'Motion_Update' from incompatible pointer type [-Wincompatible-pointer-types]
  389 |     Motion_Update(&advanced->system->motion_controller, &motion_cmd);
      |                                                         ^~~~~~~~~~~
      |                                                         |
      |                                                         motion_command_t *
In file included from ../Core/Inc/balance_car_system.h:28,
                 from ../Core/Inc/balance_car_advanced.h:22,
                 from ../Core/Src/balance_car_advanced.c:10:
../Core/Inc/motion_controller.h:138:49: note: expected 'joystick_data_t *' but argument is of type 'motion_command_t *'
  138 |                                joystick_data_t *joystick_data,
      |                                ~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~
../Core/Src/balance_car_advanced.c:389:5: error: too few arguments to function 'Motion_Update'
  389 |     Motion_Update(&advanced->system->motion_controller, &motion_cmd);
      |     ^~~~~~~~~~~~~
../Core/Inc/motion_controller.h:137:19: note: declared here
  137 | HAL_StatusTypeDef Motion_Update(motion_controller_t *controller,
      |                   ^~~~~~~~~~~~~
../Core/Src/balance_car_advanced.c: In function 'update_performance_metrics':
../Core/Src/balance_car_advanced.c:564:28: warning: implicit declaration of function 'abs' [-Wimplicit-function-declaration]
  564 |     int16_t motor_output = abs(advanced->system->status.left_motor_output) +
      |                            ^~~
../Core/Src/balance_car_advanced.c:14:1: note: include '<stdlib.h>' or provide a declaration of 'abs'
   13 | #include <stdio.h>
  +++ |+#include <stdlib.h>
   14 | 
../Core/Src/balance_car_advanced.c: In function 'execute_remote_command':
../Core/Src/balance_car_advanced.c:584:65: warning: passing argument 2 of 'Motion_Update' from incompatible pointer type [-Wincompatible-pointer-types]
  584 |             Motion_Update(&advanced->system->motion_controller, &motion_cmd);
      |                                                                 ^~~~~~~~~~~
      |                                                                 |
      |                                                                 motion_command_t *
../Core/Inc/motion_controller.h:138:49: note: expected 'joystick_data_t *' but argument is of type 'motion_command_t *'
  138 |                                joystick_data_t *joystick_data,
      |                                ~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~
../Core/Src/balance_car_advanced.c:584:13: error: too few arguments to function 'Motion_Update'
  584 |             Motion_Update(&advanced->system->motion_controller, &motion_cmd);
      |             ^~~~~~~~~~~~~
../Core/Inc/motion_controller.h:137:19: note: declared here
  137 | HAL_StatusTypeDef Motion_Update(motion_controller_t *controller,
      |                   ^~~~~~~~~~~~~
../Core/Src/balance_car_advanced.c:590:65: warning: passing argument 2 of 'Motion_Update' from incompatible pointer type [-Wincompatible-pointer-types]
  590 |             Motion_Update(&advanced->system->motion_controller, &motion_cmd);
      |                                                                 ^~~~~~~~~~~
      |                                                                 |
      |                                                                 motion_command_t *
../Core/Inc/motion_controller.h:138:49: note: expected 'joystick_data_t *' but argument is of type 'motion_command_t *'
  138 |                                joystick_data_t *joystick_data,
      |                                ~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~
../Core/Src/balance_car_advanced.c:590:13: error: too few arguments to function 'Motion_Update'
  590 |             Motion_Update(&advanced->system->motion_controller, &motion_cmd);
      |             ^~~~~~~~~~~~~
../Core/Inc/motion_controller.h:137:19: note: declared here
  137 | HAL_StatusTypeDef Motion_Update(motion_controller_t *controller,
      |                   ^~~~~~~~~~~~~
../Core/Src/balance_car_advanced.c:596:65: warning: passing argument 2 of 'Motion_Update' from incompatible pointer type [-Wincompatible-pointer-types]
  596 |             Motion_Update(&advanced->system->motion_controller, &motion_cmd);
      |                                                                 ^~~~~~~~~~~
      |                                                                 |
      |                                                                 motion_command_t *
../Core/Inc/motion_controller.h:138:49: note: expected 'joystick_data_t *' but argument is of type 'motion_command_t *'
  138 |                                joystick_data_t *joystick_data,
      |                                ~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~
../Core/Inc/auto_follow.h:182:1: error: unknown type name 'follow_state_t'; did you mean 'follow_mode_t'?
  182 | follow_state_t AutoFollow_GetState(auto_follow_t *follow);
      | ^~~~~~~~~~~~~~
      | follow_mode_t
../Core/Src/balance_car_advanced.c:596:13: error: too few arguments to function 'Motion_Update'
  596 |             Motion_Update(&advanced->system->motion_controller, &motion_cmd);
      |             ^~~~~~~~~~~~~
../Core/Inc/motion_controller.h:137:19: note: declared here
  137 | HAL_StatusTypeDef Motion_Update(motion_controller_t *controller,
      |                   ^~~~~~~~~~~~~
../Core/Src/balance_car_advanced.c:602:65: warning: passing argument 2 of 'Motion_Update' from incompatible pointer type [-Wincompatible-pointer-types]
  602 |             Motion_Update(&advanced->system->motion_controller, &motion_cmd);
      |                                                                 ^~~~~~~~~~~
      |                                                                 |
      |                                                                 motion_command_t *
../Core/Inc/motion_controller.h:138:49: note: expected 'joystick_data_t *' but argument is of type 'motion_command_t *'
  138 |                                joystick_data_t *joystick_data,
      |                                ~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~
../Core/Src/balance_car_advanced.c:602:13: error: too few arguments to function 'Motion_Update'
  602 |             Motion_Update(&advanced->system->motion_controller, &motion_cmd);
      |             ^~~~~~~~~~~~~
../Core/Inc/motion_controller.h:137:19: note: declared here
  137 | HAL_StatusTypeDef Motion_Update(motion_controller_t *controller,
      |                   ^~~~~~~~~~~~~
../Core/Inc/balance_car_main.h:134:5: error: unknown type name 'joystick_driver_t'
  134 |     joystick_driver_t joystick;
      |     ^~~~~~~~~~~~~~~~~
make: *** [Core/Src/subdir.mk:79: Core/Src/balance_car_demo.o] Error 1
../Core/Src/balance_car_system.c: In function 'update_sensor_data':
../Core/Src/balance_car_system.c:345:24: error: 'mpu6050_data_t' has no member named 'yaw'
  345 |     system->sensor_data.yaw = 0.0f;
      |                        ^
make: *** [Core/Src/subdir.mk:79: Core/Src/balance_car_advanced.o] Error 1
../Core/Src/balance_car_system.c: In function 'update_joystick_data':
../Core/Src/balance_car_system.c:370:54: error: 'joystick_data_t' has no member named 'x_raw'
  370 |     system->status.joystick_x = system->joystick_data.x_raw;
      |                                                      ^
../Core/Src/balance_car_system.c:371:54: error: 'joystick_data_t' has no member named 'y_raw'
  371 |     system->status.joystick_y = system->joystick_data.y_raw;
      |                                                      ^
../Core/Src/balance_car_main.c:56:13: error: conflicting types for 'BalanceCarMain_SafetyCheck'; have 'void(balance_car_main_t *)'
   56 | static void BalanceCarMain_SafetyCheck(balance_car_main_t *car);
      |             ^~~~~~~~~~~~~~~~~~~~~~~~~~
../Core/Inc/balance_car_main.h:217:19: note: previous declaration of 'BalanceCarMain_SafetyCheck' with type 'HAL_StatusTypeDef(balance_car_main_t *)'
  217 | HAL_StatusTypeDef BalanceCarMain_SafetyCheck(balance_car_main_t *car);
      |                   ^~~~~~~~~~~~~~~~~~~~~~~~~~
../Core/Src/balance_car_main.c: In function 'BalanceCarMain_GetDefaultConfig':
../Core/Src/balance_car_main.c:69:11: error: 'balance_car_config_t' has no member named 'control_frequency'
   69 |     config->control_frequency = 200;  // 200Hz控制频率
      |           ^~
../Core/Src/balance_car_main.c:70:11: error: 'balance_car_config_t' has no member named 'display_frequency'
   70 |     config->display_frequency = 10;   // 10Hz显示更新
      |           ^~
../Core/Src/balance_car_main.c:73:11: error: 'balance_car_config_t' has no member named 'balance_pid'
   73 |     config->balance_pid.kp = 15.0f;
      |           ^~
../Core/Src/balance_car_main.c:74:11: error: 'balance_car_config_t' has no member named 'balance_pid'
   74 |     config->balance_pid.ki = 0.1f;
      |           ^~
../Core/Src/balance_car_main.c:75:11: error: 'balance_car_config_t' has no member named 'balance_pid'
   75 |     config->balance_pid.kd = 0.8f;
      |           ^~
../Core/Src/balance_car_main.c:76:11: error: 'balance_car_config_t' has no member named 'balance_pid'
   76 |     config->balance_pid.output_limit = 1000.0f;
      |           ^~
../Core/Src/balance_car_main.c:79:11: error: 'balance_car_config_t' has no member named 'velocity_pid'
   79 |     config->velocity_pid.kp = 0.5f;
      |           ^~
../Core/Src/balance_car_main.c:80:11: error: 'balance_car_config_t' has no member named 'velocity_pid'
   80 |     config->velocity_pid.ki = 0.01f;
      |           ^~
../Core/Src/balance_car_main.c:81:11: error: 'balance_car_config_t' has no member named 'velocity_pid'
   81 |     config->velocity_pid.kd = 0.0f;
      |           ^~
../Core/Src/balance_car_main.c:82:11: error: 'balance_car_config_t' has no member named 'velocity_pid'
   82 |     config->velocity_pid.output_limit = 30.0f;
      |           ^~
../Core/Src/balance_car_main.c:85:11: error: 'balance_car_config_t' has no member named 'turn_pid'
   85 |     config->turn_pid.kp = 2.0f;
      |           ^~
../Core/Src/balance_car_main.c:86:11: error: 'balance_car_config_t' has no member named 'turn_pid'
   86 |     config->turn_pid.ki = 0.0f;
      |           ^~
../Core/Src/balance_car_main.c:87:11: error: 'balance_car_config_t' has no member named 'turn_pid'
   87 |     config->turn_pid.kd = 0.1f;
      |           ^~
../Core/Src/balance_car_main.c:88:11: error: 'balance_car_config_t' has no member named 'turn_pid'
   88 |     config->turn_pid.output_limit = 500.0f;
      |           ^~
../Core/Src/balance_car_main.c:92:11: error: 'balance_car_config_t' has no member named 'max_velocity'
   92 |     config->max_velocity = 2.0f;         // 最大速度 m/s
      |           ^~
../Core/Src/balance_car_main.c:96:11: error: 'balance_car_config_t' has no member named 'wheel_diameter'
   96 |     config->wheel_diameter = 0.1f;       // 轮子直径 10cm
      |           ^~
../Core/Src/balance_car_main.c:97:11: error: 'balance_car_config_t' has no member named 'wheel_base'
   97 |     config->wheel_base = 0.2f;           // 轮距 20cm
      |           ^~
../Core/Src/balance_car_main.c:98:11: error: 'balance_car_config_t' has no member named 'robot_height'
   98 |     config->robot_height = 0.3f;         // 机器人高度 30cm
      |           ^~
../Core/Src/balance_car_main.c: At top level:
../Core/Src/balance_car_main.c:372:29: error: redefinition of 'BalanceCarMain_InitHardware'
  372 | static balance_car_result_t BalanceCarMain_InitHardware(balance_car_main_t *car)
      |                             ^~~~~~~~~~~~~~~~~~~~~~~~~~~
../Core/Src/balance_car_main.c:144:29: note: previous definition of 'BalanceCarMain_InitHardware' with type 'balance_car_result_t(balance_car_main_t *)'
  144 | static balance_car_result_t BalanceCarMain_InitHardware(balance_car_main_t *car)
      |                             ^~~~~~~~~~~~~~~~~~~~~~~~~~~
../Core/Src/balance_car_main.c: In function 'BalanceCarMain_InitHardware':
../Core/Src/balance_car_main.c:385:21: warning: unused variable 'odrive_cfg' [-Wunused-variable]
  385 |     odrive_config_t odrive_cfg = {
      |                     ^~~~~~~~~~
../Core/Src/balance_car_main.c: At top level:
../Core/Src/balance_car_main.c:403:29: error: redefinition of 'BalanceCarMain_InitControllers'
  403 | static balance_car_result_t BalanceCarMain_InitControllers(balance_car_main_t *car)
      |                             ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
../Core/Src/balance_car_main.c:154:29: note: previous definition of 'BalanceCarMain_InitControllers' with type 'balance_car_result_t(balance_car_main_t *)'
  154 | static balance_car_result_t BalanceCarMain_InitControllers(balance_car_main_t *car)
      |                             ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
../Core/Src/balance_car_main.c:421:13: error: redefinition of 'BalanceCarMain_UpdateSensors'
  421 | static void BalanceCarMain_UpdateSensors(balance_car_main_t *car)
      |             ^~~~~~~~~~~~~~~~~~~~~~~~~~~~
../Core/Src/balance_car_main.c:164:13: note: previous definition of 'BalanceCarMain_UpdateSensors' with type 'void(balance_car_main_t *)'
  164 | static void BalanceCarMain_UpdateSensors(balance_car_main_t *car)
      |             ^~~~~~~~~~~~~~~~~~~~~~~~~~~~
../Core/Src/balance_car_main.c: In function 'BalanceCarMain_UpdateSensors':
../Core/Src/balance_car_main.c:427:23: error: 'struct <anonymous>' has no member named 'x'
  427 |     car->joystick_data.x = (float)(car->joystick_raw[0] - 2048) / 2048.0f;
      |                       ^
../Core/Src/balance_car_main.c:428:23: error: 'struct <anonymous>' has no member named 'y'
  428 |     car->joystick_data.y = (float)(car->joystick_raw[1] - 2048) / 2048.0f;
      |                       ^
../Core/Src/balance_car_main.c: At top level:
../Core/Src/balance_car_main.c:437:13: error: redefinition of 'BalanceCarMain_RunControlLoop'
  437 | static void BalanceCarMain_RunControlLoop(balance_car_main_t *car)
      |             ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~
../Core/Src/balance_car_main.c:172:13: note: previous definition of 'BalanceCarMain_RunControlLoop' with type 'void(balance_car_main_t *)'
  172 | static void BalanceCarMain_RunControlLoop(balance_car_main_t *car)
      |             ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~
../Core/Src/balance_car_main.c:467:13: error: redefinition of 'BalanceCarMain_UpdateDisplay'
  467 | static void BalanceCarMain_UpdateDisplay(balance_car_main_t *car)
      |             ^~~~~~~~~~~~~~~~~~~~~~~~~~~~
../Core/Src/balance_car_main.c:180:13: note: previous definition of 'BalanceCarMain_UpdateDisplay' with type 'void(balance_car_main_t *)'
  180 | static void BalanceCarMain_UpdateDisplay(balance_car_main_t *car)
      |             ^~~~~~~~~~~~~~~~~~~~~~~~~~~~
../Core/Src/balance_car_main.c:480:13: error: redefinition of 'BalanceCarMain_ProcessInput'
  480 | static void BalanceCarMain_ProcessInput(balance_car_main_t *car)
      |             ^~~~~~~~~~~~~~~~~~~~~~~~~~~
../Core/Src/balance_car_system.c: In function 'process_control_loop':
../Core/Src/balance_car_system.c:396:13: warning: implicit declaration of function 'Motion_ProcessJoystickInput' [-Wimplicit-function-declaration]
  396 |             Motion_ProcessJoystickInput(&system->motion_controller, &system->joystick_data);
      |             ^~~~~~~~~~~~~~~~~~~~~~~~~~~
../Core/Src/balance_car_main.c:188:13: note: previous definition of 'BalanceCarMain_ProcessInput' with type 'void(balance_car_main_t *)'
  188 | static void BalanceCarMain_ProcessInput(balance_car_main_t *car)
      |             ^~~~~~~~~~~~~~~~~~~~~~~~~~~
../Core/Src/balance_car_system.c:397:55: warning: passing argument 2 of 'Motion_Update' from incompatible pointer type [-Wincompatible-pointer-types]
  397 |             Motion_Update(&system->motion_controller, &system->motion_command);
      |                                                       ^~~~~~~~~~~~~~~~~~~~~~~
      |                                                       |
      |                                                       motion_command_t *
In file included from ../Core/Inc/balance_car_system.h:28,
                 from ../Core/Src/balance_car_system.c:18:
../Core/Inc/motion_controller.h:138:49: note: expected 'joystick_data_t *' but argument is of type 'motion_command_t *'
  138 |                                joystick_data_t *joystick_data,
      |                                ~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~
../Core/Src/balance_car_main.c:498:13: error: redefinition of 'BalanceCarMain_SafetyCheck'
  498 | static void BalanceCarMain_SafetyCheck(balance_car_main_t *car)
      |             ^~~~~~~~~~~~~~~~~~~~~~~~~~
../Core/Src/balance_car_system.c:397:13: error: too few arguments to function 'Motion_Update'
  397 |             Motion_Update(&system->motion_controller, &system->motion_command);
      |             ^~~~~~~~~~~~~
../Core/Inc/motion_controller.h:137:19: note: declared here
  137 | HAL_StatusTypeDef Motion_Update(motion_controller_t *controller,
      |                   ^~~~~~~~~~~~~
../Core/Src/balance_car_main.c:196:13: note: previous definition of 'BalanceCarMain_SafetyCheck' with type 'void(balance_car_main_t *)'
  196 | static void BalanceCarMain_SafetyCheck(balance_car_main_t *car)
      |             ^~~~~~~~~~~~~~~~~~~~~~~~~~
../Core/Src/balance_car_system.c:409:55: warning: passing argument 2 of 'Motion_Update' from incompatible pointer type [-Wincompatible-pointer-types]
  409 |             Motion_Update(&system->motion_controller, &system->motion_command);
      |                                                       ^~~~~~~~~~~~~~~~~~~~~~~
      |                                                       |
      |                                                       motion_command_t *
../Core/Src/balance_car_main.c:498:13: warning: 'BalanceCarMain_SafetyCheck' defined but not used [-Wunused-function]
  498 | static void BalanceCarMain_SafetyCheck(balance_car_main_t *car)
      |             ^~~~~~~~~~~~~~~~~~~~~~~~~~
../Core/Inc/motion_controller.h:138:49: note: expected 'joystick_data_t *' but argument is of type 'motion_command_t *'
  138 |                                joystick_data_t *joystick_data,
      |                                ~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~
../Core/Src/balance_car_system.c:409:13: error: too few arguments to function 'Motion_Update'
  409 |             Motion_Update(&system->motion_controller, &system->motion_command);
      |             ^~~~~~~~~~~~~
../Core/Src/balance_car_main.c:480:13: warning: 'BalanceCarMain_ProcessInput' defined but not used [-Wunused-function]
  480 | static void BalanceCarMain_ProcessInput(balance_car_main_t *car)
      |             ^~~~~~~~~~~~~~~~~~~~~~~~~~~
../Core/Inc/motion_controller.h:137:19: note: declared here
  137 | HAL_StatusTypeDef Motion_Update(motion_controller_t *controller,
      |                   ^~~~~~~~~~~~~
../Core/Src/balance_car_main.c:467:13: warning: 'BalanceCarMain_UpdateDisplay' defined but not used [-Wunused-function]
  467 | static void BalanceCarMain_UpdateDisplay(balance_car_main_t *car)
      |             ^~~~~~~~~~~~~~~~~~~~~~~~~~~~
../Core/Src/balance_car_system.c: In function 'update_system_status':
../Core/Src/balance_car_system.c:465:62: error: 'motion_controller_t' has no member named 'mode'
  465 |     system->status.motion_active = (system->motion_controller.mode != MOTION_MODE_STOP);
      |                                                              ^
../Core/Src/balance_car_main.c:437:13: warning: 'BalanceCarMain_RunControlLoop' defined but not used [-Wunused-function]
  437 | static void BalanceCarMain_RunControlLoop(balance_car_main_t *car)
      |             ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~
../Core/Src/balance_car_system.c: In function 'handle_mode_transitions':
../Core/Src/balance_car_system.c:488:70: error: 'joystick_data_t' has no member named 'button_press_duration'
  488 |     if (system->joystick_data.button_pressed && system->joystick_data.button_press_duration > 1000) {
      |                                                                      ^
../Core/Src/balance_car_main.c:421:13: warning: 'BalanceCarMain_UpdateSensors' defined but not used [-Wunused-function]
  421 | static void BalanceCarMain_UpdateSensors(balance_car_main_t *car)
      |             ^~~~~~~~~~~~~~~~~~~~~~~~~~~~
../Core/Src/balance_car_main.c:403:29: warning: 'BalanceCarMain_InitControllers' defined but not used [-Wunused-function]
  403 | static balance_car_result_t BalanceCarMain_InitControllers(balance_car_main_t *car)
      |                             ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
../Core/Src/balance_car_main.c:372:29: warning: 'BalanceCarMain_InitHardware' defined but not used [-Wunused-function]
  372 | static balance_car_result_t BalanceCarMain_InitHardware(balance_car_main_t *car)
      |                             ^~~~~~~~~~~~~~~~~~~~~~~~~~~
make: *** [Core/Src/subdir.mk:79: Core/Src/balance_car_main.o] Error 1
make: *** [Core/Src/subdir.mk:79: Core/Src/balance_car_system.o] Error 1
../Core/Src/balance_car_tuning.c: In function 'BalanceCarTuner_SetMotionParams':
../Core/Src/balance_car_tuning.c:203:5: warning: implicit declaration of function 'Motion_SetMaxTiltAngle' [-Wimplicit-function-declaration]
  203 |     Motion_SetMaxTiltAngle(&tuner->system->motion_controller, motion_params->max_tilt_angle);
      |     ^~~~~~~~~~~~~~~~~~~~~~
../Core/Src/balance_car_tuning.c:204:5: warning: implicit declaration of function 'Motion_SetMaxTurnRate'; did you mean 'Motion_CalculateTurnRate'? [-Wimplicit-function-declaration]
  204 |     Motion_SetMaxTurnRate(&tuner->system->motion_controller, motion_params->max_turn_rate);
      |     ^~~~~~~~~~~~~~~~~~~~~
      |     Motion_CalculateTurnRate
../Core/Src/balance_car_tuning.c:205:5: warning: implicit declaration of function 'Motion_SetAccelerationLimit' [-Wimplicit-function-declaration]
  205 |     Motion_SetAccelerationLimit(&tuner->system->motion_controller, motion_params->acceleration_limit);
      |     ^~~~~~~~~~~~~~~~~~~~~~~~~~~
../Core/Src/balance_car_tuning.c:206:5: warning: implicit declaration of function 'Motion_SetResponseSpeed'; did you mean 'Motion_SetCruiseSpeed'? [-Wimplicit-function-declaration]
  206 |     Motion_SetResponseSpeed(&tuner->system->motion_controller, motion_params->response_speed);
      |     ^~~~~~~~~~~~~~~~~~~~~~~
      |     Motion_SetCruiseSpeed
"make -j8 all" terminated with exit code 2. Build might be incomplete.

21:56:37 Build Failed. 95 errors, 23 warnings. (took 2s.227ms)

