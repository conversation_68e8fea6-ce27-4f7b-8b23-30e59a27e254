22:13:40 **** Incremental Build of configuration Debug for project F411CEU6_MPU6050_KML ****
make -j8 all 
arm-none-eabi-gcc "../Core/Src/auto_follow.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Core/Src/auto_follow.d" -MT"Core/Src/auto_follow.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Core/Src/auto_follow.o"
arm-none-eabi-gcc "../Core/Src/balance_car_advanced.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Core/Src/balance_car_advanced.d" -MT"Core/Src/balance_car_advanced.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Core/Src/balance_car_advanced.o"
arm-none-eabi-gcc "../Core/Src/balance_car_demo.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Core/Src/balance_car_demo.d" -MT"Core/Src/balance_car_demo.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Core/Src/balance_car_demo.o"
arm-none-eabi-gcc "../Core/Src/balance_car_main.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Core/Src/balance_car_main.d" -MT"Core/Src/balance_car_main.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Core/Src/balance_car_main.o"
arm-none-eabi-gcc "../Core/Src/balance_car_system.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Core/Src/balance_car_system.d" -MT"Core/Src/balance_car_system.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Core/Src/balance_car_system.o"
arm-none-eabi-gcc "../Core/Src/balance_controller.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Core/Src/balance_controller.d" -MT"Core/Src/balance_controller.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Core/Src/balance_controller.o"
arm-none-eabi-gcc "../Core/Src/error_handler.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Core/Src/error_handler.d" -MT"Core/Src/error_handler.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Core/Src/error_handler.o"
arm-none-eabi-gcc "../Core/Src/joystick_driver.c" -mcpu=cortex-m4 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F411xE -c -I../Core/Inc -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/Drivers/ssd1306" -I"C:/Users/<USER>/Rono_Workspace/F411CEU6_MPU6050_KML/KalmanFilter" -I../Drivers/STM32F4xx_HAL_Driver/Inc -I../Drivers/STM32F4xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I../Drivers/CMSIS/Include -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"Core/Src/joystick_driver.d" -MT"Core/Src/joystick_driver.o" --specs=nano.specs -mfpu=fpv4-sp-d16 -mfloat-abi=hard -mthumb -o "Core/Src/joystick_driver.o"
In file included from ../Core/Src/auto_follow.c:13:
../Core/Inc/balance_car_main.h:147:5: error: unknown type name 'joystick_driver_t'
  147 |     joystick_driver_t joystick;
      |     ^~~~~~~~~~~~~~~~~
In file included from ../Core/Src/balance_car_main.c:26:
../Core/Inc/balance_car_main.h:147:5: error: unknown type name 'joystick_driver_t'
  147 |     joystick_driver_t joystick;
      |     ^~~~~~~~~~~~~~~~~
../Core/Src/balance_car_advanced.c: In function 'update_path_tracking':
../Core/Src/balance_car_advanced.c:389:57: warning: passing argument 2 of 'Motion_Update' from incompatible pointer type [-Wincompatible-pointer-types]
  389 |     Motion_Update(&advanced->system->motion_controller, &motion_cmd);
      |                                                         ^~~~~~~~~~~
      |                                                         |
      |                                                         motion_command_t *
In file included from ../Core/Inc/balance_car_system.h:28,
                 from ../Core/Inc/balance_car_advanced.h:22,
                 from ../Core/Src/balance_car_advanced.c:10:
../Core/Inc/motion_controller.h:138:49: note: expected 'joystick_data_t *' but argument is of type 'motion_command_t *'
  138 |                                joystick_data_t *joystick_data,
      |                                ~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~
../Core/Src/balance_car_advanced.c:389:5: error: too few arguments to function 'Motion_Update'
  389 |     Motion_Update(&advanced->system->motion_controller, &motion_cmd);
      |     ^~~~~~~~~~~~~
../Core/Inc/motion_controller.h:137:19: note: declared here
  137 | HAL_StatusTypeDef Motion_Update(motion_controller_t *controller,
      |                   ^~~~~~~~~~~~~
../Core/Src/balance_car_advanced.c: In function 'update_performance_metrics':
../Core/Src/balance_car_advanced.c:564:28: warning: implicit declaration of function 'abs' [-Wimplicit-function-declaration]
  564 |     int16_t motor_output = abs(advanced->system->status.left_motor_output) +
      |                            ^~~
../Core/Src/balance_car_advanced.c:14:1: note: include '<stdlib.h>' or provide a declaration of 'abs'
   13 | #include <stdio.h>
  +++ |+#include <stdlib.h>
   14 | 
../Core/Src/balance_car_advanced.c: In function 'execute_remote_command':
../Core/Src/balance_car_advanced.c:584:65: warning: passing argument 2 of 'Motion_Update' from incompatible pointer type [-Wincompatible-pointer-types]
  584 |             Motion_Update(&advanced->system->motion_controller, &motion_cmd);
      |                                                                 ^~~~~~~~~~~
      |                                                                 |
      |                                                                 motion_command_t *
../Core/Inc/motion_controller.h:138:49: note: expected 'joystick_data_t *' but argument is of type 'motion_command_t *'
  138 |                                joystick_data_t *joystick_data,
      |                                ~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~
../Core/Src/balance_car_advanced.c:584:13: error: too few arguments to function 'Motion_Update'
  584 |             Motion_Update(&advanced->system->motion_controller, &motion_cmd);
      |             ^~~~~~~~~~~~~
../Core/Inc/motion_controller.h:137:19: note: declared here
  137 | HAL_StatusTypeDef Motion_Update(motion_controller_t *controller,
      |                   ^~~~~~~~~~~~~
../Core/Src/balance_car_advanced.c:590:65: warning: passing argument 2 of 'Motion_Update' from incompatible pointer type [-Wincompatible-pointer-types]
  590 |             Motion_Update(&advanced->system->motion_controller, &motion_cmd);
      |                                                                 ^~~~~~~~~~~
      |                                                                 |
      |                                                                 motion_command_t *
../Core/Inc/motion_controller.h:138:49: note: expected 'joystick_data_t *' but argument is of type 'motion_command_t *'
  138 |                                joystick_data_t *joystick_data,
      |                                ~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~
../Core/Src/auto_follow.c: In function 'AutoFollow_GetMotionOutput':
../Core/Src/balance_car_advanced.c:590:13: error: too few arguments to function 'Motion_Update'
  590 |             Motion_Update(&advanced->system->motion_controller, &motion_cmd);
      |             ^~~~~~~~~~~~~
../Core/Inc/motion_controller.h:137:19: note: declared here
  137 | HAL_StatusTypeDef Motion_Update(motion_controller_t *controller,
      |                   ^~~~~~~~~~~~~
../Core/Src/balance_car_advanced.c:596:65: warning: passing argument 2 of 'Motion_Update' from incompatible pointer type [-Wincompatible-pointer-types]
  596 |             Motion_Update(&advanced->system->motion_controller, &motion_cmd);
      |                                                                 ^~~~~~~~~~~
      |                                                                 |
      |                                                                 motion_command_t *
../Core/Inc/motion_controller.h:138:49: note: expected 'joystick_data_t *' but argument is of type 'motion_command_t *'
  138 |                                joystick_data_t *joystick_data,
      |                                ~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~
../Core/Src/balance_car_advanced.c:596:13: error: too few arguments to function 'Motion_Update'
  596 |             Motion_Update(&advanced->system->motion_controller, &motion_cmd);
      |             ^~~~~~~~~~~~~
../Core/Inc/motion_controller.h:137:19: note: declared here
  137 | HAL_StatusTypeDef Motion_Update(motion_controller_t *controller,
      |                   ^~~~~~~~~~~~~
../Core/Src/auto_follow.c:205:15: error: incompatible types when assigning to type 'auto_follow_motion_t' from type 'motion_output_t'
  205 |     *motion = follow->motion_output;
      |               ^~~~~~
../Core/Src/balance_car_advanced.c:602:65: warning: passing argument 2 of 'Motion_Update' from incompatible pointer type [-Wincompatible-pointer-types]
  602 |             Motion_Update(&advanced->system->motion_controller, &motion_cmd);
      |                                                                 ^~~~~~~~~~~
      |                                                                 |
      |                                                                 motion_command_t *
../Core/Inc/motion_controller.h:138:49: note: expected 'joystick_data_t *' but argument is of type 'motion_command_t *'
  138 |                                joystick_data_t *joystick_data,
      |                                ~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~
../Core/Src/balance_car_advanced.c:602:13: error: too few arguments to function 'Motion_Update'
  602 |             Motion_Update(&advanced->system->motion_controller, &motion_cmd);
      |             ^~~~~~~~~~~~~
../Core/Inc/motion_controller.h:137:19: note: declared here
  137 | HAL_StatusTypeDef Motion_Update(motion_controller_t *controller,
      |                   ^~~~~~~~~~~~~
make: *** [Core/Src/subdir.mk:79: Core/Src/auto_follow.o] Error 1
make: *** Waiting for unfinished jobs....
../Core/Src/balance_car_main.c: In function 'BalanceCarMain_InitHardware':
make: *** [Core/Src/subdir.mk:79: Core/Src/balance_car_advanced.o] Error 1
../Core/Src/balance_car_main.c:378:21: warning: unused variable 'odrive_cfg' [-Wunused-variable]
  378 |     odrive_config_t odrive_cfg = {
      |                     ^~~~~~~~~~
../Core/Src/balance_car_system.c: In function 'update_sensor_data':
../Core/Src/balance_car_system.c:345:24: error: 'mpu6050_data_t' has no member named 'yaw'
  345 |     system->sensor_data.yaw = 0.0f;
      |                        ^
../Core/Src/balance_car_system.c: In function 'update_joystick_data':
../Core/Src/balance_car_system.c:370:54: error: 'joystick_data_t' has no member named 'x_raw'
  370 |     system->status.joystick_x = system->joystick_data.x_raw;
      |                                                      ^
../Core/Src/balance_car_system.c:371:54: error: 'joystick_data_t' has no member named 'y_raw'
  371 |     system->status.joystick_y = system->joystick_data.y_raw;
      |                                                      ^
../Core/Src/balance_car_demo.c: In function 'BalanceCarDemo_Init':
../Core/Src/balance_car_demo.c:54:5: error: unknown type name 'balance_car_config_t'; did you mean 'balance_car_tuner_t'?
   54 |     balance_car_config_t system_config;
      |     ^~~~~~~~~~~~~~~~~~~~
      |     balance_car_tuner_t
make: *** [Core/Src/subdir.mk:79: Core/Src/balance_car_main.o] Error 1
../Core/Src/joystick_driver.c: In function 'Joystick_GetDirection':
../Core/Src/joystick_driver.c:150:9: warning: implicit declaration of function 'abs' [-Wimplicit-function-declaration]
  150 |     if (abs(x) < threshold && abs(y) < threshold) {
      |         ^~~
../Core/Src/joystick_driver.c:21:1: note: include '<stdlib.h>' or provide a declaration of 'abs'
   20 | #include <string.h>
  +++ |+#include <stdlib.h>
   21 | 
../Core/Src/joystick_driver.c: In function 'Joystick_SelfTest':
../Core/Src/joystick_driver.c:242:13: warning: unused variable 'initial_button' [-Wunused-variable]
  242 |     uint8_t initial_button = test_data.button_pressed;
      |             ^~~~~~~~~~~~~~
../Core/Src/joystick_driver.c: In function 'Joystick_GetStatusInfo':
../Core/Src/joystick_driver.c:422:5: warning: implicit declaration of function 'snprintf' [-Wimplicit-function-declaration]
  422 |     snprintf(info_str, max_len,
      |     ^~~~~~~~
../Core/Src/joystick_driver.c:21:1: note: include '<stdio.h>' or provide a declaration of 'snprintf'
   20 | #include <string.h>
  +++ |+#include <stdio.h>
   21 | 
../Core/Src/joystick_driver.c:422:5: warning: incompatible implicit declaration of built-in function 'snprintf' [-Wbuiltin-declaration-mismatch]
  422 |     snprintf(info_str, max_len,
      |     ^~~~~~~~
../Core/Src/joystick_driver.c:422:5: note: include '<stdio.h>' or provide a declaration of 'snprintf'
../Core/Src/balance_car_demo.c:55:5: warning: implicit declaration of function 'BalanceCarSystem_GetDefaultConfig'; did you mean 'BalanceCarDemo_GetDefaultConfig'? [-Wimplicit-function-declaration]
   55 |     BalanceCarSystem_GetDefaultConfig(&system_config);
      |     ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
      |     BalanceCarDemo_GetDefaultConfig
../Core/Src/balance_car_demo.c:57:46: warning: passing argument 2 of 'BalanceCarSystem_Init' from incompatible pointer type [-Wincompatible-pointer-types]
   57 |     if (BalanceCarSystem_Init(&demo->system, &system_config) != HAL_OK) {
      |                                              ^~~~~~~~~~~~~~
      |                                              |
      |                                              int *
In file included from ../Core/Inc/balance_car_demo.h:22,
                 from ../Core/Src/balance_car_demo.c:10:
../Core/Inc/balance_car_system.h:160:88: note: expected 'system_config_t *' but argument is of type 'int *'
  160 | HAL_StatusTypeDef BalanceCarSystem_Init(balance_car_system_t *system, system_config_t *config);
      |                                                                       ~~~~~~~~~~~~~~~~~^~~~~~
../Core/Src/balance_car_demo.c: In function 'BalanceCarDemo_GenerateReport':
../Core/Src/balance_car_demo.c:394:29: error: 'system_status_t' has no member named 'system_mode'
  394 |         (demo->system.status.system_mode == SYSTEM_MODE_BALANCE_ONLY) ? "平衡中" : "其他",
      |                             ^
../Core/Src/balance_car_demo.c: In function 'display_sensor_data':
../Core/Src/balance_car_demo.c:652:31: error: 'system_status_t' has no member named 'temperature'
  652 |            demo->system.status.temperature);
      |                               ^
../Core/Src/balance_car_system.c: In function 'process_control_loop':
../Core/Src/balance_car_system.c:396:13: warning: implicit declaration of function 'Motion_ProcessJoystickInput' [-Wimplicit-function-declaration]
  396 |             Motion_ProcessJoystickInput(&system->motion_controller, &system->joystick_data);
      |             ^~~~~~~~~~~~~~~~~~~~~~~~~~~
../Core/Src/balance_car_system.c:397:55: warning: passing argument 2 of 'Motion_Update' from incompatible pointer type [-Wincompatible-pointer-types]
  397 |             Motion_Update(&system->motion_controller, &system->motion_command);
      |                                                       ^~~~~~~~~~~~~~~~~~~~~~~
      |                                                       |
      |                                                       motion_command_t *
make: *** [Core/Src/subdir.mk:79: Core/Src/balance_car_demo.o] Error 1
In file included from ../Core/Inc/balance_car_system.h:28,
                 from ../Core/Src/balance_car_system.c:18:
../Core/Inc/motion_controller.h:138:49: note: expected 'joystick_data_t *' but argument is of type 'motion_command_t *'
  138 |                                joystick_data_t *joystick_data,
      |                                ~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~
../Core/Src/balance_car_system.c:397:13: error: too few arguments to function 'Motion_Update'
  397 |             Motion_Update(&system->motion_controller, &system->motion_command);
      |             ^~~~~~~~~~~~~
../Core/Inc/motion_controller.h:137:19: note: declared here
  137 | HAL_StatusTypeDef Motion_Update(motion_controller_t *controller,
      |                   ^~~~~~~~~~~~~
../Core/Src/balance_car_system.c:409:55: warning: passing argument 2 of 'Motion_Update' from incompatible pointer type [-Wincompatible-pointer-types]
  409 |             Motion_Update(&system->motion_controller, &system->motion_command);
      |                                                       ^~~~~~~~~~~~~~~~~~~~~~~
      |                                                       |
      |                                                       motion_command_t *
../Core/Inc/motion_controller.h:138:49: note: expected 'joystick_data_t *' but argument is of type 'motion_command_t *'
  138 |                                joystick_data_t *joystick_data,
      |                                ~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~
../Core/Src/balance_car_system.c:409:13: error: too few arguments to function 'Motion_Update'
  409 |             Motion_Update(&system->motion_controller, &system->motion_command);
      |             ^~~~~~~~~~~~~
../Core/Inc/motion_controller.h:137:19: note: declared here
  137 | HAL_StatusTypeDef Motion_Update(motion_controller_t *controller,
      |                   ^~~~~~~~~~~~~
../Core/Src/balance_car_system.c: In function 'update_system_status':
../Core/Src/balance_car_system.c:465:62: error: 'motion_controller_t' has no member named 'mode'
  465 |     system->status.motion_active = (system->motion_controller.mode != MOTION_MODE_STOP);
      |                                                              ^
../Core/Src/balance_car_system.c: In function 'handle_mode_transitions':
../Core/Src/balance_car_system.c:488:70: error: 'joystick_data_t' has no member named 'button_press_duration'
  488 |     if (system->joystick_data.button_pressed && system->joystick_data.button_press_duration > 1000) {
      |                                                                      ^
make: *** [Core/Src/subdir.mk:79: Core/Src/balance_car_system.o] Error 1
"make -j8 all" terminated with exit code 2. Build might be incomplete.

22:13:41 Build Failed. 24 errors, 16 warnings. (took 605ms)

