# STM32F411 + MPU6050 两轮平衡车完整控制方案

## 📋 方案概述

本方案是一个基于STM32F411CEU6微控制器和MPU6050陀螺仪的完整两轮平衡车控制系统，集成了双控制架构、自动跟随、手机APP控制等先进功能。

### 🎯 核心特性

1. **双控制系统融合**
   - 平衡控制：基于MPU6050陀螺仪的实时平衡维持
   - 运动控制：支持摇杆、自动跟随、手机APP多种输入方式

2. **智能化功能**
   - 自动参数调优：无需手动调试PID参数
   - 自适应控制：根据环境变化自动调整控制策略
   - 自动跟随：支持超声波、摄像头、激光雷达等多种传感器

3. **手机APP控制**
   - 无线通信：通过UART/蓝牙与手机APP通信
   - 实时监控：查看平衡车状态、传感器数据
   - 远程控制：手机遥控平衡车运动

4. **安全保护机制**
   - 多层安全检查：角度限制、超时保护、紧急停止
   - 故障自恢复：系统异常时自动恢复到安全状态

## 🏗️ 系统架构

### 硬件架构

```
STM32F411CEU6 (主控制器)
├── MPU6050 (陀螺仪/加速度计) - I2C1 (PB6/PB7)
├── ODrive 3.6 (电机控制板) - UART2 (PA2/PA3)
├── SSD1306 OLED (显示屏) - I2C2 (PB10/PB3)
├── 摇杆模块 (ADC输入) - PA0/PA1/PA4
├── 超声波传感器 (自动跟随) - PA5/PA6
├── 蓝牙模块 (手机通信) - UART1 (PA9/PA10)
└── 电源管理模块
```

### 软件架构

```
应用层
├── 主控制系统 (balance_car_main)
├── 手机APP接口 (mobile_app)
└── 自动跟随模块 (auto_follow)

控制层
├── 平衡控制器 (balance_controller)
├── 运动控制器 (motion_controller)
└── 参数调优器 (balance_car_tuning)

驱动层
├── MPU6050驱动 (mpu6050)
├── ODrive驱动 (odrive_driver)
├── 摇杆驱动 (joystick_driver)
└── 显示驱动 (ssd1306)

HAL层
└── STM32 HAL库
```

## 🎮 运行模式

### 1. 初始化模式 (INIT)
- 系统启动和硬件初始化
- 传感器自检和校准
- 参数加载和配置验证

### 2. 待机模式 (STANDBY)
- 系统就绪，等待用户指令
- 传感器数据监控
- 显示系统状态信息

### 3. 纯平衡模式 (BALANCE_ONLY)
- 仅启用平衡控制
- 用于测试平衡算法性能
- 无运动控制输入

### 4. 手动控制模式 (MANUAL)
- 摇杆控制平衡车运动
- 前后运动：通过调整目标角度实现
- 左右转向：通过差速控制实现

### 5. 自动跟随模式 (AUTO_FOLLOW)
- 自动跟随目标物体
- 支持多种传感器：超声波、摄像头、激光雷达
- 智能避障和路径规划

### 6. APP控制模式 (APP_CONTROL)
- 手机APP远程控制
- 实时数据传输和状态监控
- 参数在线调整

### 7. 紧急停止模式 (EMERGENCY)
- 立即停止所有运动
- 保持基本平衡功能
- 等待手动复位

## 🔧 控制算法

### 三环PID控制系统

1. **角度环 (平衡控制)**
   ```c
   // 角度PID参数推荐值
   angle_kp = 80.0f;   // 比例系数
   angle_ki = 0.0f;    // 积分系数  
   angle_kd = 3.0f;    // 微分系数
   ```

2. **速度环 (前后运动)**
   ```c
   // 速度PID参数推荐值
   speed_kp = 0.8f;    // 比例系数
   speed_ki = 0.1f;    // 积分系数
   speed_kd = 0.0f;    // 微分系数
   ```

3. **转向环 (左右转向)**
   ```c
   // 转向PID参数推荐值
   turn_kp = 2.5f;     // 比例系数
   turn_ki = 0.0f;     // 积分系数
   turn_kd = 0.0f;     // 微分系数
   ```

### 控制融合策略

- **前后运动**：通过调整目标角度实现，目标角度 = 速度误差 × 速度PID输出
- **左右转向**：通过差速控制实现，左右电机输出差值 = 转向误差 × 转向PID输出
- **平衡维持**：始终保持角度环控制，确保平衡车不倾倒

## 📱 手机APP功能

### 通信协议

- **传输方式**：UART/蓝牙，115200波特率
- **数据格式**：自定义二进制协议，支持JSON格式
- **帧结构**：帧头(2字节) + 版本(1字节) + 命令(1字节) + 长度(2字节) + 数据 + 校验(1字节) + 帧尾(2字节)

### 主要功能

1. **实时监控**
   - 平衡角度、角速度显示
   - 电机输出、电池电压监控
   - 系统状态和错误信息

2. **远程控制**
   - 虚拟摇杆控制
   - 模式切换按钮
   - 紧急停止功能

3. **参数调整**
   - PID参数在线调整
   - 控制限制设置
   - 功能开关配置

4. **数据记录**
   - 传感器数据记录
   - 控制性能分析
   - 历史数据回放

## 🎯 自动跟随功能

### 支持的传感器类型

1. **超声波传感器**
   - 检测距离：5cm - 400cm
   - 精度：±3mm
   - 更新频率：20Hz

2. **摄像头模块**
   - 目标识别：基于颜色/形状识别
   - 视野角度：60°
   - 处理频率：10Hz

3. **激光雷达**
   - 扫描范围：360°
   - 检测距离：0.15m - 12m
   - 角度分辨率：1°

4. **蓝牙信标**
   - 基于RSSI信号强度定位
   - 检测距离：1m - 50m
   - 定位精度：±1m

### 跟随算法

1. **目标检测**
   - 多传感器数据融合
   - 目标置信度评估
   - 噪声滤波处理

2. **路径规划**
   - 前瞻控制算法
   - 动态避障
   - 平滑轨迹生成

3. **控制输出**
   - 距离控制：保持设定跟随距离
   - 角度控制：对准目标方向
   - 速度控制：根据目标运动调整

## ⚙️ 配置参数

### 系统配置
```c
// 控制参数
max_tilt_angle = 15.0f;         // 最大倾斜角度
max_speed = 50.0f;              // 最大速度 (cm/s)
max_turn_rate = 90.0f;          // 最大转向速率 (度/s)

// 安全参数
watchdog_timeout_ms = 1000;     // 看门狗超时
input_timeout_ms = 500;         // 输入超时
emergency_angle_limit = 25.0f;  // 紧急停止角度

// 跟随参数
target_distance = 50.0f;        // 目标跟随距离 (cm)
distance_tolerance = 10.0f;     // 距离容差 (cm)
angle_tolerance = 15.0f;        // 角度容差 (度)
```

### 硬件连接配置
```c
// ODrive 3.6 连接 (UART2)
STM32_PA2 (UART2_TX) → ODrive_GPIO1 (RX)
STM32_PA3 (UART2_RX) → ODrive_GPIO2 (TX)
GND → GND

// 完整引脚分配
MPU6050:     PB6(SCL), PB7(SDA) - I2C1
OLED显示:    PB10(SCL), PB3(SDA) - I2C2
ODrive:      PA2(TX), PA3(RX) - UART2
蓝牙模块:    PA9(TX), PA10(RX) - UART1
摇杆模块:    PA0(X轴), PA1(Y轴), PA4(按键)
超声波:      PA5(Trig), PA6(Echo)
```

### 通信配置
```c
// UART2配置 (ODrive通信)
baud_rate = 115200;             // 波特率
data_bits = 8;                  // 数据位
stop_bits = 1;                  // 停止位
parity = NONE;                  // 校验位

// UART1配置 (APP通信)
connection_timeout_ms = 5000;   // 连接超时
stream_interval_ms = 100;       // 数据流间隔
max_payload_size = 128;         // 最大载荷大小
```

## 🛡️ 安全机制

### 多层安全保护

1. **硬件安全**
   - 电源过压/欠压保护
   - 电机过流保护
   - 温度过热保护

2. **软件安全**
   - 角度限制保护
   - 输入超时保护
   - 通信中断保护

3. **算法安全**
   - PID输出限幅
   - 积分饱和保护
   - 微分冲击抑制

### 故障处理

1. **传感器故障**
   - 自动检测传感器异常
   - 切换到安全模式
   - 提示用户检查硬件

2. **通信故障**
   - 检测通信中断
   - 自动切换到本地控制
   - 保持基本功能运行

3. **控制故障**
   - 监控控制性能
   - 异常时自动调整参数
   - 必要时启动紧急停止

## 📊 性能指标

### 控制性能
- **平衡精度**：±2°以内
- **响应时间**：<1秒稳定时间
- **控制频率**：200Hz
- **最大倾斜角**：±15°运动控制

### 跟随性能
- **跟随精度**：±10cm位置容差
- **响应延迟**：<200ms
- **最大跟随速度**：50cm/s
- **目标丢失恢复**：<2秒

### 通信性能
- **数据传输率**：115200bps
- **延迟**：<50ms
- **丢包率**：<1%
- **连接稳定性**：>99%

## 🔄 开发和维护

### 代码结构特点
- **模块化设计**：清晰的模块分离，易于维护
- **接口标准化**：统一的函数接口和数据结构
- **配置参数化**：所有参数可配置，便于调试
- **错误处理完善**：全面的错误检测和处理机制

### 调试和测试
- **串口调试**：详细的调试信息输出
- **状态监控**：实时系统状态显示
- **性能分析**：控制性能评估和优化
- **自动测试**：内置自检和测试功能

### 扩展性
- **传感器扩展**：支持添加新的传感器类型
- **功能扩展**：模块化设计便于添加新功能
- **平台移植**：标准HAL接口，易于移植到其他平台

## 📞 技术支持

### 文档资源
- 详细的API文档和使用说明
- 硬件连接图和电路设计
- 调试指南和故障排除
- 参数调优指导

### 开发工具
- STM32CubeMX配置文件
- Keil/IAR工程模板
- 调试脚本和工具
- 测试用例和示例代码

---

**🎉 这是一个完整、先进、实用的两轮平衡车控制方案，集成了现代智能控制技术，为平衡车开发提供了完整的解决方案。**
