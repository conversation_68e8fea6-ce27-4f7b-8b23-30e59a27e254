# STM32F411 + MPU6050 平衡车项目文件清单

## 📋 项目概述

本文档列出了完整平衡车项目的所有文件，包括核心功能、新增功能和配置文件。

## 🎯 确定的技术方案

### 1. 电机控制方式
- **✅ 确定使用**: ODrive 3.6 + UART1控制 ⭐ 最终确定
- **❌ 不使用**: 传统PWM电机驱动
- **硬件连接**: PA9(TX) → ODrive GPIO1, PA10(RX) ← ODrive GPIO2

### 2. 系统架构
- **✅ 采用**: 简化主控制系统 (balance_car_main)
- **❌ 移除**: 复杂演示应用 (balance_car_demo)
- **新增功能**: 自动跟随 + 手机APP控制

### 3. 配置方式
- **✅ 需要**: STM32CubeMX图形化配置
- **配置文件**: 详见 `STM32CubeMX配置指南.md`

## 📁 核心文件结构

### 🎮 主控制系统 (核心)
```
Core/Inc/balance_car_main.h     ✅ 已完成 - 简化主控制系统头文件
Core/Src/balance_car_main.c     🔄 需创建 - 主控制系统实现
```

### 🔧 核心控制模块
```
Core/Inc/balance_controller.h   ✅ 已存在 - 平衡控制器
Core/Src/balance_controller.c   ✅ 已存在 - 平衡控制实现
Core/Inc/motion_controller.h    ✅ 已存在 - 运动控制器
Core/Src/motion_controller.c    ✅ 已存在 - 运动控制实现
Core/Inc/joystick_driver.h      ✅ 已存在 - 摇杆驱动
Core/Src/joystick_driver.c      ✅ 已存在 - 摇杆驱动实现
```

### 🤖 智能功能模块 (新增)
```
Core/Inc/auto_follow.h          ✅ 已完成 - 自动跟随功能头文件
Core/Src/auto_follow.c          🔄 需创建 - 自动跟随功能实现
Core/Inc/mobile_app.h           ✅ 已完成 - 手机APP接口头文件
Core/Src/mobile_app.c           🔄 需创建 - 手机APP接口实现
```

### 🔌 硬件驱动层
```
Core/Inc/mpu6050.h              ✅ 已存在 - MPU6050驱动
Core/Src/mpu6050.c              ✅ 已存在 - MPU6050实现
Core/Inc/odrive_driver.h        ✅ 已更新 - ODrive驱动 (UART2)
Core/Src/odrive_driver.c        ✅ 已更新 - ODrive实现 (UART2)
Core/Inc/ssd1306.h              ✅ 已存在 - OLED显示驱动
Core/Src/ssd1306.c              ✅ 已存在 - OLED显示实现
```

### 🛠️ 支持模块
```
Core/Inc/error_handler.h        ✅ 已存在 - 错误处理
Core/Src/error_handler.c        ✅ 已存在 - 错误处理实现
Core/Inc/app_config.h           ✅ 已存在 - 配置管理
```

### 🗑️ 移除的文件 (简化架构)
```
Core/Inc/balance_car_demo.h     ❌ 建议移除 - 复杂演示应用
Core/Src/balance_car_demo.c     ❌ 建议移除 - 复杂演示实现
Core/Inc/balance_car_tuning.h   ❌ 建议移除 - 复杂调优系统
Core/Src/balance_car_tuning.c   ❌ 建议移除 - 复杂调优实现
Core/Inc/balance_car_advanced.h ❌ 建议移除 - 高级功能
Core/Src/balance_car_advanced.c ❌ 建议移除 - 高级功能实现
```

## 📄 文档文件

### 📚 主要文档
```
README.md                       ✅ 已更新 - 项目主文档
平衡车控制方案完整文档.md        ✅ 已完成 - 完整技术方案
STM32CubeMX配置指南.md          ✅ 已完成 - 硬件配置指南
项目文件清单.md                 ✅ 当前文件 - 文件清单
```

### 📋 配置文件
```
Core/Src/main.c                 🔄 需更新 - 主函数 (用户已修改)
Core/Inc/main.h                 ✅ 已存在 - 主头文件
stm32f4xx_hal_conf.h           ✅ 已存在 - HAL配置
stm32f4xx_it.c                 ✅ 已存在 - 中断处理
```

## 🎯 下一步工作计划

### 1. 立即需要完成的文件
```
优先级1 (核心功能):
├── Core/Src/balance_car_main.c     - 主控制系统实现
├── Core/Src/auto_follow.c          - 自动跟随功能实现
└── Core/Src/mobile_app.c           - 手机APP接口实现

优先级2 (系统集成):
├── 更新 Core/Src/main.c            - 集成新的主控制系统
└── STM32CubeMX项目配置             - 按照配置指南设置
```

### 2. 可选的改进工作
```
优先级3 (优化):
├── 移除演示应用相关文件             - 简化项目结构
├── 创建测试用例                    - 验证功能正确性
└── 性能优化                       - 提高系统响应速度
```

## 🔧 技术规格统一

### 硬件接口规格
```
通信接口:
├── UART1: 115200-8-N-1 (蓝牙/手机APP)
├── UART2: 115200-8-N-1 (ODrive控制) ⭐ 主要
├── I2C1: 100kHz (MPU6050)
└── I2C2: 400kHz (OLED显示)

模拟接口:
├── ADC1_CH0: PA0 (摇杆X轴)
├── ADC1_CH1: PA1 (摇杆Y轴)
└── 12位分辨率, 连续转换模式

数字接口:
├── PA4: GPIO_Input (摇杆按键)
├── PA5: GPIO_Output (超声波Trig)
└── PA6: GPIO_Input (超声波Echo)
```

### 软件接口规格
```
控制频率:
├── 主控制循环: 200Hz (5ms)
├── 平衡控制: 200Hz
├── 运动控制: 100Hz
└── 显示更新: 10Hz

数据格式:
├── 角度数据: float (-180° ~ +180°)
├── 速度数据: int16_t (-1000 ~ +1000)
├── 电机输出: float (turns/s)
└── 通信协议: 自定义二进制帧
```

## ✅ 项目状态总结

### 已完成 ✅
- [x] 核心控制算法设计
- [x] ODrive驱动程序 (UART2)
- [x] 自动跟随功能设计
- [x] 手机APP接口设计
- [x] 简化主控制系统设计
- [x] 完整技术文档
- [x] STM32CubeMX配置指南

### 进行中 🔄
- [ ] 主控制系统实现 (balance_car_main.c)
- [ ] 自动跟随功能实现 (auto_follow.c)
- [ ] 手机APP接口实现 (mobile_app.c)

### 待开始 📋
- [ ] STM32CubeMX项目配置
- [ ] 系统集成测试
- [ ] 性能调优
- [ ] 用户手册编写

---

**🎉 项目架构已统一确定，技术方案已明确，可以开始具体的代码实现工作！**
